<?php

namespace app\company\controller;

use app\controller\CompanyBaseController;
use app\model\CsShop;
use app\model\CsShopAdmin;
use app\model\CsOrder;
use app\model\CsTable;
use app\service\ReportService;
use support\Request;
use support\Response;
use support\Log;

class CsShopController extends CompanyBaseController
{
    // 验证器
    protected $validateName = 'CsShopValidate';

    // 当前主模型
    protected $modelName = 'CsShop';

    /**
     * 获取门店详情（扩展版本，包含更多统计信息）
     * @param Request $request
     * @return Response
     */
    public function detail(Request $request): Response
    {
        try {
            $id = $request->get('id', 0);
            if (empty($id)) {
                return fail('参数错误');
            }

            $shop = CsShop::with(['csCompany'])
                ->where('company_id', $request->company_id)
                ->find($id);

            if (empty($shop)) {
                return fail('门店不存在');
            }

            // 获取门店统计数据
            $stats = $this->getShopStatistics($id);
            $shop->statistics = $stats;

            // 获取管理员信息
            $adminCount = CsShopAdmin::where('shop_id', $id)->count();
            $shop->admin_count = $adminCount;

            // 获取桌台信息
            $tableStats = CsTable::where('shop_id', $id)
                ->selectRaw('COUNT(*) as total_tables, SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as occupied_tables')
                ->first();
            $shop->table_stats = $tableStats;

            return success($shop);
        } catch (\Exception $e) {
            Log::error('获取门店详情失败: ' . $e->getMessage());
            return fail('获取门店详情失败');
        }
    }

    /**
     * 获取门店实时状态
     * @param Request $request
     * @return Response
     */
    public function status(Request $request): Response
    {
        try {
            $id = $request->get('id', 0);
            if (empty($id)) {
                return fail('参数错误');
            }

            $shop = CsShop::where('company_id', $request->company_id)->find($id);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            // 获取实时数据
            $realTimeData = $this->getRealTimeShopData($id);
            
            return success([
                'shop_info' => [
                    'id' => $shop->id,
                    'shop_name' => $shop->shop_name,
                    'status' => $shop->status,
                    'address' => $shop->address
                ],
                'real_time_data' => $realTimeData
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店状态失败: ' . $e->getMessage());
            return fail('获取门店状态失败');
        }
    }

    /**
     * 获取所有门店状态汇总
     * @param Request $request
     * @return Response
     */
    public function statusSummary(Request $request): Response
    {
        try {
            $shops = CsShop::where('company_id', $request->company_id)
                ->select(['id', 'shop_name', 'status', 'address'])
                ->get();

            $summary = [];
            foreach ($shops as $shop) {
                $realTimeData = $this->getRealTimeShopData($shop->id);
                $summary[] = [
                    'shop_info' => $shop,
                    'real_time_data' => $realTimeData
                ];
            }

            // 计算汇总统计
            $totalShops = count($shops);
            $activeShops = $shops->where('status', 1)->count();
            $inactiveShops = $totalShops - $activeShops;

            return success([
                'shops' => $summary,
                'summary_stats' => [
                    'total_shops' => $totalShops,
                    'active_shops' => $activeShops,
                    'inactive_shops' => $inactiveShops
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店状态汇总失败: ' . $e->getMessage());
            return fail('获取门店状态汇总失败');
        }
    }

    /**
     * 更新门店营业状态
     * @param Request $request
     * @return Response
     */
    public function updateStatus(Request $request): Response
    {
        try {
            $id = $request->post('id', 0);
            $status = $request->post('status', 1);
            
            if (empty($id)) {
                return fail('参数错误');
            }

            $shop = CsShop::where('company_id', $request->company_id)->find($id);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            $shop->status = $status;
            $shop->save();

            Log::info("门店状态更新成功，门店ID: {$id}, 新状态: {$status}");
            return success('门店状态更新成功');
        } catch (\Exception $e) {
            Log::error('更新门店状态失败: ' . $e->getMessage());
            return fail('更新门店状态失败');
        }
    }

    /**
     * 为门店创建管理员账号
     * @param Request $request
     * @return Response
     */
    public function createAdmin(Request $request): Response
    {
        try {
            $shopId = $request->post('shop_id', 0);
            $username = $request->post('username', '');
            $password = $request->post('password', '');
            $mobile = $request->post('mobile', '');
            $nickname = $request->post('nickname', '');

            if (empty($shopId) || empty($username) || empty($password)) {
                return fail('参数错误');
            }

            // 验证门店是否存在且属于当前公司
            $shop = CsShop::where('company_id', $request->company_id)->find($shopId);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            // 检查用户名是否已存在
            $existingAdmin = CsShopAdmin::where('username', $username)->first();
            if ($existingAdmin) {
                return fail('用户名已存在');
            }

            // 创建管理员账号
            $admin = new CsShopAdmin();
            $admin->shop_id = $shopId;
            $admin->username = $username;
            $admin->password = password_hash($password, PASSWORD_DEFAULT);
            $admin->mobile = $mobile;
            $admin->nickname = $nickname;
            $admin->status = 1;
            $admin->save();

            Log::info("门店管理员创建成功，门店ID: {$shopId}, 用户名: {$username}");
            return success('管理员账号创建成功');
        } catch (\Exception $e) {
            Log::error('创建门店管理员失败: ' . $e->getMessage());
            return fail('创建管理员账号失败');
        }
    }

    /**
     * 重置门店管理员密码
     * @param Request $request
     * @return Response
     */
    public function resetPassword(Request $request): Response
    {
        try {
            $adminId = $request->post('admin_id', 0);
            $newPassword = $request->post('new_password', '');

            if (empty($adminId) || empty($newPassword)) {
                return fail('参数错误');
            }

            // 验证管理员是否存在且属于当前公司的门店
            $admin = CsShopAdmin::whereHas('csShop', function($query) use ($request) {
                $query->where('company_id', $request->company_id);
            })->find($adminId);

            if (empty($admin)) {
                return fail('管理员不存在');
            }

            $admin->password = password_hash($newPassword, PASSWORD_DEFAULT);
            $admin->save();

            Log::info("门店管理员密码重置成功，管理员ID: {$adminId}");
            return success('密码重置成功');
        } catch (\Exception $e) {
            Log::error('重置管理员密码失败: ' . $e->getMessage());
            return fail('密码重置失败');
        }
    }

    /**
     * 获取门店管理员列表
     * @param Request $request
     * @return Response
     */
    public function adminList(Request $request): Response
    {
        try {
            $shopId = $request->get('shop_id', 0);
            
            if (empty($shopId)) {
                return fail('参数错误');
            }

            // 验证门店是否存在且属于当前公司
            $shop = CsShop::where('company_id', $request->company_id)->find($shopId);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            $admins = CsShopAdmin::where('shop_id', $shopId)
                ->select(['id', 'username', 'nickname', 'mobile', 'status', 'created_at', 'last_login_at'])
                ->get();

            return success($admins);
        } catch (\Exception $e) {
            Log::error('获取门店管理员列表失败: ' . $e->getMessage());
            return fail('获取管理员列表失败');
        }
    }

    /**
     * 获取门店统计数据
     * @param int $shopId
     * @return array
     */
    private function getShopStatistics(int $shopId): array
    {
        try {
            // 今日订单统计
            $todayOrders = CsOrder::where('shop_id', $shopId)
                ->whereDate('created_at', date('Y-m-d'))
                ->selectRaw('COUNT(*) as total_orders, SUM(total_amount) as total_revenue')
                ->first();

            // 本月订单统计
            $monthOrders = CsOrder::where('shop_id', $shopId)
                ->whereMonth('created_at', date('m'))
                ->whereYear('created_at', date('Y'))
                ->selectRaw('COUNT(*) as total_orders, SUM(total_amount) as total_revenue')
                ->first();

            return [
                'today' => [
                    'orders' => $todayOrders->total_orders ?? 0,
                    'revenue' => $todayOrders->total_revenue ?? 0
                ],
                'month' => [
                    'orders' => $monthOrders->total_orders ?? 0,
                    'revenue' => $monthOrders->total_revenue ?? 0
                ]
            ];
        } catch (\Exception $e) {
            Log::error('获取门店统计数据失败: ' . $e->getMessage());
            return [
                'today' => ['orders' => 0, 'revenue' => 0],
                'month' => ['orders' => 0, 'revenue' => 0]
            ];
        }
    }

    /**
     * 获取门店实时数据
     * @param int $shopId
     * @return array
     */
    private function getRealTimeShopData(int $shopId): array
    {
        try {
            // 实时桌台状态
            $tableStats = CsTable::where('shop_id', $shopId)
                ->selectRaw('
                    COUNT(*) as total_tables,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as occupied_tables,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as available_tables
                ')
                ->first();

            // 今日实时数据
            $todayStats = CsOrder::where('shop_id', $shopId)
                ->whereDate('created_at', date('Y-m-d'))
                ->selectRaw('
                    COUNT(*) as today_orders,
                    SUM(total_amount) as today_revenue,
                    COUNT(DISTINCT user_id) as today_customers
                ')
                ->first();

            return [
                'table_stats' => [
                    'total' => $tableStats->total_tables ?? 0,
                    'occupied' => $tableStats->occupied_tables ?? 0,
                    'available' => $tableStats->available_tables ?? 0
                ],
                'today_stats' => [
                    'orders' => $todayStats->today_orders ?? 0,
                    'revenue' => $todayStats->today_revenue ?? 0,
                    'customers' => $todayStats->today_customers ?? 0
                ]
            ];
        } catch (\Exception $e) {
            Log::error('获取门店实时数据失败: ' . $e->getMessage());
            return [
                'table_stats' => ['total' => 0, 'occupied' => 0, 'available' => 0],
                'today_stats' => ['orders' => 0, 'revenue' => 0, 'customers' => 0]
            ];
        }
    }
}
