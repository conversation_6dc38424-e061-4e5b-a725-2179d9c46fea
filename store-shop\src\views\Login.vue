<template>
  <div class="login_wrap">
      <div class="fixed_index">
          <el-icon :size="20"><HomeFilled /></el-icon>
          九软官网
      </div>
      <div class="version">当前版本：V1.0.0</div>
      <div class="login_main">
          <div class="login_header">
              <img src="../assets/images/logo.png" alt="">
              <b>九软门店收银管理系统</b>
          </div>
          <form @submit.prevent="handleLogin" autocomplete="off" class="login_mid">
                <div class="login_li">
                    <b>账号：</b>
                    <el-input
                        v-model="mobile"
                        placeholder="请输入账号"
                        size="large"
                        class="login-input"
                        maxlength="20"
                        clearable
                        autocomplete="username"
                    >
                    </el-input>
                </div>
                <div class="login_li">
                    <b>密码：</b>
                    <el-input
                        v-model="password"
                        placeholder="请输入密码"
                        type="password"
                        size="large"
                        class="login-input"
                        show-password
                        autocomplete="current-password"
                    >
                    </el-input>
                </div>
                <div class="login_li">
                  <slider-captcha
                      ref="sliderCaptchaRef"
                      @success="onSliderSuccess"
                      @fail="onSliderFail"
                      :width="isMobile ? 280 : 340"
                      :successText="'验证通过'"
                      :slideText="'请按住滑块，拖动到最右侧'"
                  />
                </div>
              <!-- 登录按钮 -->
                  <el-button class="login_btn"
                      type="primary"
                      size="large"
                      :disabled="!sliderPassed || loading"
                      :loading="loading"
                      native-type="submit"
                      round
                  >登录</el-button>
          </form>
          <div class="login_footer">
              <div class="login_footer_li" @click="goToRegister">没有账号，去注册？</div>
              <div class="login_footer_li" @click="ewm_flag = true">
                  <el-icon :size="18"><Iphone /></el-icon>
                  九软员工端
              </div>
          </div>
          <!---------------九软商家端的弹框----------------->
          <div class="alert_bg" v-show="ewm_flag" @click="ewm_flag = false"></div>
          <div class="alert_down" v-show="ewm_flag">
              <div class="alert_down_header">
                  <b>九软员工端</b>
                  <el-icon :size="20" @click="ewm_flag = false"><Close /></el-icon>
              </div>
              <div class="ewm_ul">
                  <div class="ewm_li">
                      <b><img src="../assets/images/ewm.png" alt=""></b>
                      <span>安卓手机端下载</span>
                  </div>
                  <div class="ewm_li">
                      <b><img src="../assets/images/ewm.png" alt=""></b>
                      <span>微信小程序端</span>
                  </div>
              </div>
          </div>
      </div>
      <div class="copyright">Copyright © 2024 九软信息技术
          <a href="https://beian.miit.gov.cn" target="_blank" class="beian">浙ICP备16015472号-1</a>
      </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElInput, ElButton, ElMessage, ElIcon } from 'element-plus'
import { HomeFilled, Iphone, Close } from '@element-plus/icons-vue'
import SliderCaptcha from '../components/SliderCaptcha.vue'
import { login } from '../services/api'
import md5 from 'crypto-js/md5'
import { throttle } from '@/utils'
import { useRealtimeStore } from '@/stores/realtime'

const router = useRouter()
const mobile = ref('')
const password = ref('')
const sliderPassed = ref(false)
const loading = ref(false)
const isMobile = ref(false)
const ewm_flag = ref(false)

// 添加滑块验证组件的引用
const sliderCaptchaRef = ref<InstanceType<typeof SliderCaptcha> | null>(null)

function checkMobile() {
  isMobile.value = window.innerWidth < 800
}
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

function onSliderSuccess() {
  sliderPassed.value = true
}
function onSliderFail() {
  sliderPassed.value = false
}

// 创建一个使用节流的登录处理函数
const handleLogin = throttle(async () => {
  // 表单验证
  if (!mobile.value.trim()) {
    ElMessage.error('请输入账号')
    return
  }
  if (!password.value.trim()) {
    ElMessage.error('请输入密码')
    return
  }
  if (!sliderPassed.value) {
    ElMessage.warning('请完成滑块验证')
    return
  }

  if (loading.value) {
    return
  }

  loading.value = true
  try {
    // 对密码进行MD5加密
    const encryptedPassword = md5(password.value).toString()

    // 发起登录请求
    const response = await login({
      username: mobile.value.trim(),
      password: encryptedPassword
    })

    // 响应拦截器已处理code不为1的情况，这里是成功场景
    if (response && response.token) {
      // 清理之前的权限数据
      const { clearUserAuthData } = await import('@/utils/authUtils')
      await clearUserAuthData()
      
      // 保存token
      localStorage.setItem('token', response.token)

      // 保存用户信息
      const userInfo = {
        token: response.token,
        user_info: {
          nickname: response.nickname || '管理员',
          avatar: response.avatar || '',
          username: response.username || mobile.value.trim(),
          id: response.id || 0,
          shop_id: response.shop_id || 1
        }
      }
      localStorage.setItem('user_info', JSON.stringify(userInfo))

      // 初始化实时消息系统
      try {
        const realtimeStore = useRealtimeStore()
        
        // 设置未读消息数量
        if (response.unread_message_count !== undefined) {
          realtimeStore.setUnreadCount(response.unread_message_count)
        }
        
        // 初始化WebSocket连接（这里会使用正确的shop_id）
        await realtimeStore.initialize()
        console.log('登录成功，实时消息系统已初始化')
      } catch (error) {
        console.error('初始化实时消息系统失败:', error)
      }

      ElMessage.success('登录成功')
      
      // 使用 nextTick 确保状态更新完成后再跳转
      await nextTick()
      router.push('/')
    } else {
      ElMessage.error('登录失败，返回数据格式不正确')
      // 登录失败时重置滑块验证状态
      resetSliderCaptcha()
    }
  } catch (e: any) {
    // 错误已在响应拦截器中处理，这里不需要额外处理
    // console.error('登录失败', e)
    // 登录失败时重置滑块验证状态
    resetSliderCaptcha()
  } finally {
    loading.value = false
  }
}, 3000) // 设置3秒的节流时间

// 重置滑块验证状态
const resetSliderCaptcha = () => {
  if (sliderCaptchaRef.value) {
    sliderCaptchaRef.value.reset()
    sliderPassed.value = false
  }
}

// 跳转到注册页面
function goToRegister() {
  router.push('/register')
}
</script>

<style scoped>
.login_wrap{width:100%;height:100vh;background: url(../assets/images/login_bg.jpg) center center no-repeat;
    -moz-background-size:cover;-webkit-background-size: cover;background-size: cover;margin:0;}
.login_main{width:460px;height:430px;position:fixed;left:50%;margin-left:-230px;top:50%;margin-top:-215px;z-index:99;
    background:#fff;border-radius:6px;box-shadow: -5px 5px 5px -3px #aaa,5px 5px 5px -3px #aaa;overflow:hidden;}
.login_header{width:100%;height:100px;float:left;display:flex;justify-content:center;align-items: center;overflow:hidden;}
.login_header img{height:80px;width:80px;display:inline-block;}
.login_header b{width:auto;height:80px;font-size:28px;line-height:80px;display:inline-block;margin-left:10px;letter-spacing:1px;}
.login_mid{width:100%;min-height:260px;height:auto;float:left;padding:0 35px;overflow:hidden;}
.login_mid .login_li{width:100%;height:40px;float:left;margin-top:15px;}
.alert_tips{position:absolute;left:0;top:0;z-index:999;width:100%;padding:0;}
.login_li b{width:70px;height:40px;font-size:14px;color:#333;line-height:40px;display:inline-block;float:left;
    text-align:right;padding-right:5px;font-weight:normal;white-space:nowrap;}
:deep(.login_li .el-input){width:320px;height:40px;display:inline-block;float:left;}
:deep(.login_li .el-input .el-input__wrapper){width:100%;height:40px;background:#f0f5f3;display:block;padding:4px 15px;
    box-shadow: 0 0 0 1px #ddd inset;transition: 2s;}
:deep(.login_li.login_li_password .el-input .el-input__wrapper){padding:4px 50px 4px 15px;}
:deep(.login_li .el-input .el-input__wrapper:hover){box-shadow: 0 0 0 1px #2c9a4d inset;transition: 2s;}
:deep(.login_li .el-input .el-input__wrapper.is-focus){box-shadow: 0 0 0 1px #2c9a4d inset;transition: 2s;}
:deep(.login_li .el-input .el-input__wrapper .el-input__inner){color:#000;vertical-align:top;}
:deep(.login_li .el-input .el-input__wrapper .el-input__suffix){position:absolute;right:15px;top:0;z-index:2;width:20px;}
:deep(.login_li .el-input.input_yzm){width:190px;display:inline-block;float:left;}
:deep(.login_li .el-input .el-input__wrapper .el-input__inner){height:32px;}
.login_li .slider-captcha{width:100%;}
:deep(.login_li .slider-captcha .slider-bg){height:40px;line-height:40px;background:#f0f5f3;color:#666;}
:deep(.login_li .slider-captcha .slider-button){width:40px;height:40px;}
:deep(.login_li .slider-captcha .slider-mask){height:40px;}
.login_li .yzm_img{width:120px;height:44px;background:#f0f5f3;float:left;margin-left:10px;
    cursor:pointer;position:relative;border-radius:4px;overflow:hidden;}
.login_btn{width:100%;height:44px;background:#2c9a4d;font-size:18px;color:#fff;line-height:44px;text-align:center;
    float:left;margin-top:35px;border-radius:4px;letter-spacing:10px;font-weight:bold;text-indent:5px;
    cursor:pointer;opacity:0.8;}
.login_btn:hover{background:#c4272e;opacity:1.0;}
.to_forget{width:100%;height:44px;float:left;font-size:14px;color:#999;line-height:44px;text-align:center;
    display:block;cursor:pointer;}
.to_forget:hover{color:#c4272e;}
.login_footer{width:100%;height:50px;background:#e1efe9;float:left;margin-top:20px;}
.login_footer .login_footer_li{width:50%;height:50px;font-size:14px;color:#333;line-height:50px;
    display:inline-block;float:left;padding:0 35px;cursor:pointer;text-align:left;}
.login_footer .login_footer_li:nth-child(2){text-align:right;}
.login_footer .login_footer_li .el-icon{display:inline-block;color:#333;vertical-align:top;margin:16px 0;}
.login_footer .login_footer_li:hover{color:#c4272e;}
.login_footer .login_footer_li:hover .el-icon{color:#c4272e;}
.copyright{width:100%;height:20px;font-size:14px;color:#aaa;line-height:20px;text-align:center;padding:0 30px;
    overflow:hidden;position:fixed;left:0;bottom:10px;z-index:90;}
.copyright a{font-style:normal;cursor:pointer;margin-left:10px;color:#aaa;}
.copyright a:hover{opacity:1.0;color:#fff;}
@media (max-width:1200px){
    .login_main{width:360px;height:340px;margin-left:-180px;margin-top:-170px;}
    .login_header{height:80px;}
    .login_header img{height:60px;width:60px;display:inline-block;}
    .login_header b{height:60px;font-size:20px;line-height:60px;}
    .login_mid{min-height:200px;padding:0 20px;height:auto;}
    .login_mid .login_li{height:34px;margin-top:12px;}
    .login_li b{width:70px;height:34px;font-size:13px;line-height:34px;}
    :deep(.login_li .el-input){width:250px;height:34px;}
    :deep(.login_li .el-input .el-input__wrapper){height:34px;padding:2px 10px;font-size:13px;}
    :deep(.login_li.login_li_password .el-input .el-input__wrapper){padding:2px 40px 2px 10px;font-size:13px;}
    :deep(.login_li .el-input .el-input__wrapper .el-input__suffix){right:15px;width:20px;}
    :deep(.login_li .el-input .el-input__wrapper .el-input__inner){height:30px;}
    :deep(.login_li .slider-captcha .slider-bg){height:34px;line-height:34px;font-size:12px;}
    :deep(.login_li .slider-captcha .slider-button){width:34px;height:34px;}
    :deep(.login_li .slider-captcha .slider-mask){height:34px;}
    .login_li .el-input.input_yzm{width:150px;}
    .login_li .yzm_img{width:94px;height:34px;margin-left:6px;}
    .login_btn{height:34px;font-size:16px;line-height:34px;margin-top:25px;letter-spacing:10px;text-indent:5px;}
    .to_forget{height:34px;font-size:13px;line-height:34px;}
    .login_footer{height:40px;}
    .login_footer .login_footer_li{height:40px;font-size:13px;line-height:40px;padding:0 20px;}
    .login_footer .login_footer_li .el-icon{margin:11px 0;}
    .copyright{height:20px;font-size:13px;padding:0 20px;}
}
.version{width:160px;height:20px;font-size:14px;color:#fff;line-height:20px;display:block;position:fixed;
    right:30px;top:20px;z-index:91;opacity:0.8;text-align:right;}
.fixed_index{width:120px;height:40px;background:#214334;border-radius:4px;font-size:16px;color:#fff;vertical-align:top;display:flex;justify-content:center;
    line-height:40px;text-align:center;position:fixed;left:30px;top:20px;z-index:90;opacity:0.7;cursor:pointer;align-items:center;}
.fixed_index .el-icon{display:inline-flex;transition:2s;color:#fff;vertical-align:top;margin-right:5px;}
.fixed_index:hover{opacity:1.0;}
/*****弹框二维码样式*****/
.alert_bg{width:100%;height:100vh;background:#000;opacity:0.5;position:fixed;left:0;top:0;z-index:999;}
.alert_down{width:400px;height:280px;background:#fff;position:fixed;left:50%;margin-left:-200px;top:50%;margin-top:-140px;
    z-index:1000;border-radius:6px;}
.alert_down_header{width:100%;height:51px;float:left;display:flex;border-bottom:1px solid #eee;
    justify-content:space-between;align-items: center;padding:0 20px;}
.alert_down_header b{height:50px;font-size:18px;color:#333;line-height:50px;}
.alert_down_header .el-icon{color:#999;cursor:pointer;}
.alert_down_header .el-icon:hover{color:#c4272e;}
.ewm_ul{width:100%;height:229px;display:block;float:left;padding:30px 20px 0;}
.ewm_ul .ewm_li{width:50%;height:199px;display:inline-block;float:left;padding:0 20px;}
.ewm_ul .ewm_li b{width:140px;height:140px;display:block;float:left;}
.ewm_ul .ewm_li b img{width:100%;height:auto;display:block;}
.ewm_ul .ewm_li span{width:100%;height:30px;font-size:14px;color:#333;line-height:30px;display:block;
    float:left;text-align:center;}
@media (max-width:1200px){
    .version{width:160px;height:20px;font-size:13px;right:20px;top:15px;}
    .fixed_index{width:110px;height:36px;font-size:14px;line-height:36px;left:20px;top:15px;}
    /*****弹框二维码样式*****/
    .alert_down{width:340px;height:240px;margin-left:-170px;margin-top:-120px;}
    .alert_down_header{height:45px;padding:0 15px;}
    .alert_down_header b{height:44px;font-size:15px;line-height:44px;}
    .ewm_ul{height:195px;padding:20px 20px 0;}
    .ewm_ul .ewm_li{width:50%;height:175px;padding:0 15px;}
    .ewm_ul .ewm_li b{width:120px;height:120px;}
    .ewm_ul .ewm_li span{height:30px;font-size:13px;line-height:30px;}
}
</style>
