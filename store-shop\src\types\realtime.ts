// WebSocket连接状态
export enum WebSocketStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// WebSocket消息类型
export enum WebSocketMessageType {
  PING = 'ping',
  PONG = 'pong',
  BUSINESS = 'business',
  RESPONSE = 'response'
}

// 业务消息动作类型
export enum BusinessAction {
  JOIN_GROUP = 'join_group',
  CALL_SERVICE = 'call_service',
  SUBMIT_ORDER = 'submit_order',
  BOOK_TABLE = 'book_table',
  CANCEL_BOOK = 'cancel_book',
  SERVICE_RESPONSE = 'service_response',
  ORDER_STATUS_UPDATE = 'order_status_update',
  BOOKING_STATUS_UPDATE = 'booking_status_update'
}

// 实时消息类型（数据库存储）
export enum RealtimeMessageType {
  CALL_SERVICE = 'call_service',
  SUBMIT_ORDER = 'submit_order',
  BOOK_TABLE = 'book_table',
  CANCEL_BOOK = 'cancel_book',
  SERVICE_RESPONSE = 'service_response',
  ORDER_STATUS_UPDATE = 'order_status_update',
  BOOKING_STATUS_UPDATE = 'booking_status_update'
}

// WebSocket基础消息格式
export interface WebSocketMessage {
  type: WebSocketMessageType
  [key: string]: any
}

// 心跳消息
export interface HeartbeatMessage extends WebSocketMessage {
  type: WebSocketMessageType.PING | WebSocketMessageType.PONG
}

// 业务消息格式（发送到服务器）
export interface BusinessMessage extends WebSocketMessage {
  type: WebSocketMessageType.BUSINESS
  action: BusinessAction
  data: Record<string, any>
}

// 响应消息格式（服务器返回）
export interface ResponseMessage extends WebSocketMessage {
  type: WebSocketMessageType.RESPONSE | WebSocketMessageType.BUSINESS
  action?: BusinessAction
  status?: string
  message?: string
  data?: Record<string, any>
}

// 加入群组消息数据
export interface JoinGroupData {
  shop_id: string | number
}

// 呼叫服务消息数据
export interface CallServiceData {
  shop_id: string | number
  table_id: string | number
  table_name?: string
  user_id?: string | number
  message?: string
  timestamp?: string
}

// 订单提交消息数据
export interface SubmitOrderData {
  shop_id: string | number
  order_id: string | number
  order_sn?: string
  user_id?: string | number
  table_id?: string | number
  table_name?: string
  total_amount?: number
  timestamp?: string
}

// 桌台预订消息数据
export interface BookTableData {
  shop_id: string | number
  booking_id: string | number
  table_id: string | number
  table_name?: string
  user_id?: string | number
  booking_time?: string
  guest_count?: number
  timestamp?: string
}

// 取消预订消息数据
export interface CancelBookData {
  shop_id: string | number
  booking_id: string | number
  table_id?: string | number
  table_name?: string
  user_id?: string | number
  reason?: string
  timestamp?: string
}

// 实时消息（数据库存储格式）
export interface RealtimeMessage {
  id?: number
  shop_id: number
  message_type: RealtimeMessageType
  title: string
  content: string
  is_read: number  // 0-未读 1-已读
  extra_data?: Record<string, any>
  created_at?: string
  updated_at?: string
}

// 实时消息表单
export interface RealtimeMessageForm {
  shop_id: number
  message_type: RealtimeMessageType
  title: string
  content: string
  extra_data?: Record<string, any>
}

// 实时消息列表查询参数
export interface RealtimeMessageListParams {
  page?: number
  page_size?: number
  sort_field?: string
  sort?: string
  keyword?: string
  title?: string
  message_type?: RealtimeMessageType
  is_read?: number  // 0-未读 1-已读
  created_at?: string[]
}

// WebSocket连接配置
export interface WebSocketConfig {
  url: string
  heartbeatInterval: number  // 心跳间隔（毫秒）
  reconnectInterval: number  // 重连间隔（毫秒）
  maxReconnectAttempts: number  // 最大重连次数
  timeout: number  // 连接超时时间（毫秒）
}

// WebSocket连接状态
export interface WebSocketState {
  status: WebSocketStatus
  connected: boolean
  reconnectAttempts: number
  lastHeartbeat: number
  lastMessage: number
}

// 实时消息状态（前端状态管理）
export interface RealtimeState {
  // WebSocket连接状态
  websocketState: WebSocketState
  
  // 消息数据
  messages: RealtimeMessage[]
  unreadCount: number
  
  // 消息处理状态
  processingMessages: Set<number>
  duplicateCache: Set<string>  // 消息去重缓存
  
  // 离线处理
  lastOnlineTime: number
  pendingMessages: RealtimeMessage[]
}

// 消息处理器接口
export interface MessageHandler {
  (message: ResponseMessage): void
}

// 消息处理器映射
export interface MessageHandlers {
  [key: string]: MessageHandler
}

// WebSocket管理器接口
export interface WebSocketManager {
  // 连接管理
  connect(): Promise<void>
  disconnect(): void
  reconnect(): void
  
  // 消息发送
  send(message: WebSocketMessage): void
  sendHeartbeat(): void
  
  // 事件监听
  onMessage(handler: MessageHandler): void
  onConnect(handler: () => void): void
  onDisconnect(handler: () => void): void
  onError(handler: (error: Event) => void): void
  
  // 状态获取
  getStatus(): WebSocketStatus
  isConnected(): boolean
}

// 消息工具函数类型
export interface MessageUtils {
  // 消息格式化
  formatCallServiceMessage(data: CallServiceData): string
  formatSubmitOrderMessage(data: SubmitOrderData): string
  formatBookTableMessage(data: BookTableData): string
  formatCancelBookMessage(data: CancelBookData): string
  
  // 消息去重
  generateMessageKey(message: ResponseMessage): string
  isDuplicateMessage(key: string): boolean
  addToCache(key: string): void
  
  // 时间处理
  formatTimestamp(timestamp: string | number): string
  isExpiredMessage(timestamp: string | number, expireMinutes: number): boolean
}

// 通知选项
export interface NotificationOptions {
  title: string
  body: string
  icon?: string
  sound?: boolean
  persistent?: boolean
  onClick?: () => void
}

// 消息通知管理器
export interface NotificationManager {
  // 浏览器通知
  requestPermission(): Promise<NotificationPermission>
  showNotification(options: NotificationOptions): void
  
  // 应用内通知
  showToast(message: string, type?: 'success' | 'warning' | 'error' | 'info'): void
  
  // 声音提醒
  playSound(soundType?: 'default' | 'urgent'): void
}

// API响应格式
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 包含未读数量的操作响应格式（用于标记已读、删除等操作）
// 注意：响应拦截器会直接返回data部分，所以这里定义的是data的结构
export interface MessageOperationResponse {
  message: string
  unread_count: number
  [key: string]: any
}

// 标记已读操作响应
export interface MarkReadResponse extends MessageOperationResponse {
  updated_count?: number
}

// 删除操作响应
export interface DeleteResponse extends MessageOperationResponse {
  deleted_count?: number
}

// 分页响应格式
export interface PaginatedResponse<T = any> {
  itemList: T[]
  total: number
  current_page: number
  per_page: number
}

// 消息API服务接口
export interface MessageApiService {
  // 获取消息列表
  getMessageList(params: RealtimeMessageListParams): Promise<PaginatedResponse<RealtimeMessage>>
  
  // 标记消息已读
  markMessageRead(id: number): Promise<ApiResponse>
  
  // 批量标记已读
  batchMarkRead(ids: number[]): Promise<ApiResponse>
  
  // 获取未读消息数量
  getUnreadCount(): Promise<number>
  
  // 创建消息（内部使用）
  createMessage(data: RealtimeMessageForm): Promise<ApiResponse<RealtimeMessage>>
}