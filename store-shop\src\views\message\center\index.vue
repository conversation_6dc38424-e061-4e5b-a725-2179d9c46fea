<template>
  <div class="page-container">
    <div class="page-content">
      <!-- 搜索栏 -->
      <div class="search-bar" style="display: flex; align-items: center;">
        <el-form :model="searchForm" inline class="form-inline">
          <el-form-item label="消息类型">
            <el-select v-model="searchForm.message_type" placeholder="全部类型" clearable style="width: 140px;">
              <el-option 
                v-for="(label, value) in messageTypeOptions" 
                :key="value" 
                :label="label" 
                :value="value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.is_read" placeholder="全部状态" clearable style="width: 120px;">
              <el-option label="未读" :value="0" />
              <el-option label="已读" :value="1" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="标题搜索">
            <el-input 
              v-model="searchForm.title" 
              placeholder="请输入消息标题" 
              clearable 
              style="width: 200px;"
            />
          </el-form-item> -->
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 300px;"
            />
          </el-form-item>
        </el-form>
        <div class="search-btns" style="margin-left: 10px;">
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="resetSearch" :icon="RefreshRight">重置</el-button>
        </div>
      </div>
      
      <!-- 操作栏 -->
      <div class="action-bar">
        <div class="left-actions">
          <el-button 
            type="primary" 
            size="small" 
            @click="handleBatchMarkRead" 
            :disabled="selectedMessages.length === 0"
            :icon="Check"
          >
            批量已读
          </el-button>
          <el-button 
            type="success" 
            size="small" 
            @click="handleMarkAllRead"
            :disabled="unreadCount === 0"
            :icon="CircleCheck"
          >
            全部已读
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click="handleBatchDelete" 
            :disabled="selectedMessages.length === 0"
            :icon="Delete"
          >
            批量删除
          </el-button>
          <el-button 
            type="warning" 
            size="small" 
            @click="handleRefresh"
            :icon="Refresh"
          >
            刷新
          </el-button>
        </div>
        <div class="right-actions">
          <div class="stats-container">
            <div class="connection-status">
              <el-tag 
                :type="connectionStatusClass" 
                size="small"
                :icon="connectionStatusIcon"
              >
                {{ connectionStatusText }}
              </el-tag>
            </div>
            <p class="stats-text">
              共 <span class="count-num">{{ totalCount }}</span> 条消息，
              未读 <span class="count-num-warn">{{ unreadCount }}</span> 条
              <span v-if="urgentCount > 0" class="urgent-count">
                （紧急 <span class="count-num-danger">{{ urgentCount }}</span> 条）
              </span>
            </p>
          </div>
        </div>
      </div>
      
      <!-- 消息列表 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="messageList"
          class="custom-table"
          row-key="id"
          @selection-change="handleSelectionChange"
          :row-class-name="getRowClassName"
        >
          <el-table-column type="selection" width="55" />
          
          <!-- 消息标题 -->
          <el-table-column label="消息内容" min-width="300">
            <template #default="{ row }">
              <div class="message-content">
                <div class="message-title" :class="{ 'unread-title': row.is_read === 0 }">
                  <el-icon v-if="row.is_urgent" class="urgent-icon">
                    <Warning />
                  </el-icon>
                  <div v-html="row.title"></div>
                </div>
                <div class="message-text" v-html="row.content"></div>
                <div class="message-meta">
                  <!-- <span v-if="row.table_id" class="meta-item">
                    <el-icon><Calendar /></el-icon>
                    桌台{{ row.table_id }}号
                  </span>
                  <span v-if="row.order_id" class="meta-item">
                    <el-icon><DocumentCopy /></el-icon>
                    订单{{ row.order_id }}
                  </span> -->
                  <span class="meta-item">
                    {{ row.time_ago }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <!-- 消息类型 -->
          <el-table-column label="类型" width="120">
            <template #default="{ row }">
              <el-tag :type="row.type_class" size="small" :icon="getMessageIcon(row.message_type)">
                {{ row.type_text }}
              </el-tag>
            </template>
          </el-table-column>
          
          <!-- 优先级 -->
          <el-table-column label="优先级" width="100">
            <template #default="{ row }">
              <el-tag :type="getPriorityClass(row.priority)" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <!-- 状态 -->
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.read_status_class" size="small">
                {{ row.read_status_text }}
              </el-tag>
            </template>
          </el-table-column>
          
          <!-- 接收时间 -->
          <el-table-column prop="created_at" label="接收时间" width="200">
            <template #default="{ row }">
              {{ formatTime(row.created_at) }}
            </template>
          </el-table-column>
          
          <!-- 操作 -->
          <el-table-column label="操作" fixed="right">
            <template #default="{ row }">
              <div class="operation-btns">
                <el-button 
                  v-if="row.is_read === 0"
                  type="success" 
                  size="small" 
                  @click.stop="handleMarkRead(row.id)"
                  :icon="Check"
                >
                  已读
                </el-button>
                <el-button 
                  type="danger" 
                  size="small" 
                  @click.stop="handleDelete(row.id)"
                  :icon="Delete"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <Pagination
          :total="pagination.total"
          v-model:page="pagination.page"
          v-model:page-size="pagination.page_size"
          @pagination="fetchMessages"
        />
      </div>
    </div>

    <!-- 消息详情对话框 -->
    <RealtimeMessageDetail
      v-model:visible="detailDialogVisible"
      :message-data="currentMessage"
      @mark-read="handleMarkReadInDialog"
      @delete="handleDeleteInDialog"
    />
    
    <!-- 实时通知中心 -->
    <NotificationCenter ref="notificationCenter" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  RefreshRight,
  Check,
  CircleCheck,
  Delete,
  Refresh,
  Warning,
  Calendar,
  DocumentCopy,
  View,
  Connection,
  Remove
} from '@element-plus/icons-vue'
import Pagination from '@/components/common/Pagination.vue'
import RealtimeMessageDetail from '@/components/message/RealtimeMessageDetail.vue'
import NotificationCenter from '@/components/realtime/NotificationCenter.vue'
import { useRealtimeStore } from '@/stores/realtime'
import { 
  getRealtimeMessageList, 
  markMessageRead, 
  batchMarkMessagesRead, 
  markAllMessagesRead,
  deleteRealtimeMessage,
  batchDeleteMessages,
  getMessageTypeOptions,
  getTodayStatistics,
  getUnreadMessageCount
} from '@/services/messageApi'
import type { RealtimeMessage } from '@/types/realtime'
import '@/assets/styles/list-page.css'
import '@/assets/styles/component-common.css'

// 组件引用
const notificationCenter = ref()

// Store
const realtimeStore = useRealtimeStore()

// 状态
const loading = ref(false)
const messageList = ref<RealtimeMessage[]>([])
const selectedMessages = ref<RealtimeMessage[]>([])
const currentMessage = ref<RealtimeMessage | null>(null)
const detailDialogVisible = ref(false)
const messageTypeOptions = ref<Record<string, string>>({})

// 搜索表单
const searchForm = ref({
  message_type: '',
  is_read: undefined as number | undefined,
  title: '',
  dateRange: [] as string[]
})

// 分页
const pagination = ref({
  page: 1,
  page_size: 15,
  total: 0
})

// 统计信息
const totalCount = ref(0)
const unreadCount = ref(0)
const urgentCount = ref(0)

// 计算属性
const connectionStatusText = computed(() => {
  switch (realtimeStore.connectionStatus) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中'
    case 'disconnected':
      return '已断开'
    case 'reconnecting':
      return '重连中'
    case 'error':
      return '连接错误'
    default:
      return '未知'
  }
})

const connectionStatusClass = computed(() => {
  switch (realtimeStore.connectionStatus) {
    case 'connected':
      return 'success'
    case 'connecting':
    case 'reconnecting':
      return 'warning'
    case 'disconnected':
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
})

const connectionStatusIcon = computed(() => {
  return realtimeStore.isConnected ? Connection : Remove
})

// 方法
const fetchMessages = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.value.page,
      page_size: pagination.value.page_size,
      sort_field: 'created_at',
      sort: 'desc'
    } as any

    // 添加搜索条件
    if (searchForm.value.message_type) {
      params.message_type = searchForm.value.message_type
    }
    
    if (searchForm.value.is_read !== undefined) {
      params.is_read = searchForm.value.is_read
    }
    
    if (searchForm.value.title) {
      params.title = searchForm.value.title
    }
    
    if (searchForm.value.dateRange && searchForm.value.dateRange.length === 2) {
      params.created_at = searchForm.value.dateRange
    }

    const response = await getRealtimeMessageList(params)
    
    messageList.value = response.itemList || []
    pagination.value.total = response.total || 0
    totalCount.value = response.total || 0
    
    // 计算未读和紧急消息数量
    unreadCount.value = messageList.value.filter(msg => msg.is_read === 0).length
    urgentCount.value = messageList.value.filter(msg => msg.message_type === 'call_service' && msg.is_read === 0).length
    
  } catch (error) {
    console.error('获取消息列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.value.page = 1
  fetchMessages()
}

const resetSearch = () => {
  searchForm.value = {
    message_type: '',
    is_read: undefined,
    title: '',
    dateRange: []
  }
  handleSearch()
}

const handleSelectionChange = (selection: RealtimeMessage[]) => {
  selectedMessages.value = selection
}

const getRowClassName = ({ row }: { row: RealtimeMessage }) => {
  let className = ''
  if (row.is_read === 0) {
    className += 'unread-row '
  }
  if (row.message_type === 'call_service') {
    className += 'urgent-row '
  }
  return className.trim()
}

const getMessageIcon = (type: string) => {
  // 这里可以根据消息类型返回对应的图标
  return undefined
}

const getPriorityClass = (priority: number) => {
  const classMap: Record<number, string> = {
    1: 'info',
    2: 'primary', 
    3: 'warning',
    4: 'danger'
  }
  return classMap[priority] || 'info'
}

const getPriorityText = (priority: number) => {
  const textMap: Record<number, string> = {
    1: '低',
    2: '中',
    3: '高', 
    4: '紧急'
  }
  return textMap[priority] || '未知'
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN')
}

// 统一的未读数量同步函数
const syncUnreadCount = async (fromResponse?: number) => {
  try {
    if (fromResponse !== undefined) {
      // 从API响应中直接获取未读数量（性能更佳）
      console.log('同步未读数量 - 从响应:', fromResponse, '类型:', typeof fromResponse)
      unreadCount.value = fromResponse
      realtimeStore.setUnreadCount(fromResponse)
      
      // 确保响应式更新生效
      await nextTick()
      console.log('同步后 store 中的未读数量:', realtimeStore.unreadCount)
    } else {
      // 降级方案：重新调用接口获取
      const globalUnreadCount = await getUnreadMessageCount()
      console.log('同步未读数量 - 从接口:', globalUnreadCount, '类型:', typeof globalUnreadCount)
      unreadCount.value = globalUnreadCount
      realtimeStore.setUnreadCount(globalUnreadCount)
      
      // 确保响应式更新生效
      await nextTick()
      console.log('同步后 store 中的未读数量:', realtimeStore.unreadCount)
    }
  } catch (error) {
    console.error('同步未读数量失败:', error)
  }
}

const handleMarkRead = async (id: number) => {
  try {
    const response = await markMessageRead(id)
    console.log('标记已读接口响应:', response)
    
    // 响应拦截器已经返回了data部分，直接访问
    const message = response?.message || '标记成功'
    const unreadCount = response?.unread_count
    
    ElMessage.success(message)
    
    // 同步到store并使用响应中的未读数量
    realtimeStore.markMessageRead(id)
    
    if (unreadCount !== undefined) {
      await syncUnreadCount(unreadCount)
    } else {
      await syncUnreadCount()
    }
    
    // 刷新列表数据以确保状态一致
    await fetchMessages()
  } catch (error) {
    console.error('标记已读失败:', error)
  }
}

const handleBatchMarkRead = async () => {
  try {
    const ids = selectedMessages.value.map(msg => msg.id!).filter(id => id)
    if (ids.length === 0) {
      ElMessage.warning('请选择要标记的消息')
      return
    }
    
    const response = await batchMarkMessagesRead(ids)
    console.log('批量标记已读接口响应:', response)
    
    // 响应拦截器已经返回了data部分，直接访问
    const message = response?.message || `成功标记 ${ids.length} 条消息为已读`
    const unreadCount = response?.unread_count
    
    ElMessage.success(message)
    
    // 同步到store并使用响应中的未读数量
    realtimeStore.markMessagesRead(ids)
    
    if (unreadCount !== undefined) {
      await syncUnreadCount(unreadCount)
    } else {
      await syncUnreadCount()
    }
    
    // 清空选中项
    selectedMessages.value = []
    
    // 刷新列表数据以确保状态一致
    await fetchMessages()
  } catch (error) {
    console.error('批量标记已读失败:', error)
  }
}

const handleMarkAllRead = async () => {
  try {
    await ElMessageBox.confirm('确定要将所有消息标记为已读吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await markAllMessagesRead()
    console.log('全部标记已读接口响应:', response)
    
    // 响应拦截器已经返回了data部分，直接访问
    const message = response?.message || '全部消息已标记为已读'
    const unreadCount = response?.unread_count
    
    ElMessage.success(message)
    
    // 同步到store并使用响应中的未读数量
    realtimeStore.markAllRead()
    
    if (unreadCount !== undefined) {
      await syncUnreadCount(unreadCount)
    } else {
      await syncUnreadCount()
    }
    
    // 刷新列表数据以确保状态一致
    await fetchMessages()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('全部标记已读失败:', error)
    }
  }
}

const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条消息吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteRealtimeMessage(id)
    console.log('删除接口响应:', response)
    
    // 响应拦截器已经返回了data部分，直接访问
    const message = response?.message || '删除成功'
    const unreadCount = response?.unread_count
    
    ElMessage.success(message)
    
    // 使用响应中的未读数量同步
    if (unreadCount !== undefined) {
      await syncUnreadCount(unreadCount)
    } else {
      // 如果响应中没有未读数量，则重新获取
      await syncUnreadCount()
    }
    
    // 刷新列表数据以确保状态一致
    await fetchMessages()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除消息失败:', error)
    }
  }
}

const handleBatchDelete = async () => {
  try {
    const ids = selectedMessages.value.map(msg => msg.id!).filter(id => id)
    if (ids.length === 0) {
      ElMessage.warning('请选择要删除的消息')
      return
    }
    
    await ElMessageBox.confirm(`确定要删除选中的 ${ids.length} 条消息吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await batchDeleteMessages(ids)
    console.log('批量删除接口响应:', response)
    
    // 响应拦截器已经返回了data部分，直接访问
    const message = response?.message || `成功删除 ${ids.length} 条消息`
    const unreadCount = response?.unread_count
    
    ElMessage.success(message)
    
    // 使用响应中的未读数量同步
    if (unreadCount !== undefined) {
      await syncUnreadCount(unreadCount)
    } else {
      await syncUnreadCount()
    }
    
    // 清空选中项
    selectedMessages.value = []
    
    // 刷新列表数据以确保状态一致
    await fetchMessages()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除消息失败:', error)
    }
  }
}

const handleRefresh = () => {
  fetchMessages()
}

const handleViewDetail = (message: RealtimeMessage) => {
  currentMessage.value = message
  detailDialogVisible.value = true
}

const handleMarkReadInDialog = (messageId: number) => {
  handleMarkRead(messageId)
  detailDialogVisible.value = false
}

const handleDeleteInDialog = (messageId: number) => {
  handleDelete(messageId)
  detailDialogVisible.value = false
}

// 初始化数据
const initializeData = async () => {
  try {
    // 获取消息类型选项
    messageTypeOptions.value = await getMessageTypeOptions()
    
    // 初始化实时系统
    await realtimeStore.initialize()
    
    // 获取消息列表
    await fetchMessages()
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

// 监听实时消息更新
watch(() => realtimeStore.messages, () => {
  // 当store中的消息更新时，刷新页面数据
  fetchMessages()
}, { deep: true })

// 生命周期
onMounted(() => {
  initializeData()
})

onUnmounted(() => {
  // 清理工作
})
</script>

<style scoped>
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stats-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.connection-status {
  display: flex;
  align-items: center;
}

.stats-text {
  font-size: 14px;
  color: #606266;
  margin: 0;
}

.count-num {
  font-weight: bold;
  color: #409eff;
  margin: 0 4px;
}

.count-num-warn {
  font-weight: bold;
  color: #e6a23c;
  margin: 0 4px;
}

.count-num-danger {
  font-weight: bold;
  color: #f56c6c;
  margin: 0 4px;
}

.urgent-count {
  color: #f56c6c;
}

.message-content {
  padding: 4px 0;
}

.message-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.unread-title {
  font-weight: bold;
  color: #303133;
}

.urgent-icon {
  color: #f56c6c;
}

.message-text {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  line-height: 1.4;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 11px;
  color: #909399;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.time-ago {
  margin-left: auto;
}

.operation-btns {
  display: flex;
  gap: 4px;
}

/* 表格行样式 */
:deep(.unread-row) {
  background-color: #f0f9ff;
}

:deep(.urgent-row) {
  border-left: 3px solid #f56c6c;
}

:deep(.urgent-row.unread-row) {
  background-color: #fef0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-bar .form-inline {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-bar .form-inline .el-form-item {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .action-bar {
    flex-direction: column;
    gap: 8px;
  }
  
  .stats-container {
    flex-direction: column;
    gap: 8px;
  }
  
  .operation-btns {
    flex-direction: column;
  }
}
</style>