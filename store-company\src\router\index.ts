import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('@/views/auth/ForgotPassword.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/dashboard',
    name: 'DashboardLayout',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { 
          title: '数据大屏',
          requiresAuth: true 
        }
      }
    ]
  },
  {
    path: '/shops',
    name: 'ShopsLayout',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'shops',
        component: () => import('@/views/Shops.vue'),
        meta: { 
          title: '门店管理',
          requiresAuth: true 
        }
      }
    ]
  },
  {
    path: '/permissions',
    name: 'PermissionsLayout',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'permissions',
        redirect: '/permissions/admin'
      },
      {
        path: 'admin',
        name: 'permissions-admin',
        component: () => import('@/views/PermissionsAdmin.vue'),
        meta: { 
          title: '管理员管理',
          requiresAuth: true 
        }
      },
      {
        path: 'role',
        name: 'permissions-role',
        component: () => import('@/views/PermissionsRole.vue'),
        meta: { 
          title: '角色管理',
          requiresAuth: true 
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/login'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (!to.meta.requiresAuth && authStore.isAuthenticated && to.path !== '/login') {
    // 如果已登录用户访问非登录页面的公开页面，重定向到首页
    next('/dashboard')
  } else {
    next()
  }
})

export default router