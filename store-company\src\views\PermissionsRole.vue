<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">角色管理</h1>
      <p class="page-description">管理系统角色信息，设置角色权限</p>
    </div>

    <div class="card">
      <div class="card-header">
        <div class="card-actions">
          <button class="btn btn-primary" @click="showAddRoleModal = true">
            <i class="icon">➕</i> 新增角色
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th width="80">ID</th>
                <th width="200">角色名称</th>
                <th width="200">添加时间</th>
                <th width="200">更新时间</th>
                <th width="300">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="role in roleList" :key="role.id">
                <td>{{ role.id }}</td>
                <td>{{ role.role_name }}</td>
                <td>{{ formatDate(role.created_at) }}</td>
                <td>{{ formatDate(role.updated_at) }}</td>
                <td>
                  <div class="action-buttons">
                    <button class="btn btn-sm btn-warning" @click="managePermissions(role)" title="权限管理">
                      <i class="icon">🔐</i> 权限
                    </button>
                    <button class="btn btn-sm btn-primary" @click="editRole(role)" title="编辑角色">
                      <i class="icon">✏️</i> 编辑
                    </button>
                    <button class="btn btn-sm btn-danger" @click="deleteRole(role)" title="删除角色">
                      <i class="icon">🗑️</i> 删除
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 角色新增模态框 -->
    <div v-if="showAddRoleModal" class="modal-overlay" @click.self="showAddRoleModal = false">
      <div class="modal-dialog">
        <div class="modal-header">
          <h3 class="modal-title">新增角色</h3>
          <button class="modal-close" @click="showAddRoleModal = false">×</button>
        </div>
        <div class="modal-body">
          <form class="role-form">
            <div class="form-group">
              <label class="form-label">角色名称 <span class="required">*</span></label>
              <input type="text" class="form-control" v-model="roleForm.role_name" placeholder="请输入角色名称">
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showAddRoleModal = false">取消</button>
          <button class="btn btn-primary" @click="saveRole">
            <i class="icon">💾</i> 保存角色
          </button>
        </div>
      </div>
    </div>

    <!-- 角色编辑模态框 -->
    <div v-if="showEditRoleModal" class="modal-overlay" @click.self="showEditRoleModal = false">
      <div class="modal-dialog">
        <div class="modal-header">
          <h3 class="modal-title">编辑角色</h3>
          <button class="modal-close" @click="showEditRoleModal = false">×</button>
        </div>
        <div class="modal-body">
          <form class="role-form">
            <div class="form-group">
              <label class="form-label">角色名称 <span class="required">*</span></label>
              <input type="text" class="form-control" v-model="roleForm.role_name" placeholder="请输入角色名称">
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showEditRoleModal = false">取消</button>
          <button class="btn btn-primary" @click="saveRole">
            <i class="icon">💾</i> 保存修改
          </button>
        </div>
      </div>
    </div>

    <!-- 权限管理模态框 -->
    <div v-if="showPermissionsModal" class="modal-overlay" @click.self="showPermissionsModal = false">
      <div class="modal-dialog modal-lg">
        <div class="modal-header">
          <h3 class="modal-title">权限管理 - {{ currentRole?.role_name }}</h3>
          <button class="modal-close" @click="showPermissionsModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="permissions-container">
            <div class="permission-group" v-for="group in permissionGroups" :key="group.name">
              <div class="group-header">
                <label class="checkbox-label">
                  <input type="checkbox"
                         :checked="isGroupAllSelected(group)"
                         @change="toggleGroupPermissions(group, $event.target.checked)">
                  <strong>{{ group.name }}</strong>
                </label>
              </div>
              <div class="permission-items">
                <label class="checkbox-label permission-item" v-for="permission in group.permissions" :key="permission.id">
                  <input type="checkbox"
                         v-model="selectedPermissions"
                         :value="permission.id">
                  {{ permission.name }}
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showPermissionsModal = false">取消</button>
          <button class="btn btn-primary" @click="savePermissions">
            <i class="icon">💾</i> 保存权限
          </button>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <ConfirmDialog
      :visible="confirmDialogState.visible"
      :type="confirmDialogState.type"
      :title="confirmDialogState.title"
      :message="confirmDialogState.message"
      :details="confirmDialogState.details"
      :confirm-text="confirmDialogState.confirmText"
      :cancel-text="confirmDialogState.cancelText"
      :loading="confirmDialogState.loading"
      :loading-text="confirmDialogState.loadingText"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />

    <!-- 消息提示 -->
    <MessageToast ref="messageToastRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import MessageToast from '@/components/MessageToast.vue'
import { useConfirm, useMessage, useDeleteConfirm } from '@/composables/useDialog'

// 对话框组合式函数
const { confirmDialogState, handleConfirm, handleCancel } = useConfirm()
const { setMessageToastRef, success, error, warning } = useMessage()
const { confirmDelete } = useDeleteConfirm()

// 消息提示组件引用
const messageToastRef = ref<any>(null)

// 角色管理相关变量
const showAddRoleModal = ref(false)
const showEditRoleModal = ref(false)
const showPermissionsModal = ref(false)
const currentRole = ref<any>(null)

// 初始化消息提示
onMounted(() => {
  setMessageToastRef(messageToastRef.value)
})

// 角色表单数据
const roleForm = ref({
  id: null as number | null,
  role_name: ''
})

// 角色列表数据
const roleList = ref([
  {
    id: 1,
    role_name: '超级管理员',
    created_at: '2024-01-15 10:00:00',
    updated_at: '2024-01-15 10:00:00'
  },
  {
    id: 2,
    role_name: '店长',
    created_at: '2024-02-10 14:30:00',
    updated_at: '2024-02-10 14:30:00'
  },
  {
    id: 3,
    role_name: '收银员',
    created_at: '2024-03-05 09:15:00',
    updated_at: '2024-03-05 09:15:00'
  },
  {
    id: 4,
    role_name: '服务员',
    created_at: '2024-03-20 16:20:00',
    updated_at: '2024-03-20 16:20:00'
  }
])

// 权限组数据
const permissionGroups = ref([
  {
    name: '门店管理',
    permissions: [
      { id: 'shop_view', name: '查看门店' },
      { id: 'shop_add', name: '新增门店' },
      { id: 'shop_edit', name: '编辑门店' },
      { id: 'shop_delete', name: '删除门店' }
    ]
  },
  {
    name: '用户管理',
    permissions: [
      { id: 'user_view', name: '查看用户' },
      { id: 'user_add', name: '新增用户' },
      { id: 'user_edit', name: '编辑用户' },
      { id: 'user_delete', name: '删除用户' }
    ]
  },
  {
    name: '权限管理',
    permissions: [
      { id: 'role_view', name: '查看角色' },
      { id: 'role_add', name: '新增角色' },
      { id: 'role_edit', name: '编辑角色' },
      { id: 'role_delete', name: '删除角色' },
      { id: 'permission_manage', name: '权限管理' }
    ]
  },
  {
    name: '系统管理',
    permissions: [
      { id: 'system_config', name: '系统配置' },
      { id: 'log_view', name: '查看日志' },
      { id: 'backup_manage', name: '备份管理' }
    ]
  }
])

// 选中的权限
const selectedPermissions = ref<string[]>([])

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 角色管理方法
const editRole = (role: any) => {
  currentRole.value = { ...role }
  roleForm.value = { ...role }
  showEditRoleModal.value = true
}

const deleteRole = async (role: any) => {
  const confirmed = await confirmDelete(role.role_name, '角色')
  if (confirmed) {
    const index = roleList.value.findIndex(r => r.id === role.id)
    if (index > -1) {
      roleList.value.splice(index, 1)
      success(`角色"${role.role_name}"删除成功`)
      console.log('删除角色:', role.role_name)
    }
  }
}

const saveRole = () => {
  // 验证表单
  if (!roleForm.value.role_name.trim()) {
    error('请输入角色名称')
    return
  }

  if (roleForm.value.id) {
    // 编辑模式
    const index = roleList.value.findIndex(r => r.id === roleForm.value.id)
    if (index > -1) {
      roleList.value[index] = {
        ...roleForm.value,
        updated_at: new Date().toISOString().substring(0, 19).replace('T', ' ')
      }
      success(`角色"${roleForm.value.role_name}"修改成功`)
    }
    showEditRoleModal.value = false
  } else {
    // 新增模式
    const newRole = {
      ...roleForm.value,
      id: Math.max(...roleList.value.map(r => r.id)) + 1,
      created_at: new Date().toISOString().substring(0, 19).replace('T', ' '),
      updated_at: new Date().toISOString().substring(0, 19).replace('T', ' ')
    }
    roleList.value.push(newRole)
    success(`角色"${roleForm.value.role_name}"创建成功`)
    showAddRoleModal.value = false
  }

  // 重置表单
  resetRoleForm()
}

const resetRoleForm = () => {
  roleForm.value = {
    id: null,
    role_name: ''
  }
}

const managePermissions = (role: any) => {
  currentRole.value = role
  // 这里可以从服务器加载该角色的权限
  selectedPermissions.value = ['shop_view', 'user_view', 'role_view'] // 示例权限
  showPermissionsModal.value = true
}

const isGroupAllSelected = (group: any) => {
  return group.permissions.every((permission: any) =>
    selectedPermissions.value.includes(permission.id)
  )
}

const toggleGroupPermissions = (group: any, checked: boolean) => {
  if (checked) {
    // 添加该组所有权限
    group.permissions.forEach((permission: any) => {
      if (!selectedPermissions.value.includes(permission.id)) {
        selectedPermissions.value.push(permission.id)
      }
    })
  } else {
    // 移除该组所有权限
    group.permissions.forEach((permission: any) => {
      const index = selectedPermissions.value.indexOf(permission.id)
      if (index > -1) {
        selectedPermissions.value.splice(index, 1)
      }
    })
  }
}

const savePermissions = () => {
  console.log('保存权限:', currentRole.value.role_name, selectedPermissions.value)
  showPermissionsModal.value = false
  success(`已为角色"${currentRole.value.role_name}"保存权限设置`)
}
</script>

<style scoped>
/* CSS变量定义 - 商务典雅蓝色主题系统 */
:root {
  --primary-deep-blue: #0D1B2A;
  --primary-business-blue: #1B365D;
  --primary-steel-blue: #415A77;
  --secondary-steel-blue: #778DA9;
  --secondary-elegant-blue: #E0E1DD;
  --secondary-light-blue-gray: #F1F3F4;
  --accent-platinum: #C7D2DD;
  --accent-warm-silver: #B8C5D1;
  --accent-soft-gold: #E8B86D;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --text-primary: #0D1B2A;
  --text-secondary: #415A77;
  --text-light: #778DA9;
  --text-muted: #B8C5D1;
  --shadow-light: rgba(13, 27, 42, 0.08);
  --shadow-medium: rgba(13, 27, 42, 0.15);
  --shadow-deep: rgba(13, 27, 42, 0.25);
}

.page-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.page-description {
  color: var(--text-light);
  font-size: 14px;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--shadow-light);
  border: 1px solid #e4e7ed;
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 16px var(--shadow-medium);
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.card-body {
  padding: 24px;
}

/* 表格样式 */
.table-container {
  background: white;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e4e7ed;
}

.table th {
  background: var(--secondary-light-blue-gray);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 13px;
  position: sticky;
  top: 0;
  z-index: 1;
}

.table td {
  color: var(--text-secondary);
  font-size: 13px;
}

.table tr:hover {
  background: rgba(65, 90, 119, 0.05);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 4px;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn-primary {
  background: var(--primary-steel-blue);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-business-blue);
}

.btn-secondary {
  background: var(--secondary-elegant-blue);
  color: var(--text-secondary);
}

.btn-secondary:hover {
  background: var(--secondary-steel-blue);
  color: white;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.icon {
  font-size: 14px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-lg {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 角色管理样式 */
.role-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--text-primary);
}

.required {
  color: #dc3545;
}

.form-control {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-steel-blue);
  box-shadow: 0 0 0 2px rgba(52, 144, 220, 0.1);
}

/* 权限管理样式 */
.permissions-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.permission-group {
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: white;
}

.permission-group:last-child {
  margin-bottom: 0;
}

.group-header {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 6px 6px 0 0;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  transform: scale(1.1);
}

.permission-items {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.permission-item {
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-weight: normal;
}

.permission-item:hover {
  background-color: #f8f9fa;
}

.permission-item input[type="checkbox"] {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .card-actions {
    flex-direction: column;
  }

  .table-container {
    overflow-x: auto;
  }

  .modal-dialog {
    width: 95%;
    margin: 16px;
  }

  .permission-items {
    grid-template-columns: 1fr;
  }
}
</style>