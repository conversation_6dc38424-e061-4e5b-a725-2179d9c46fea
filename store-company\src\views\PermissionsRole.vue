<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">角色管理</h1>
      <p class="page-description">管理系统角色权限，包括角色创建、权限分配、角色成员管理等</p>
    </div>

    <!-- 操作栏 -->
    <div class="page-actions">
      <div class="search-box">
        <input 
          v-model="searchKeyword" 
          type="text" 
          placeholder="搜索角色名称、描述..." 
          class="search-input"
        />
        <button class="search-btn">🔍</button>
      </div>
      
      <div class="action-buttons">
        <button class="btn btn-primary" @click="openAddDialog">
          <span class="btn-icon">+</span>
          新增角色
        </button>
        <button class="btn btn-outline">
          <span class="btn-icon">📤</span>
          导出数据
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-row">
      <div class="stat-item">
        <div class="stat-icon">🏷️</div>
        <div class="stat-info">
          <div class="stat-value">{{ roleStats.total }}</div>
          <div class="stat-label">总角色数</div>
        </div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">✅</div>
        <div class="stat-info">
          <div class="stat-value">{{ roleStats.active }}</div>
          <div class="stat-label">启用中</div>
        </div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">👥</div>
        <div class="stat-info">
          <div class="stat-value">{{ roleStats.totalUsers }}</div>
          <div class="stat-label">关联用户</div>
        </div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">🔐</div>
        <div class="stat-info">
          <div class="stat-value">{{ roleStats.totalPermissions }}</div>
          <div class="stat-label">总权限数</div>
        </div>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="role-list-container">
      <div class="list-header">
        <h3>角色列表</h3>
        <div class="filter-controls">
          <select v-model="statusFilter" class="filter-select">
            <option value="">全部状态</option>
            <option value="active">启用</option>
            <option value="disabled">禁用</option>
          </select>
        </div>
      </div>

      <!-- 角色卡片网格 -->
      <div class="role-grid">
        <div v-for="role in filteredRoles" :key="role.id" class="role-card">
          <div class="role-header">
            <div class="role-info">
              <div class="role-icon" :class="role.type">
                {{ getRoleIcon(role.type) }}
              </div>
              <div class="role-basic">
                <h4 class="role-name">{{ role.name }}</h4>
                <p class="role-description">{{ role.description }}</p>
              </div>
            </div>
            <div class="role-status" :class="role.status">
              <span class="status-dot"></span>
              {{ getStatusText(role.status) }}
            </div>
          </div>

          <div class="role-content">
            <div class="role-stats">
              <div class="stat-item">
                <span class="stat-number">{{ role.userCount }}</span>
                <span class="stat-text">关联用户</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ role.permissions.length }}</span>
                <span class="stat-text">权限数量</span>
              </div>
            </div>

            <div class="role-permissions">
              <h5>主要权限</h5>
              <div class="permission-tags">
                <span 
                  v-for="permission in role.permissions.slice(0, 3)" 
                  :key="permission.id" 
                  class="permission-tag"
                >
                  {{ permission.name }}
                </span>
                <span v-if="role.permissions.length > 3" class="permission-more">
                  +{{ role.permissions.length - 3 }}个
                </span>
              </div>
            </div>
          </div>

          <div class="role-footer">
            <div class="role-meta">
              <span class="create-time">创建时间: {{ formatDate(role.createTime) }}</span>
            </div>
            <div class="role-actions">
              <button class="action-btn" @click="managePermissions(role)" title="权限管理">🔐</button>
              <button class="action-btn" @click="editRole(role)" title="编辑角色">✏️</button>
              <button class="action-btn" @click="viewRole(role)" title="查看详情">👁️</button>
              <button 
                class="action-btn" 
                :class="{ warning: role.status === 'active', success: role.status === 'disabled' }"
                @click="toggleRoleStatus(role)" 
                :title="role.status === 'active' ? '禁用' : '启用'"
              >
                {{ role.status === 'active' ? '🔒' : '🔓' }}
              </button>
              <button class="action-btn danger" @click="deleteRole(role)" title="删除角色">🗑️</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限管理模态框 -->
    <div v-if="showPermissionModal" class="modal-overlay" @click="closePermissionModal">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h3>权限管理 - {{ currentRole?.name }}</h3>
          <button class="modal-close" @click="closePermissionModal">×</button>
        </div>
        
        <div class="modal-content">
          <div class="permission-groups">
            <div v-for="group in permissionGroups" :key="group.id" class="permission-group">
              <div class="group-header">
                <label class="checkbox-label">
                  <input 
                    type="checkbox" 
                    :checked="isGroupFullySelected(group)"
                    :indeterminate="isGroupPartiallySelected(group)"
                    @change="toggleGroup(group, $event)"
                  />
                  <span class="group-name">{{ group.name }}</span>
                </label>
              </div>
              
              <div class="group-permissions">
                <label 
                  v-for="permission in group.permissions" 
                  :key="permission.id" 
                  class="permission-checkbox"
                >
                  <input 
                    type="checkbox" 
                    :value="permission.id"
                    v-model="selectedPermissions"
                  />
                  <span class="permission-name">{{ permission.name }}</span>
                  <span class="permission-desc">{{ permission.description }}</span>
                </label>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button class="btn btn-outline" @click="closePermissionModal">取消</button>
          <button class="btn btn-primary" @click="savePermissions">保存权限</button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <button class="page-btn" :disabled="currentPage === 1" @click="prevPage">上一页</button>
      <span class="page-info">第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
      <button class="page-btn" :disabled="currentPage === totalPages" @click="nextPage">下一页</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

const searchKeyword = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = 9
const showPermissionModal = ref(false)
const currentRole = ref<any>(null)
const selectedPermissions = ref<number[]>([])

const roleStats = reactive({
  total: 5,
  active: 4,
  totalUsers: 15,
  totalPermissions: 24
})

const roles = reactive([
  {
    id: 1,
    name: '超级管理员',
    description: '拥有系统所有权限，可管理所有功能模块',
    type: 'super_admin',
    status: 'active',
    userCount: 2,
    permissions: [
      { id: 1, name: '用户管理' },
      { id: 2, name: '角色管理' },
      { id: 3, name: '门店管理' },
      { id: 4, name: '数据统计' }
    ],
    createTime: new Date('2023-01-01')
  },
  {
    id: 2,
    name: '管理员',
    description: '拥有大部分管理权限，负责日常运营管理',
    type: 'admin',
    status: 'active',
    userCount: 5,
    permissions: [
      { id: 1, name: '用户管理' },
      { id: 3, name: '门店管理' },
      { id: 4, name: '数据统计' }
    ],
    createTime: new Date('2023-01-15')
  },
  {
    id: 3,
    name: '操作员',
    description: '基础操作权限，负责具体业务操作',
    type: 'operator',
    status: 'active',
    userCount: 8,
    permissions: [
      { id: 3, name: '门店管理' },
      { id: 4, name: '数据统计' }
    ],
    createTime: new Date('2023-02-01')
  }
])

const permissionGroups = reactive([
  {
    id: 1,
    name: '用户管理',
    permissions: [
      { id: 1, name: '查看用户', description: '查看用户列表和详情' },
      { id: 2, name: '新增用户', description: '创建新用户账户' },
      { id: 3, name: '编辑用户', description: '修改用户信息' },
      { id: 4, name: '删除用户', description: '删除用户账户' }
    ]
  },
  {
    id: 2,
    name: '角色管理',
    permissions: [
      { id: 5, name: '查看角色', description: '查看角色列表和详情' },
      { id: 6, name: '新增角色', description: '创建新角色' },
      { id: 7, name: '编辑角色', description: '修改角色信息' },
      { id: 8, name: '删除角色', description: '删除角色' },
      { id: 9, name: '分配权限', description: '为角色分配权限' }
    ]
  },
  {
    id: 3,
    name: '门店管理',
    permissions: [
      { id: 10, name: '查看门店', description: '查看门店列表和详情' },
      { id: 11, name: '新增门店', description: '创建新门店' },
      { id: 12, name: '编辑门店', description: '修改门店信息' },
      { id: 13, name: '删除门店', description: '删除门店' }
    ]
  }
])

const filteredRoles = computed(() => {
  let filtered = roles
  
  if (searchKeyword.value) {
    filtered = filtered.filter(role => 
      role.name.includes(searchKeyword.value) || 
      role.description.includes(searchKeyword.value)
    )
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(role => role.status === statusFilter.value)
  }
  
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filtered.slice(start, end)
})

const totalPages = computed(() => {
  let filtered = roles
  
  if (searchKeyword.value) {
    filtered = filtered.filter(role => 
      role.name.includes(searchKeyword.value) || 
      role.description.includes(searchKeyword.value)
    )
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(role => role.status === statusFilter.value)
  }
  
  return Math.ceil(filtered.length / pageSize)
})

const getRoleIcon = (type: string) => {
  const iconMap = {
    super_admin: '👑',
    admin: '🔧',
    operator: '👤'
  }
  return iconMap[type as keyof typeof iconMap] || '🏷️'
}

const getStatusText = (status: string) => {
  const statusMap = {
    active: '启用',
    disabled: '禁用'
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString()
}

const isGroupFullySelected = (group: any) => {
  return group.permissions.every((p: any) => selectedPermissions.value.includes(p.id))
}

const isGroupPartiallySelected = (group: any) => {
  const selected = group.permissions.some((p: any) => selectedPermissions.value.includes(p.id))
  const full = isGroupFullySelected(group)
  return selected && !full
}

const toggleGroup = (group: any, event: Event) => {
  const checked = (event.target as HTMLInputElement).checked
  const groupPermissionIds = group.permissions.map((p: any) => p.id)
  
  if (checked) {
    // 添加组内所有权限
    groupPermissionIds.forEach((id: number) => {
      if (!selectedPermissions.value.includes(id)) {
        selectedPermissions.value.push(id)
      }
    })
  } else {
    // 移除组内所有权限
    selectedPermissions.value = selectedPermissions.value.filter(
      id => !groupPermissionIds.includes(id)
    )
  }
}

const openAddDialog = () => {
  console.log('打开新增角色对话框')
}

const editRole = (role: any) => {
  console.log('编辑角色:', role)
}

const viewRole = (role: any) => {
  console.log('查看角色详情:', role)
}

const managePermissions = (role: any) => {
  currentRole.value = role
  selectedPermissions.value = role.permissions.map((p: any) => p.id)
  showPermissionModal.value = true
}

const closePermissionModal = () => {
  showPermissionModal.value = false
  currentRole.value = null
  selectedPermissions.value = []
}

const savePermissions = () => {
  if (currentRole.value) {
    // 更新角色权限
    const allPermissions = permissionGroups.flatMap(g => g.permissions)
    currentRole.value.permissions = allPermissions.filter(p => 
      selectedPermissions.value.includes(p.id)
    )
  }
  closePermissionModal()
}

const toggleRoleStatus = (role: any) => {
  const newStatus = role.status === 'active' ? 'disabled' : 'active'
  const action = newStatus === 'disabled' ? '禁用' : '启用'
  
  if (confirm(`确定要${action}角色"${role.name}"吗？`)) {
    role.status = newStatus
  }
}

const deleteRole = (role: any) => {
  if (confirm(`确定要删除角色"${role.name}"吗？此操作不可恢复！`)) {
    const index = roles.findIndex(r => r.id === role.id)
    if (index > -1) {
      roles.splice(index, 1)
      roleStats.total--
    }
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}
</script>

<style scoped>
/* CSS变量定义 - 商务典雅蓝色主题系统 */
:root {
  --primary-deep-blue: #0D1B2A;
  --primary-business-blue: #1B365D;
  --primary-steel-blue: #415A77;
  --secondary-steel-blue: #778DA9;
  --secondary-elegant-blue: #E0E1DD;
  --secondary-light-blue-gray: #F1F3F4;
  --accent-platinum: #C7D2DD;
  --accent-warm-silver: #B8C5D1;
  --accent-soft-gold: #E8B86D;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --text-primary: #0D1B2A;
  --text-secondary: #415A77;
  --text-light: #778DA9;
  --text-muted: #B8C5D1;
  --shadow-light: rgba(13, 27, 42, 0.08);
  --shadow-medium: rgba(13, 27, 42, 0.15);
  --shadow-deep: rgba(13, 27, 42, 0.25);
}

.page-container {
  padding: 24px;
  background: var(--secondary-light-blue-gray);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
}

/* 操作栏 */
.page-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.search-box {
  display: flex;
  align-items: center;
  position: relative;
}

.search-input {
  width: 300px;
  height: 40px;
  padding: 0 40px 0 16px;
  border: 1px solid var(--accent-platinum);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-steel-blue);
  box-shadow: 0 0 0 3px rgba(65, 90, 119, 0.1);
}

.search-btn {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: var(--text-light);
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background: var(--primary-steel-blue);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-business-blue);
  transform: translateY(-1px);
}

.btn-outline {
  background: white;
  color: var(--text-secondary);
  border: 1px solid var(--accent-platinum);
}

.btn-outline:hover {
  background: var(--secondary-light-blue-gray);
}

.btn-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 统计行 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--shadow-light);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px var(--shadow-medium);
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 角色列表容器 */
.role-list-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px var(--shadow-light);
  margin-bottom: 24px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--secondary-light-blue-gray);
}

.list-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--accent-platinum);
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-steel-blue);
}

/* 角色网格 */
.role-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 20px;
}

.role-card {
  border: 1px solid var(--accent-platinum);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
  background: white;
}

.role-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px var(--shadow-medium);
  border-color: var(--primary-steel-blue);
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.role-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.role-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.role-icon.super_admin {
  background: linear-gradient(135deg, #F56C6C 0%, #E6A23C 100%);
}

.role-icon.admin {
  background: linear-gradient(135deg, #E6A23C 0%, #67C23A 100%);
}

.role-icon.operator {
  background: linear-gradient(135deg, #909399 0%, #778DA9 100%);
}

.role-basic {
  flex: 1;
}

.role-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 6px;
}

.role-description {
  font-size: 14px;
  color: var(--text-light);
  line-height: 1.4;
}

.role-status {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
}

.role-status.active {
  background: rgba(103, 194, 58, 0.1);
  color: var(--success-color);
}

.role-status.disabled {
  background: rgba(144, 147, 153, 0.1);
  color: var(--info-color);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  margin-right: 6px;
}

.role-content {
  margin-bottom: 20px;
}

.role-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  padding: 16px;
  background: var(--secondary-light-blue-gray);
  border-radius: 8px;
}

.role-stats .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  background: none;
  box-shadow: none;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-text {
  font-size: 12px;
  color: var(--text-secondary);
}

.role-permissions h5 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.permission-tag {
  padding: 4px 8px;
  background: rgba(65, 90, 119, 0.1);
  color: var(--text-secondary);
  border-radius: 12px;
  font-size: 12px;
}

.permission-more {
  padding: 4px 8px;
  background: rgba(232, 184, 109, 0.1);
  color: var(--accent-soft-gold);
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.role-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid var(--secondary-light-blue-gray);
}

.role-meta {
  flex: 1;
}

.create-time {
  font-size: 12px;
  color: var(--text-muted);
}

.role-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--secondary-light-blue-gray);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-btn:hover {
  background: var(--accent-platinum);
}

.action-btn.danger:hover {
  background: var(--danger-color);
  color: white;
}

.action-btn.warning:hover {
  background: var(--warning-color);
  color: white;
}

.action-btn.success:hover {
  background: var(--success-color);
  color: white;
}

/* 权限管理模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(13, 27, 42, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 16px 64px var(--shadow-deep);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--secondary-light-blue-gray);
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-light);
  transition: all 0.3s ease;
}

.modal-close:hover {
  color: var(--text-primary);
  background: var(--secondary-light-blue-gray);
  border-radius: 50%;
}

.modal-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.permission-groups {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.permission-group {
  border: 1px solid var(--accent-platinum);
  border-radius: 8px;
  overflow: hidden;
}

.group-header {
  background: var(--secondary-light-blue-gray);
  padding: 16px;
  border-bottom: 1px solid var(--accent-platinum);
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 600;
  color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  transform: scale(1.2);
}

.group-permissions {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.permission-checkbox {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.permission-checkbox:hover {
  background: var(--secondary-light-blue-gray);
}

.permission-checkbox input[type="checkbox"] {
  margin-right: 12px;
  margin-top: 2px;
}

.permission-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.permission-desc {
  font-size: 12px;
  color: var(--text-light);
  margin-left: 4px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid var(--secondary-light-blue-gray);
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid var(--accent-platinum);
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.page-btn:hover:not(:disabled) {
  background: var(--primary-steel-blue);
  color: white;
  border-color: var(--primary-steel-blue);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .page-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .search-input {
    width: 100%;
  }
  
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .role-grid {
    grid-template-columns: 1fr;
  }
  
  .list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .modal-container {
    width: 95%;
    margin: 16px;
  }
  
  .role-stats {
    gap: 16px;
  }
}
</style>