import api from './api'

// 会员概览相关的类型定义
interface MemberOverviewSummary {
  total_members: number
  active_members: number  
  total_consumption: number
  average_level: number
  growth_rates: {
    members: number
    active: number
    consumption: number
    level: number
  }
}

interface MemberGrowthData {
  date: string
  register_count: number
  active_count: number
}

interface MemberLevelDistribution {
  level_name: string
  level_id: number
  member_count: number
  percentage: number
  color?: string
}

interface MemberAssetStats {
  points: {
    issued: number
    used: number
    remaining: number
    usage_rate: number
  }
  balance: {
    total: number
    recharged: number
    consumed: number
    activity_rate: number
  }
  other_assets: Array<{
    type: string
    name: string
    count: number
    unit: string
    status: string
    status_text: string
  }>
}

interface MemberActivityLayer {
  layer_key: string
  layer_name: string
  description: string
  member_count: number
  percentage: number
  color: string
}

interface MemberOverviewParams {
  start_date: string
  end_date: string
  shop_id?: number
}

interface MemberOverviewResponse {
  summary: MemberOverviewSummary
  growth_data: MemberGrowthData[]
  level_distribution: MemberLevelDistribution[]
  asset_stats: MemberAssetStats
  activity_layers: MemberActivityLayer[]
}

/**
 * 会员概览服务
 */
class MemberOverviewService {
  /**
   * 获取会员概览数据
   */
  async getOverviewData(params: MemberOverviewParams): Promise<MemberOverviewResponse> {
    const response = await api.post('/shop/member/overview', params)
    return response.data
  }

  /**
   * 获取会员统计摘要
   */
  async getSummaryData(params: MemberOverviewParams): Promise<MemberOverviewSummary> {
    try {
      const response = await api.get('/shop/member/summary', { params })
      
      // 数据安全验证
      if (!response || typeof response !== 'object') {
        throw new Error('接口返回数据格式错误')
      }
      
      // 返回安全的默认数据结构
      return {
        total_members: response.total_members ?? 0,
        active_members: response.active_members ?? 0,
        total_consumption: response.total_consumption ?? 0,
        average_level: response.average_level ?? 1,
        growth_rates: {
          members: response.growth_rates?.members ?? 0,
          active: response.growth_rates?.active ?? 0,
          consumption: response.growth_rates?.consumption ?? 0,
          level: response.growth_rates?.level ?? 0
        }
      }
    } catch (error) {
      console.error('获取会员统计摘要失败:', error)
      // 返回默认数据，防止页面崩溃
      return {
        total_members: 0,
        active_members: 0,
        total_consumption: 0,
        average_level: 1,
        growth_rates: {
          members: 0,
          active: 0,
          consumption: 0,
          level: 0
        }
      }
    }
  }

  /**
   * 获取会员增长趋势数据
   */
  async getGrowthTrendData(params: MemberOverviewParams & { type?: 'register' | 'active' }): Promise<MemberGrowthData[]> {
    try {
      const response = await api.get('/shop/member/growthTrend', { params })
      return Array.isArray(response) ? response : []
    } catch (error) {
      console.error('获取会员增长趋势数据失败:', error)
      return []
    }
  }

  /**
   * 获取会员等级分布数据
   */
  async getLevelDistributionData(params: MemberOverviewParams): Promise<MemberLevelDistribution[]> {
    try {
      const response = await api.get('/shop/member/levelDistribution', { params })
      return Array.isArray(response) ? response : []
    } catch (error) {
      console.error('获取会员等级分布数据失败:', error)
      return []
    }
  }

  /**
   * 获取会员资产统计数据
   */
  async getAssetStatsData(params: MemberOverviewParams): Promise<MemberAssetStats> {
    try {
      const response = await api.get('/shop/member/assetStats', { params })
      
      if (!response || typeof response !== 'object') {
        throw new Error('接口返回数据格式错误')
      }
      
      return {
        points: response.points ?? { issued: 0, used: 0, remaining: 0, usage_rate: 0 },
        balance: response.balance ?? { total: 0, recharged: 0, consumed: 0, activity_rate: 0 },
        other_assets: Array.isArray(response.other_assets) ? response.other_assets : []
      }
    } catch (error) {
      console.error('获取会员资产统计数据失败:', error)
      return {
        points: { issued: 0, used: 0, remaining: 0, usage_rate: 0 },
        balance: { total: 0, recharged: 0, consumed: 0, activity_rate: 0 },
        other_assets: []
      }
    }
  }

  /**
   * 获取会员活跃度分层数据
   */
  async getActivityLayersData(params: MemberOverviewParams): Promise<MemberActivityLayer[]> {
    try {
      const response = await api.get('/shop/member/activityLayers', { params })
      return Array.isArray(response) ? response : []
    } catch (error) {
      console.error('获取会员活跃度分层数据失败:', error)
      return []
    }
  }

  /**
   * 导出会员概览报表
   */
  async exportOverviewReport(params: MemberOverviewParams): Promise<Blob> {
    const response = await api.get('/shop/member/exportReport', {
      params,
      responseType: 'blob'
    })
    return response.data
  }

  /**
   * 获取会员消费趋势数据
   */
  async getConsumptionTrendData(params: MemberOverviewParams): Promise<Array<{
    date: string
    consumption_amount: number
    order_count: number
    avg_order_amount: number
  }>> {
    const response = await api.get('/shop/member/consumptionTrend', { params })
    return response.data
  }

  /**
   * 获取会员行为分析数据
   */
  async getBehaviorAnalysisData(params: MemberOverviewParams): Promise<{
    consumption_preference: Array<{
      category: string
      percentage: number
      amount: number
    }>
    consumption_time_distribution: Array<{
      hour: number
      order_count: number
      consumption_amount: number
    }>
    referral_network: Array<{
      referrer_id: number
      referrer_name: string
      referral_count: number
      total_consumption: number
    }>
  }> {
    const response = await api.get('/shop/member/behaviorAnalysis', { params })
    return response.data
  }

  /**
   * 获取会员流失风险预警数据
   */
  async getChurnRiskData(params: MemberOverviewParams): Promise<Array<{
    risk_level: 'high' | 'medium' | 'low'
    member_count: number
    percentage: number
    description: string
    suggested_actions: string[]
  }>> {
    const response = await api.get('/shop/member/churnRisk', { params })
    return response.data
  }

  /**
   * 获取会员价值分析数据
   */
  async getValueAnalysisData(params: MemberOverviewParams): Promise<{
    ltv_distribution: Array<{
      value_range: string
      member_count: number
      percentage: number
      avg_ltv: number
    }>
    rfm_analysis: Array<{
      segment: string
      member_count: number
      percentage: number
      description: string
      marketing_strategy: string
    }>
  }> {
    const response = await api.get('/shop/member/valueAnalysis', { params })
    return response.data
  }
}

export default new MemberOverviewService()

// 导出类型定义供其他组件使用
export type {
  MemberOverviewSummary,
  MemberGrowthData,
  MemberLevelDistribution,
  MemberAssetStats,
  MemberActivityLayer,
  MemberOverviewParams,
  MemberOverviewResponse
}