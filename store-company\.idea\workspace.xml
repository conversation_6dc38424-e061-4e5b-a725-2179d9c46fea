<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c15df0e6-8497-4c17-a252-c2626a191e78" name="角色管理" comment="角色管理">
      <change beforePath="$PROJECT_DIR$/src/views/PermissionsAdmin.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/PermissionsAdmin.vue" afterDir="false" />
    </list>
    <list id="5be66a97-0266-4a8d-a427-87fc0854c8de" name="项目初始化" comment="项目初始化" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="E:\phpstudy_pro\Extensions\php\php7.3.4nts\php.exe" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="30cchK66626mGUoLoHNiIZtSKTy" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;project.propVCSSupport.DirectoryMappings&quot;,
    &quot;ts.external.directory.path&quot;: &quot;E:\\phpstudy_pro\\WWW\\2024\\shop-sass-xly\\new-shop-sales\\store-company\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-php-predefined-ba97393d7c68-b4dcf6bb9de9-com.jetbrains.php.sharedIndexes-PS-233.13135.108" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="c15df0e6-8497-4c17-a252-c2626a191e78" name="更改" comment="" />
      <changelist id="5be66a97-0266-4a8d-a427-87fc0854c8de" name="项目初始化" comment="项目初始化" />
      <created>1753930420565</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753930420565</updated>
      <workItem from="1753930430751" duration="816000" />
      <workItem from="1753951398334" duration="2569000" />
      <workItem from="1753955292564" duration="1072000" />
      <workItem from="1753956496344" duration="2922000" />
    </task>
    <task id="LOCAL-00001" summary="项目初始化">
      <option name="closed" value="true" />
      <created>1753930562468</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753930562468</updated>
    </task>
    <task id="LOCAL-00002" summary="优化细节">
      <option name="closed" value="true" />
      <created>1753951436609</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753951436609</updated>
    </task>
    <task id="LOCAL-00003" summary="数据大屏">
      <option name="closed" value="true" />
      <created>1753952453634</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753952453634</updated>
    </task>
    <task id="LOCAL-00004" summary="门店列表">
      <option name="closed" value="true" />
      <created>1753953499565</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753953499565</updated>
    </task>
    <task id="LOCAL-00005" summary="门店列表">
      <option name="closed" value="true" />
      <created>1753956573004</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753956573004</updated>
    </task>
    <task id="LOCAL-00006" summary="门店表单">
      <option name="closed" value="true" />
      <created>1753964107976</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753964107976</updated>
    </task>
    <task id="LOCAL-00007" summary="角色管理">
      <option name="closed" value="true" />
      <created>1753966047041</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753966047041</updated>
    </task>
    <option name="localTasksCounter" value="8" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="项目初始化" />
    <MESSAGE value="优化细节" />
    <MESSAGE value="数据大屏" />
    <MESSAGE value="门店列表" />
    <MESSAGE value="门店表单" />
    <MESSAGE value="角色管理" />
    <option name="LAST_COMMIT_MESSAGE" value="角色管理" />
  </component>
</project>