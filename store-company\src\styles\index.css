/* 授权企业管理系统 - 严格按照原型设计的样式系统 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 确保HTML和body没有额外的边距和内边距 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden;
}

/* 确保根元素占满整个视窗 */
#app {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
}

:root {
  /* 商务典雅蓝色主题系统 */
  --primary-deep-blue: #0D1B2A;
  --primary-business-blue: #1B365D;
  --primary-steel-blue: #415A77;
  --secondary-steel-blue: #778DA9;
  --secondary-elegant-blue: #E0E1DD;
  --secondary-light-blue-gray: #F1F3F4;
  --accent-platinum: #C7D2DD;
  --accent-warm-silver: #B8C5D1;
  --accent-soft-gold: #E8B86D;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --text-primary: #0D1B2A;
  --text-secondary: #415A77;
  --text-light: #778DA9;
  --text-muted: #B8C5D1;
  --glass-background: rgba(255, 255, 255, 0.92);
  --glass-border: rgba(255, 255, 255, 0.8);
  --shadow-deep: rgba(13, 27, 42, 0.25);
  --shadow-medium: rgba(13, 27, 42, 0.15);
  --shadow-light: rgba(13, 27, 42, 0.08);
  --gradient-primary: linear-gradient(135deg, var(--primary-business-blue) 0%, var(--primary-deep-blue) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-soft-gold) 0%, var(--accent-warm-silver) 100%);
  
  /* 兼容性变量 - 保持与现有组件的兼容 */
  --color-primary: var(--primary-steel-blue);
  --color-success: var(--success-color);
  --color-warning: var(--warning-color);
  --color-danger: var(--danger-color);
  --color-info: var(--info-color);
  --bg-body: #f8f9fa;
  --bg-white: #ffffff;
  --bg-hover: var(--secondary-light-blue-gray);
  --text-white: #ffffff;
  --border-light: var(--secondary-elegant-blue);
  --border-medium: var(--accent-platinum);
  --border-primary: var(--primary-steel-blue);
  
  /* 阴影系统 */
  --shadow-sm: var(--shadow-light);
  --shadow-md: var(--shadow-medium);
  --shadow-lg: var(--shadow-deep);
  
  /* 边框圆角 */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 20px;
  --radius-full: 50%;
  
  /* Z-index层级 */
  --z-sidebar: 999;
  --z-header: 1000;
  --z-modal: 2000;
  
  /* 布局尺寸 */
  --header-height: 56px;
  --sidebar-width: 240px;
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

body {
  font-family: var(--font-family);
  background-color: var(--bg-body);
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  /* 确保body没有默认边距 */
}

/* Header 样式 */
.header {
  background: var(--color-primary-gradient);
  color: var(--text-white);
  padding: 0 20px;
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-sm);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-header);
}

.logo {
  font-size: 20px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  /* gap: 15px; */
}

.notification-bell {
  position: relative;
  cursor: pointer;
  font-size: 18px;
}

.notification-bell::after {
  content: "3";
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4757;
  color: var(--text-white);
  border-radius: var(--radius-full);
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Sidebar 样式 */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  /* top: var(--header-height); */
  width: var(--sidebar-width);
  /* height: calc(100vh - var(--header-height)); */
  height: 100vh;
  background: var(--bg-white);
  box-shadow: 2px 0 10px rgba(0,0,0,0.1);
  overflow-y: auto;
  z-index: var(--z-sidebar);
}

.nav-menu {
  list-style: none;
  padding: 20px 0;
}

.nav-item {
  margin: 5px 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-link:hover, 
.nav-link.active {
  background: var(--bg-hover);
  color: var(--color-primary);
  border-left-color: var(--color-primary);
}

.nav-icon {
  margin-right: 12px;
  font-size: 18px;
  width: 20px;
  text-align: center;
}

/* Main Content 样式 - 移除全局样式，使用组件内样式 */
/*.main-content {
  margin-left: var(--sidebar-width);
  margin-top: var(--header-height);
  padding: 20px;
  min-height: calc(100vh - var(--header-height));
}*/

/* Dashboard Grid 样式 */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

/* Stat Card 样式 */
.stat-card {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: 25px;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.stat-title {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--text-white);
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 5px;
}

.stat-change {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-change.positive {
  color: var(--color-success);
}

.stat-change.negative {
  color: var(--color-danger);
}

/* Charts Section 样式 */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: 25px;
  box-shadow: var(--shadow-md);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.chart-placeholder {
  height: 300px;
  background: linear-gradient(45deg, #f8f9ff 0%, #e8ecff 100%);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  font-size: 16px;
  border: 2px dashed var(--color-primary);
}

/* Store Grid 样式 */
.store-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.store-card {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: 20px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.store-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.store-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.store-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.store-status {
  padding: 4px 12px;
  border-radius: var(--radius-xl);
  font-size: 12px;
  font-weight: 500;
}

.status-online {
  background: #d4edda;
  color: #155724;
}

.status-offline {
  background: #f8d7da;
  color: #721c24;
}

.store-info {
  margin-bottom: 15px;
}

.store-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.store-actions {
  display: flex;
  gap: 10px;
}

/* Button 样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background: var(--color-primary);
  color: var(--text-white);
}

.btn-primary:hover {
  background: var(--color-primary-dark);
}

.btn-secondary {
  background: var(--color-gray-50);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
}

.btn-secondary:hover {
  background: #e9ecef;
}

.btn-success {
  background: var(--color-success);
  color: var(--text-white);
}

.btn-warning {
  background: var(--color-warning);
  color: #212529;
}

.btn-danger {
  background: var(--color-danger);
  color: var(--text-white);
}

/* Table 样式 */
.table-container {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: 25px;
  box-shadow: var(--shadow-md);
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-light);
}

.table th {
  background: var(--color-gray-50);
  font-weight: 600;
  color: var(--text-primary);
}

.table tr:hover {
  background: var(--bg-hover);
}

/* Form 样式 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-primary);
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-sm);
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Modal 样式 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: var(--z-modal);
}

.modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: 30px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
}

/* Page 样式 */
.page {
  display: none;
}

.page.active {
  display: block;
}

.page-header {
  /* display: flex;
  justify-content: space-between;
  align-items: center; */
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-secondary);
  font-size: 14px;
  /* margin-bottom: 10px; */
}

.breadcrumb a {
  color: var(--color-primary);
  text-decoration: none;
}

/* Alert 样式 */
.alert {
  padding: 12px 16px;
  border-radius: var(--radius-sm);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.alert-danger {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Loading 样式 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--color-primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tabs 样式 */
.tabs {
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 20px;
}

.tab-nav {
  display: flex;
  gap: 0;
}

.tab-link {
  padding: 12px 20px;
  color: var(--text-secondary);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.tab-link.active,
.tab-link:hover {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .charts-section {
    grid-template-columns: 1fr;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .store-grid {
    grid-template-columns: 1fr;
  }
}

/* 隐藏类 */
.hidden {
  display: none !important;
}