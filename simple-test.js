const http = require('http');

async function testLoginPageAccess() {
    console.log('🌐 测试登录页面访问...');
    
    const url = 'http://localhost:8000/%E6%8E%88%E6%9D%83%E5%85%AC%E5%8F%B8%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F.html';
    
    return new Promise((resolve, reject) => {
        http.get(url, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`✅ HTTP状态码: ${res.statusCode}`);
                
                // 测试页面内容
                const tests = [
                    {
                        name: '页面标题',
                        test: () => data.includes('<title>授权公司管理系统</title>'),
                        expected: '包含正确的页面标题'
                    },
                    {
                        name: 'Vue.js加载',
                        test: () => data.includes('vue@3/dist/vue.global.js'),
                        expected: '包含Vue 3框架'
                    },
                    {
                        name: 'Element Plus加载',
                        test: () => data.includes('element-plus/dist/index.full.js'),
                        expected: '包含Element Plus UI库'
                    },
                    {
                        name: '登录表单',
                        test: () => data.includes('login_main') && data.includes('input'),
                        expected: '包含登录表单结构'
                    },
                    {
                        name: '商务蓝色主题',
                        test: () => data.includes('--primary-business-blue: #1B365D'),
                        expected: '包含商务蓝色主题变量'
                    },
                    {
                        name: '登录按钮',
                        test: () => data.includes('login-btn') || data.includes('立即登录'),
                        expected: '包含登录按钮'
                    },
                    {
                        name: '响应式设计',
                        test: () => data.includes('@media') && data.includes('max-width'),
                        expected: '包含响应式设计代码'
                    },
                    {
                        name: '动画效果',
                        test: () => data.includes('@keyframes') && data.includes('animation'),
                        expected: '包含CSS动画效果'
                    }
                ];
                
                console.log('\n📋 页面结构测试结果:');
                console.log('━'.repeat(50));
                
                let passedTests = 0;
                tests.forEach((test, index) => {
                    const result = test.test();
                    const status = result ? '✅ 通过' : '❌ 失败';
                    console.log(`${index + 1}. ${test.name}: ${status}`);
                    console.log(`   期望: ${test.expected}`);
                    if (result) passedTests++;
                });
                
                console.log('━'.repeat(50));
                console.log(`📊 测试总结: ${passedTests}/${tests.length} 项测试通过`);
                
                if (passedTests === tests.length) {
                    console.log('🎉 所有测试通过！页面结构完整。');
                } else {
                    console.log('⚠️  部分测试失败，可能需要检查页面结构。');
                }
                
                // 检查页面大小
                const sizeKB = (data.length / 1024).toFixed(2);
                console.log(`📏 页面大小: ${sizeKB} KB`);
                console.log(`📄 页面行数: ${data.split('\n').length} 行`);
                
                resolve({
                    status: res.statusCode,
                    size: data.length,
                    tests: tests.map(test => ({
                        name: test.name,
                        passed: test.test()
                    })),
                    passedCount: passedTests,
                    totalCount: tests.length
                });
            });
        }).on('error', (err) => {
            console.error('❌ 请求失败:', err.message);
            reject(err);
        });
    });
}

// 运行测试
testLoginPageAccess()
    .then((result) => {
        console.log('\n🔍 详细测试完成！');
        if (result.passedCount === result.totalCount) {
            console.log('✨ Playwright MCP可以正常测试此页面');
        }
    })
    .catch((error) => {
        console.error('测试失败:', error.message);
    });