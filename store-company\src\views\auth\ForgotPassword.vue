<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <h1 class="auth-title">忘记密码</h1>
        <div class="step-indicator">
          <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
            <div class="step-number">1</div>
            <div class="step-text">身份验证</div>
          </div>
          <div class="step-line" :class="{ active: currentStep > 1 }"></div>
          <div class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
            <div class="step-number">2</div>
            <div class="step-text">短信验证</div>
          </div>
          <div class="step-line" :class="{ active: currentStep > 2 }"></div>
          <div class="step" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
            <div class="step-number">3</div>
            <div class="step-text">重置密码</div>
          </div>
        </div>
      </div>
      
      <!-- Step 1: Identity Verification -->
      <form v-show="currentStep === 1" @submit.prevent="handleStep1" class="auth-form">
        <div class="form-group">
          <label class="form-label">手机号码</label>
          <input 
            type="tel" 
            class="form-input" 
            placeholder="请输入注册时的手机号码"
            v-model="step1Form.phone"
          />
        </div>
        
        <div class="contact-service">
          <span class="contact-text">遇到问题？</span>
          <button type="button" class="contact-btn" @click="showContactModal = true">联系客服</button>
        </div>
        
        <button type="submit" class="submit-btn" :disabled="loading">
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? '获取验证码' : '获取验证码' }}
        </button>
        
        <div class="auth-switch">
          <span class="auth-switch-text">想起密码了？</span>
          <router-link to="/login" class="auth-switch-link">立即登录</router-link>
        </div>
      </form>

      <!-- Step 2: SMS Verification -->
      <form v-show="currentStep === 2" @submit.prevent="handleStep2" class="auth-form">
        <div class="verification-info">
          <p>验证码已发送至手机号</p>
          <p class="phone-display">{{ maskedPhone }}</p>
        </div>
        
        <div class="form-group">
          <label class="form-label">短信验证码</label>
          <div class="captcha-container">
            <input 
              type="text" 
              class="form-input captcha-input" 
              placeholder="请输入6位验证码"
              v-model="step2Form.smsCode"
              maxlength="6"
            />
            <button 
              type="button" 
              class="captcha-btn" 
              :disabled="smsCountdown > 0"
              @click="resendSmsCode"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '重新发送' }}
            </button>
          </div>
        </div>
        
        <button type="submit" class="submit-btn" :disabled="loading">
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? '验证中...' : '下一步' }}
        </button>
        
        <div class="auth-switch">
          <button type="button" class="back-btn" @click="goBack">返回上一步</button>
        </div>
      </form>

      <!-- Step 3: Reset Password -->
      <form v-show="currentStep === 3" @submit.prevent="handleStep3" class="auth-form">
        <div class="form-group">
          <label class="form-label">新密码</label>
          <div class="password-container">
            <input 
              :type="showPassword ? 'text' : 'password'" 
              class="form-input" 
              placeholder="请设置新密码"
              v-model="step3Form.password"
            />
            <button 
              type="button" 
              class="password-toggle" 
              @click="togglePassword"
            >
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
          <div class="password-strength" v-if="step3Form.password">
            <div 
              class="password-strength-bar" 
              :class="passwordStrength.level"
            ></div>
            <div class="password-strength-text">
              密码强度：{{ passwordStrength.text }}
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">确认密码</label>
          <div class="password-container">
            <input 
              :type="showConfirmPassword ? 'text' : 'password'" 
              class="form-input" 
              placeholder="请确认新密码"
              v-model="step3Form.confirmPassword"
            />
            <button 
              type="button" 
              class="password-toggle" 
              @click="toggleConfirmPassword"
            >
              {{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
        </div>
        
        <button type="submit" class="submit-btn" :disabled="loading">
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? '重置中...' : '完成重置' }}
        </button>
        
        <div class="auth-switch">
          <button type="button" class="back-btn" @click="goBack">返回上一步</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Contact Service Modal -->
  <div class="modal" :class="{ show: showContactModal }" @click="closeContactModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">联系客服</h3>
        <button class="close-btn" @click="showContactModal = false">&times;</button>
      </div>
      <div class="modal-body">
        <div class="contact-info">
          <div class="contact-item">
            <div class="contact-icon">📞</div>
            <div class="contact-details">
              <div class="contact-label">客服热线</div>
              <div class="contact-value">400-123-4567</div>
              <div class="contact-note">工作时间：9:00-18:00</div>
            </div>
          </div>
          
          <div class="contact-item">
            <div class="contact-icon">💬</div>
            <div class="contact-details">
              <div class="contact-label">在线客服</div>
              <div class="contact-value">微信：CompanyService</div>
              <div class="contact-note">24小时在线服务</div>
            </div>
          </div>
          
          <div class="contact-item">
            <div class="contact-icon">📧</div>
            <div class="contact-details">
              <div class="contact-label">邮箱支持</div>
              <div class="contact-value"><EMAIL></div>
              <div class="contact-note">1-2个工作日内回复</div>
            </div>
          </div>
        </div>
        
        <div class="service-note">
          <p><strong>密码重置说明：</strong></p>
          <p>联系客服时请提供您的手机号码和企业信息，客服将协助您重置密码。</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn-secondary" @click="showContactModal = false">关闭</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 当前步骤
const currentStep = ref(1)
const loading = ref(false)

// 步骤1表单数据
const step1Form = ref({
  phone: ''
})

// 步骤2表单数据
const step2Form = ref({
  smsCode: ''
})

// 步骤3表单数据
const step3Form = ref({
  password: '',
  confirmPassword: ''
})

// 密码显示状态
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const smsCountdown = ref(0)

// 客服模态框状态
const showContactModal = ref(false)

// 脱敏手机号显示
const maskedPhone = computed(() => {
  const phone = step1Form.value.phone
  if (phone.length >= 11) {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }
  return phone
})

// 密码强度检测
const passwordStrength = computed(() => {
  const password = step3Form.value.password.trim()
  if (!password) return { level: '', text: '' }
  
  let score = 0
  if (password.length >= 8) score++
  if (/[a-z]/.test(password)) score++
  if (/[A-Z]/.test(password)) score++
  if (/[0-9]/.test(password)) score++
  if (/[^a-zA-Z0-9]/.test(password)) score++
  
  if (score <= 2) {
    return { level: 'weak', text: '弱' }
  } else if (score <= 3) {
    return { level: 'medium', text: '中' }
  } else {
    return { level: 'strong', text: '强' }
  }
})

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

// 关闭客服模态框
const closeContactModal = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    showContactModal.value = false
  }
}

// 返回上一步
const goBack = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 处理步骤1：身份验证
const handleStep1 = async () => {
  if (!step1Form.value.phone.trim()) {
    ElMessage.warning('请输入手机号码')
    return
  }
  
  if (!/^1[3-9]\d{9}$/.test(step1Form.value.phone.trim())) {
    ElMessage.error('请输入正确的手机号码')
    return
  }
  
  loading.value = true
  
  try {
    // 模拟身份验证API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟验证成功，发送短信验证码
    ElMessage.success('验证码已发送至您的手机')
    currentStep.value = 2
    
    // 启动倒计时
    smsCountdown.value = 60
    const timer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (error) {
    ElMessage.error('手机号码验证失败，请检查后重试')
  } finally {
    loading.value = false
  }
}

// 处理步骤2：短信验证
const handleStep2 = async () => {
  if (!step2Form.value.smsCode.trim()) {
    ElMessage.warning('请输入短信验证码')
    return
  }
  
  if (step2Form.value.smsCode.trim().length !== 6) {
    ElMessage.warning('请输入6位验证码')
    return
  }
  
  loading.value = true
  
  try {
    // 模拟短信验证API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟验证成功
    ElMessage.success('验证码验证成功')
    currentStep.value = 3
    
  } catch (error) {
    ElMessage.error('验证码错误，请重新输入')
  } finally {
    loading.value = false
  }
}

// 处理步骤3：重置密码
const handleStep3 = async () => {
  if (!step3Form.value.password.trim()) {
    ElMessage.warning('请输入新密码')
    return
  }
  
  if (step3Form.value.password.trim().length < 6) {
    ElMessage.warning('密码至少需要6位')
    return
  }
  
  if (!step3Form.value.confirmPassword.trim()) {
    ElMessage.warning('请确认新密码')
    return
  }
  
  if (step3Form.value.password.trim() !== step3Form.value.confirmPassword.trim()) {
    ElMessage.warning('两次输入的密码不一致')
    return
  }
  
  loading.value = true
  
  try {
    // 模拟重置密码API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success('密码重置成功，请使用新密码登录')
    
    // 跳转到登录页面
    router.push('/login')
    
  } catch (error) {
    ElMessage.error('密码重置失败，请重试')
  } finally {
    loading.value = false
  }
}

// 重新发送短信验证码
const resendSmsCode = async () => {
  if (smsCountdown.value > 0) return
  
  try {
    // 模拟重新发送短信
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('验证码已重新发送')
    
    // 重新启动倒计时
    smsCountdown.value = 60
    const timer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (error) {
    ElMessage.error('发送失败，请重试')
  }
}
</script>

<style scoped>
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0D1B2A 0%, #1B365D 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(ellipse at 25% 20%, rgba(232, 184, 109, 0.08) 0%, transparent 60%),
    radial-gradient(ellipse at 75% 80%, rgba(199, 210, 221, 0.12) 0%, transparent 60%),
    radial-gradient(ellipse at 50% 50%, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(0.5deg); }
  66% { transform: translate(-20px, 20px) rotate(-0.5deg); }
}

.auth-card {
  background: rgba(255, 255, 255, 0.92);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 24px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  width: 100%;
  max-width: 520px;
  padding: 48px 40px;
  position: relative;
  z-index: 5;
  animation: cardSlideUp 0.8s ease-out;
}

@keyframes cardSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;
}

.auth-title {
  font-size: 28px;
  font-weight: 600;
  color: #0D1B2A;
  margin-bottom: 32px;
  line-height: 1.2;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(65, 90, 119, 0.1);
  color: #B8C5D1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.step.active .step-number {
  background: #415A77;
  color: white;
}

.step.completed .step-number {
  background: #1B365D;
  color: white;
}

.step-text {
  font-size: 12px;
  color: #B8C5D1;
  font-weight: 500;
  transition: color 0.3s ease;
}

.step.active .step-text,
.step.completed .step-text {
  color: #415A77;
}

.step-line {
  width: 60px;
  height: 2px;
  background: rgba(65, 90, 119, 0.1);
  margin: 0 16px;
  margin-bottom: 30px;
  transition: background 0.3s ease;
}

.step-line.active {
  background: #415A77;
}

/* 表单样式 */
.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 15px;
  font-weight: 500;
  color: #415A77;
}

.form-input {
  width: 100%;
  height: 48px;
  border: 2px solid rgba(65, 90, 119, 0.1);
  border-radius: 12px;
  padding: 0 16px;
  font-size: 16px;
  color: #0D1B2A;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #415A77;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 4px rgba(65, 90, 119, 0.1);
}

.form-input::placeholder {
  color: #B8C5D1;
}

/* 验证信息显示 */
.verification-info {
  text-align: center;
  margin-bottom: 32px;
  padding: 20px;
  background: rgba(65, 90, 119, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(65, 90, 119, 0.1);
}

.verification-info p {
  margin: 0;
  color: #415A77;
  font-size: 14px;
}

.phone-display {
  font-weight: 600;
  font-size: 16px;
  color: #0D1B2A !important;
  margin-top: 4px !important;
}

/* 密码容器 */
.password-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #778DA9;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
}

.password-toggle:hover {
  color: #415A77;
}

/* 验证码容器 */
.captcha-container {
  display: flex;
  gap: 12px;
}

.captcha-input {
  flex: 1;
}

.captcha-btn {
  width: 120px;
  height: 48px;
  background: linear-gradient(135deg, #E8B86D 0%, #B8C5D1 100%);
  border: none;
  border-radius: 12px;
  color: #0D1B2A;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.captcha-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 27, 42, 0.15);
}

.captcha-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 8px;
}

.password-strength-bar {
  height: 4px;
  background: rgba(65, 90, 119, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.password-strength-bar::after {
  content: '';
  display: block;
  height: 100%;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.password-strength-bar.weak::after {
  width: 33%;
  background: #f56565;
}

.password-strength-bar.medium::after {
  width: 66%;
  background: #ed8936;
}

.password-strength-bar.strong::after {
  width: 100%;
  background: #48bb78;
}

.password-strength-text {
  font-size: 12px;
  color: #B8C5D1;
}

/* 按钮样式 */
.submit-btn {
  width: 100%;
  height: 52px;
  background: linear-gradient(135deg, #1B365D 0%, #0D1B2A 100%);
  border: none;
  border-radius: 14px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(13, 27, 42, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 24px;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(13, 27, 42, 0.3);
}

.submit-btn:active {
  transform: translateY(0);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.back-btn {
  background: none;
  border: none;
  color: #415A77;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(65, 90, 119, 0.05);
  color: #0D1B2A;
}

/* 页面切换链接样式 */
.auth-switch {
  text-align: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid rgba(65, 90, 119, 0.1);
}

.auth-switch-text {
  color: #778DA9;
  font-size: 14px;
  margin-right: 8px;
}

.auth-switch-link {
  color: #415A77;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.auth-switch-link:hover {
  color: #0D1B2A;
  text-decoration: underline;
}

/* 加载状态 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 联系客服样式 */
.contact-service {
  text-align: left;
  margin-bottom: 24px;
  /* padding: 12px 0; */
}

.contact-text {
  color: #778DA9;
  font-size: 13px;
  margin-right: 6px;
}

.contact-btn {
  background: none;
  border: none;
  color: #415A77;
  font-size: 13px;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  /* text-decoration: underline;
  text-underline-offset: 2px; */
}

.contact-btn:hover {
  color: #0D1B2A;
  text-decoration: underline;
  text-underline-offset: 2px;
}

/* 模态框样式 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  width: 90%;
  max-width: 480px;
  max-height: 80vh;
  overflow-y: auto;
  animation: modalSlideUp 0.3s ease;
}

@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid rgba(65, 90, 119, 0.1);
  margin-bottom: 24px;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #0D1B2A;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #778DA9;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(65, 90, 119, 0.1);
  color: #415A77;
}

.modal-body {
  padding: 0 24px 24px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: rgba(65, 90, 119, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(65, 90, 119, 0.08);
}

.contact-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(65, 90, 119, 0.1);
  border-radius: 10px;
  flex-shrink: 0;
}

.contact-details {
  flex: 1;
}

.contact-label {
  font-size: 14px;
  color: #415A77;
  font-weight: 500;
  margin-bottom: 4px;
}

.contact-value {
  font-size: 16px;
  color: #0D1B2A;
  font-weight: 600;
  margin-bottom: 4px;
}

.contact-note {
  font-size: 12px;
  color: #778DA9;
}

.service-note {
  background: rgba(232, 184, 109, 0.1);
  border: 1px solid rgba(232, 184, 109, 0.2);
  border-radius: 12px;
  padding: 16px;
}

.service-note p {
  margin: 0;
  font-size: 14px;
  color: #415A77;
  line-height: 1.6;
}

.service-note p:first-child {
  margin-bottom: 8px;
}

.modal-footer {
  padding: 0 24px 24px;
  display: flex;
  justify-content: flex-end;
}

.btn-secondary {
  background: rgba(65, 90, 119, 0.1);
  border: none;
  border-radius: 10px;
  color: #415A77;
  font-size: 14px;
  font-weight: 500;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(65, 90, 119, 0.15);
  color: #0D1B2A;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-card {
    padding: 32px 24px;
    margin: 16px;
  }

  .auth-title {
    font-size: 24px;
  }

  .step-indicator {
    margin-bottom: 32px;
  }
  
  .step-line {
    width: 40px;
    margin: 0 12px;
    margin-bottom: 30px;
  }
  
  .captcha-container {
    flex-direction: column;
  }
  
  .captcha-btn {
    width: 100%;
  }

  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .contact-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}
</style>