<template>
  <el-dialog
    v-model="visible"
    title="消息中心"
    width="800"
    align-center
    :destroy-on-close="true"
    :modal="true"
    :append-to-body="true"
    class="message-center-dialog"
  >
    <div class="message-center-content">      
      <!-- 操作栏 -->
      <div class="action-bar">
        <div class="left-actions">
          <el-button 
            type="primary" 
            size="small" 
            @click="handleBatchMarkRead" 
            :disabled="selectedMessages.length === 0"
            :icon="Check"
          >
            批量已读
          </el-button>
          <!-- <el-button 
            type="success" 
            size="small" 
            @click="handleMarkAllRead"
            :disabled="unreadCount === 0"
            :icon="CircleCheck"
          >
            全部已读
          </el-button> -->
          <el-button 
            type="warning" 
            size="small" 
            @click="handleRefresh"
            :icon="Refresh"
          >
            刷新
          </el-button>
        </div>

        <div class="right-actions">
          <div class="stats-container">
            <div class="connection-status">
              <el-tag 
                :type="connectionStatusClass" 
                size="small"
                :icon="connectionStatusIcon"
              >
                {{ connectionStatusText }}
              </el-tag>
            </div>
            <p class="stats-text">
              未读 <span class="count-num-warn">{{ unreadCount }}</span> 条
            </p>
          </div>
        </div>
      </div>

      <!-- 消息列表 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="messageList"
          class="custom-table"
          row-key="id"
          @selection-change="handleSelectionChange"
          :row-class-name="getRowClassName"
          height="450"
        >
          <el-table-column type="selection" width="55" />
          
          <!-- 消息标题 -->
          <el-table-column label="消息内容" min-width="260">
            <template #default="{ row }">
              <div class="message-content">
                <div class="message-title" :class="{ 'unread-title': row.is_read === 0 }">
                  <el-icon v-if="row.is_urgent" class="urgent-icon">
                    <Warning />
                  </el-icon>
                  <div v-html="row.title"></div>
                </div>
                <div class="message-text" v-html="row.content"></div>
                <div class="message-meta">                
                  <span class="meta-item">
                    {{ row.time_ago }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <!-- 状态 -->
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.read_status_class" size="small">
                {{ row.read_status_text }}
              </el-tag>
            </template>
          </el-table-column>
          
          <!-- 接收时间 -->
          <el-table-column prop="created_at" label="接收时间" width="200">
            <template #default="{ row }">
              {{ formatTime(row.created_at) }}
            </template>
          </el-table-column>
          
          <!-- 操作 -->
          <el-table-column label="操作" fixed="right">
            <template #default="{ row }">
              <div class="operation-btns">
                <el-button 
                  v-if="row.is_read === 0"
                  type="success" 
                  size="small" 
                  @click.stop="handleMarkRead(row.id)"
                  :icon="Check"
                >
                  已读
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :total="pagination.total"
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 15, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check,
  CircleCheck,
  Refresh,
  Warning,
  Connection,
  Link
} from '@element-plus/icons-vue'
import { useRealtimeStore } from '@/stores/realtime'
import { 
  getRealtimeMessageList, 
  markMessageRead, 
  batchMarkMessagesRead, 
  markAllMessagesRead,
  getMessageTypeOptions,
  getUnreadMessageCount
} from '@/services/messageApi'
import type { RealtimeMessage } from '@/types/realtime'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Store
const realtimeStore = useRealtimeStore()

// 状态
const loading = ref(false)
const messageList = ref<RealtimeMessage[]>([])
const selectedMessages = ref<RealtimeMessage[]>([])
const messageTypeOptions = ref<Record<string, string>>({})

// 搜索表单
const searchForm = ref({
  message_type: '',
  is_read: 0 as number | undefined, // 默认显示未读消息
  title: '',
  dateRange: [] as string[]
})

// 分页
const pagination = ref({
  page: 1,
  page_size: 15,
  total: 0
})

// 统计信息
const totalCount = ref(0)
const unreadCount = ref(0)
const urgentCount = ref(0)

// 计算属性
const connectionStatusText = computed(() => {
  switch (realtimeStore.connectionStatus) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中'
    case 'disconnected':
      return '已断开'
    case 'reconnecting':
      return '重连中'
    case 'error':
      return '连接错误'
    default:
      return '未知'
  }
})

const connectionStatusClass = computed(() => {
  switch (realtimeStore.connectionStatus) {
    case 'connected':
      return 'success'
    case 'connecting':
    case 'reconnecting':
      return 'warning'
    case 'disconnected':
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
})

const connectionStatusIcon = computed(() => {
  return realtimeStore.isConnected ? Connection : Link
})

// 方法
const fetchMessages = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.value.page,
      page_size: pagination.value.page_size,
      sort_field: 'created_at',
      sort: 'desc',
      is_read: 0,
    } as any

    // 添加搜索条件
    if (searchForm.value.message_type) {
      params.message_type = searchForm.value.message_type
    }
    
    /* if (searchForm.value.is_read !== undefined) {
      params.is_read = searchForm.value.is_read
    } */
    
    if (searchForm.value.title) {
      params.title = searchForm.value.title
    }
    
    if (searchForm.value.dateRange && searchForm.value.dateRange.length === 2) {
      params.created_at = searchForm.value.dateRange
    }

    const response = await getRealtimeMessageList(params)
    
    messageList.value = response.itemList || []
    pagination.value.total = response.total || 0
    totalCount.value = response.total || 0
    
    // 获取全局未读数量（不只是当前页面的）
    const globalUnreadCount = await getUnreadMessageCount()
    unreadCount.value = globalUnreadCount
    
    // 计算当前页面的紧急消息数量
    urgentCount.value = messageList.value.filter(msg => msg.message_type === 'call_service' && msg.is_read === 0).length
    
  } catch (error) {
    console.error('获取消息列表失败:', error)
    ElMessage.error('获取消息列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.value.page = 1
  fetchMessages()
}

const resetSearch = () => {
  searchForm.value = {
    message_type: '',
    is_read: 0, // 重置时仍默认显示未读消息
    title: '',
    dateRange: []
  }
  handleSearch()
}

const handleSelectionChange = (selection: RealtimeMessage[]) => {
  selectedMessages.value = selection
}

const getRowClassName = ({ row }: { row: RealtimeMessage }) => {
  let className = ''
  if (row.is_read === 0) {
    className += 'unread-row '
  }
  if (row.message_type === 'call_service') {
    className += 'urgent-row '
  }
  return className.trim()
}

const getMessageIcon = (type: string) => {
  return undefined
}

const getPriorityClass = (priority: number) => {
  const classMap: Record<number, string> = {
    1: 'info',
    2: 'primary', 
    3: 'warning',
    4: 'danger'
  }
  return classMap[priority] || 'info'
}

const getPriorityText = (priority: number) => {
  const textMap: Record<number, string> = {
    1: '低',
    2: '中',
    3: '高', 
    4: '紧急'
  }
  return textMap[priority] || '未知'
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN')
}

const handleMarkRead = async (id: number) => {
  try {
    await markMessageRead(id)
    ElMessage.success('标记成功')
    
    // 同步到store
    realtimeStore.markMessageRead(id)
    
    // 重新获取全局未读数量并更新store
    const globalUnreadCount = await getUnreadMessageCount()
    realtimeStore.setUnreadCount(globalUnreadCount)
    
    // 重新获取消息列表（应用当前筛选条件）
    await fetchMessages()
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('标记已读失败')
  }
}

const handleBatchMarkRead = async () => {
  try {
    const ids = selectedMessages.value.map(msg => msg.id!).filter(id => id)
    if (ids.length === 0) {
      ElMessage.warning('请选择要标记的消息')
      return
    }
    
    await batchMarkMessagesRead(ids)
    ElMessage.success(`成功标记 ${ids.length} 条消息为已读`)
    
    // 同步到store
    realtimeStore.markMessagesRead(ids)
    
    // 重新获取全局未读数量并更新store
    const globalUnreadCount = await getUnreadMessageCount()
    realtimeStore.setUnreadCount(globalUnreadCount)
    
    // 清空选中项并重新获取消息列表（应用当前筛选条件）
    selectedMessages.value = []
    await fetchMessages()
  } catch (error) {
    console.error('批量标记已读失败:', error)
    ElMessage.error('批量标记已读失败')
  }
}

const handleMarkAllRead = async () => {
  try {
    await ElMessageBox.confirm('确定要将所有消息标记为已读吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await markAllMessagesRead()
    ElMessage.success('全部消息已标记为已读')
    
    // 同步到store
    realtimeStore.markAllRead()
    
    // 重新获取全局未读数量（应该为0）并更新store
    const globalUnreadCount = await getUnreadMessageCount()
    realtimeStore.setUnreadCount(globalUnreadCount)
    
    // 重新获取消息列表（应用当前筛选条件）
    await fetchMessages()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('全部标记已读失败:', error)
      ElMessage.error('全部标记已读失败')
    }
  }
}



const handleRefresh = () => {
  fetchMessages()
}

const handleSizeChange = (val: number) => {
  pagination.value.page_size = val
  pagination.value.page = 1
  fetchMessages()
}

const handleCurrentChange = (val: number) => {
  pagination.value.page = val
  fetchMessages()
}

// 初始化数据
const initializeData = async () => {
  try {
    // 获取消息类型选项
    messageTypeOptions.value = await getMessageTypeOptions()
    
    // 初始化实时系统
    await realtimeStore.initialize()
    
    // 获取消息列表
    await fetchMessages()
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

// 监听对话框显示状态，初始化数据
watch(visible, (newVal) => {
  if (newVal) {
    initializeData()
    // 确保对话框显示在最上层
    setTimeout(() => {
      const dialogElements = document.querySelectorAll('.el-dialog__wrapper')
      const overlayElements = document.querySelectorAll('.el-overlay')
      
      dialogElements.forEach(el => {
        if (el.querySelector('.message-center-dialog')) {
          ;(el as HTMLElement).style.zIndex = '10001'
        }
      })
      
      overlayElements.forEach(el => {
        if (el.querySelector('.message-center-dialog')) {
          ;(el as HTMLElement).style.zIndex = '10000'
        }
      })
    }, 100)
  }
})

// 监听实时消息更新
watch(() => realtimeStore.messages, () => {
  // 当store中的消息更新时，刷新页面数据
  fetchMessages()
}, { deep: true })
</script>

<style scoped>

.message-center-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}

.message-center-content {
  height: 100%;
}

.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.search-btns {
  margin-left: 10px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.center-actions {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 16px;
}

.stats-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.connection-status {
  display: flex;
  align-items: center;
}

.stats-text {
  font-size: 14px;
  color: #606266;
  margin: 0;
}

.count-num {
  font-weight: bold;
  color: #409eff;
  margin: 0 4px;
}

.count-num-warn {
  font-weight: bold;
  color: #e6a23c;
  margin: 0 4px;
}

.count-num-danger {
  font-weight: bold;
  color: #f56c6c;
  margin: 0 4px;
}

.urgent-count {
  color: #f56c6c;
}

.message-content {
  padding: 4px 0;
}

.message-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.unread-title {
  font-weight: bold;
  color: #303133;
}

.urgent-icon {
  color: #f56c6c;
}

.message-text {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  line-height: 1.4;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 11px;
  color: #909399;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.time-ago {
  margin-left: auto;
}

.operation-btns {
  display: flex;
  gap: 4px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

/* 表格行样式 */
:deep(.unread-row) {
  background-color: #f0f9ff;
}

:deep(.urgent-row) {
  border-left: 3px solid #f56c6c;
}

:deep(.urgent-row.unread-row) {
  background-color: #fef0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-bar .form-inline {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-bar .form-inline .el-form-item {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .action-bar {
    flex-direction: column;
    gap: 8px;
  }
  
  .center-actions {
    justify-content: flex-start;
    margin: 0;
  }
  
  .stats-container {
    flex-direction: column;
    gap: 8px;
  }
  
  .operation-btns {
    flex-direction: column;
  }
}
</style>