<template>
  <div class="member-level-chart">
    <!-- 图表加载状态 -->
    <div v-if="isLoading" class="chart-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>图表加载中...</span>
    </div>
    
    <!-- 无数据状态 -->
    <div v-else-if="!hasData" class="chart-empty">
      <el-icon><Warning /></el-icon>
      <span>暂无数据</span>
    </div>

    <!-- ECharts 饼图 -->
    <v-chart
      v-else
      ref="chartRef"
      :option="chartOption"
      :style="{ height: typeof height === 'number' ? height + 'px' : height }"
      autoresize
      @click="handleClick"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { Loading, Warning } from '@element-plus/icons-vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必要的组件
use([
  CanvasRenderer,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent
])

interface LevelData {
  name?: string
  value?: number
  level_name?: string
  member_count?: number
  level_id?: number
  percentage?: number
  color?: string
}

interface Props {
  data: LevelData[]
  height?: number | string
}

const props = withDefaults(defineProps<Props>(), {
  height: 300
})

const emit = defineEmits<{
  click: [params: any]
}>()

const chartRef = ref()
const isLoading = ref(true)

// 等级颜色映射
const levelColors = [
  '#5470c6', // 普通会员
  '#91cc75', // 银牌会员
  '#fac858', // 金牌会员  
  '#ee6666', // 钻石会员
  '#73c0de', // 至尊会员
  '#3ba272', // VIP会员
  '#fc8452', // 超级VIP
  '#9a60b4'  // 终身会员
]

// 监听数据变化，控制加载状态
watch(() => props.data, (newData) => {
  if (newData && newData.length > 0) {
    nextTick(() => {
      setTimeout(() => {
        isLoading.value = false
      }, 300)
    })
  } else {
    isLoading.value = true
  }
}, { immediate: true })

// 检查是否有数据
const hasData = computed(() => {
  return props.data && props.data.length > 0
})

// 处理图表数据，兼容多种数据格式
const processedData = computed(() => {
  if (!hasData.value) return []
  
  console.log('MemberLevelChart - 原始数据:', props.data)
  
  const processed = props.data.map((item, index) => ({
    name: item.name || item.level_name || `等级${item.level_id || index + 1}`,
    value: item.value || item.member_count || 0,
    itemStyle: {
      color: item.color || levelColors[index % levelColors.length]
    }
  }))
  
  console.log('MemberLevelChart - 处理后数据:', processed)
  return processed
})

// 计算总数
const totalCount = computed(() => {
  return processedData.value.reduce((sum, item) => sum + item.value, 0)
})

// ECharts配置选项
const chartOption = computed(() => {
  return {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const percentage = ((params.value / totalCount.value) * 100).toFixed(1)
        return `
          <div style="margin: 0px 0 0; line-height:1;">
            <div style="font-size:14px; color:#666; font-weight:400; line-height:1;">
              ${params.name}
            </div>
            <div style="margin: 10px 0 0; line-height:1;">
              <div style="margin: 0px 0 0; line-height:1;">
                <span style="display:inline-block; margin-right:4px; border-radius:10px; width:10px; height:10px; background-color:${params.color};"></span>
                <span style="font-size:14px; color:#666; font-weight:400; margin-left:2px">
                  会员数量
                </span>
                <span style="float:right; margin-left:20px; font-size:14px; color:#666; font-weight:900">
                  ${params.value} 人 (${percentage}%)
                </span>
                <div style="clear:both"></div>
              </div>
            </div>
          </div>
        `
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 20,
      bottom: 20,
      textStyle: {
        color: '#606266',
        fontSize: 12
      },
      formatter: (name: string) => {
        const item = processedData.value.find(d => d.name === name)
        if (item) {
          const percentage = ((item.value / totalCount.value) * 100).toFixed(1)
          return `${name} ${percentage}%`
        }
        return name
      }
    },
    series: [
      {
        name: '会员等级分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['35%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold',
            formatter: (params: any) => {
              const percentage = ((params.value / totalCount.value) * 100).toFixed(1)
              return `{a|${params.name}}\n{b|${params.value}人}\n{c|${percentage}%}`
            },
            rich: {
              a: {
                fontSize: 14,
                fontWeight: 'bold',
                color: '#303133',
                lineHeight: 20
              },
              b: {
                fontSize: 16,
                fontWeight: 'bold', 
                color: '#303133',
                lineHeight: 24
              },
              c: {
                fontSize: 12,
                color: '#909399',
                lineHeight: 16
              }
            }
          },
          scale: true,
          scaleSize: 10
        },
        labelLine: {
          show: false
        },
        data: processedData.value
      }
    ],
    animation: true,
    animationType: 'scale',
    animationEasing: 'elasticOut',
    animationDelay: (idx: number) => Math.random() * 200
  }
})

// 处理点击事件
const handleClick = (params: any) => {
  emit('click', params)
}
</script>

<style scoped lang="scss">
.member-level-chart {
  position: relative;
  
  .chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--el-text-color-secondary);
    z-index: 10;
    
    .el-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }
    
    span {
      font-size: 14px;
    }
  }

  .chart-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--el-text-color-secondary);
    
    .el-icon {
      font-size: 48px;
      margin-bottom: 8px;
      color: #C0C4CC;
    }
    
    span {
      font-size: 14px;
    }
  }
}
</style>