// 用户相关类型定义
export interface User {
  id: number
  username: string
  nickname: string
  avatar?: string
  role_id: number
  role_name: string
  status: number
  created_at: string
  login_time?: string
}

// 登录表单
export interface LoginForm {
  username: string
  password: string
  captcha: string
  remember: boolean
}

// 注册表单
export interface RegisterForm {
  companyName: string
  contactName: string
  phone: string
  smsCode: string
  password: string
  confirmPassword: string
  agreement: boolean
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 登录响应
export interface LoginResponse {
  token: string
  user: User
}