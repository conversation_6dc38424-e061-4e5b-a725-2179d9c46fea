<template>
  <div v-if="visible" class="modal-overlay" @click.self="handleCancel">
    <div class="modal-dialog modal-lg">
      <div class="modal-header">
        <h3 class="modal-title">{{ isEdit ? '编辑管理员' : '新增管理员' }}</h3>
        <button class="modal-close" @click="handleCancel">×</button>
      </div>
      <div class="modal-body">
        <form class="admin-form" @submit.prevent="handleSubmit">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">昵称 <span class="required">*</span></label>
              <input 
                type="text" 
                class="form-control" 
                v-model="formData.nickname" 
                placeholder="请输入管理员昵称"
                :class="{ 'error': errors.nickname }"
              >
              <div v-if="errors.nickname" class="error-message">{{ errors.nickname }}</div>
            </div>
            <div class="form-group">
              <label class="form-label">登录账号 <span class="required">*</span></label>
              <input 
                type="text" 
                class="form-control" 
                v-model="formData.username" 
                placeholder="请输入登录账号"
                :class="{ 'error': errors.username }"
              >
              <div v-if="errors.username" class="error-message">{{ errors.username }}</div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">
                登录密码 
                <span v-if="!isEdit" class="required">*</span>
              </label>
              <input 
                type="password" 
                class="form-control" 
                v-model="formData.password" 
                :placeholder="isEdit ? '留空则不修改密码' : '请输入登录密码'"
                :class="{ 'error': errors.password }"
              >
              <small v-if="isEdit" class="form-hint">留空则不修改原密码</small>
              <div v-if="errors.password" class="error-message">{{ errors.password }}</div>
            </div>
            <div class="form-group">
              <label class="form-label">所属角色 <span class="required">*</span></label>
              <select 
                class="form-control" 
                v-model="formData.role_id"
                :class="{ 'error': errors.role_id }"
              >
                <option value="">请选择角色</option>
                <option v-for="role in roleList" :key="role.id" :value="role.id">
                  {{ role.role_name }}
                </option>
              </select>
              <div v-if="errors.role_id" class="error-message">{{ errors.role_id }}</div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">头像URL</label>
              <input 
                type="url" 
                class="form-control" 
                v-model="formData.avatar" 
                placeholder="请输入头像链接(可选)"
              >
            </div>
            <div class="form-group">
              <label class="form-label">状态</label>
              <div class="radio-group">
                <label class="radio-item">
                  <input type="radio" :name="`adminStatus_${Date.now()}`" :value="1" v-model="formData.status">
                  <span class="radio-label">正常</span>
                </label>
                <label class="radio-item">
                  <input type="radio" :name="`adminStatus_${Date.now()}`" :value="0" v-model="formData.status">
                  <span class="radio-label">禁用</span>
                </label>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" @click="handleCancel">取消</button>
        <button type="button" class="btn btn-primary" @click="handleSubmit" :disabled="loading">
          <i class="icon">💾</i> 
          {{ loading ? '保存中...' : (isEdit ? '保存修改' : '保存管理员') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'

// 定义 Props
interface Props {
  visible: boolean
  adminData?: any
  roleList: Array<{ id: number; role_name: string }>
  existingUsernames?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  adminData: null,
  roleList: () => [],
  existingUsernames: () => []
})

// 定义 Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'submit', data: any): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive({
  id: null as number | null,
  nickname: '',
  username: '',
  password: '',
  avatar: '',
  role_id: '' as string | number,
  status: 1
})

// 表单验证错误
const errors = reactive({
  nickname: '',
  username: '',
  password: '',
  role_id: ''
})

// 加载状态
const loading = ref(false)

// 是否为编辑模式
const isEdit = computed(() => !!formData.id)

// 监听 adminData 变化，初始化表单
watch(() => props.adminData, (newData) => {
  if (newData) {
    // 编辑模式
    Object.assign(formData, {
      id: newData.id,
      nickname: newData.nickname,
      username: newData.username,
      password: '',
      avatar: newData.avatar || '',
      role_id: newData.role_id,
      status: newData.status
    })
  } else {
    // 新增模式
    resetForm()
  }
  // 清除错误信息
  clearErrors()
}, { immediate: true })

// 监听 visible 变化
watch(() => props.visible, (newVisible) => {
  if (!newVisible) {
    clearErrors()
  }
})

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    nickname: '',
    username: '',
    password: '',
    avatar: '',
    role_id: '' as string | number,
    status: 1
  })
}

// 清除错误信息
const clearErrors = () => {
  Object.assign(errors, {
    nickname: '',
    username: '',
    password: '',
    role_id: ''
  })
}

// 表单验证
const validateForm = (): boolean => {
  clearErrors()
  let isValid = true

  // 验证昵称
  if (!formData.nickname.trim()) {
    errors.nickname = '请输入管理员昵称'
    isValid = false
  }

  // 验证登录账号
  if (!formData.username.trim()) {
    errors.username = '请输入登录账号'
    isValid = false
  } else {
    // 检查用户名是否重复
    const existingUsernames = props.existingUsernames.filter(username => 
      username !== (props.adminData?.username || '')
    )
    if (existingUsernames.includes(formData.username)) {
      errors.username = '登录账号已存在，请更换'
      isValid = false
    }
  }

  // 验证密码（新增模式必填）
  if (!isEdit.value && !formData.password) {
    errors.password = '请输入登录密码'
    isValid = false
  } else if (formData.password && formData.password.length < 6) {
    errors.password = '密码长度至少6位'
    isValid = false
  }

  // 验证角色
  if (!formData.role_id) {
    errors.role_id = '请选择所属角色'
    isValid = false
  }

  return isValid
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true
  
  try {
    // 准备提交数据
    const submitData = { ...formData }
    
    // 如果是编辑模式且密码为空，则不包含密码字段
    if (isEdit.value && !submitData.password) {
      delete submitData.password
    }

    emit('submit', submitData)
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}
</script>

<style scoped>
/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(13, 27, 42, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(13, 27, 42, 0.25);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

.modal-lg {
  max-width: 800px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #F1F3F4;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #0D1B2A;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #778DA9;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #F1F3F4;
  color: #415A77;
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #F1F3F4;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式 */
.admin-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #415A77;
}

.required {
  color: #F56C6C;
}

.form-control {
  padding: 12px 16px;
  border: 1px solid #C7D2DD;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.form-control:focus {
  outline: none;
  border-color: #415A77;
  box-shadow: 0 0 0 3px rgba(65, 90, 119, 0.1);
}

.form-control.error {
  border-color: #F56C6C;
  box-shadow: 0 0 0 3px rgba(245, 108, 108, 0.1);
}

.form-control::placeholder {
  color: #B8C5D1;
}

.form-hint {
  font-size: 12px;
  color: #778DA9;
  margin-top: 4px;
}

.error-message {
  font-size: 12px;
  color: #F56C6C;
  margin-top: 4px;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 20px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

.radio-item input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: #415A77;
}

.radio-label {
  color: #415A77;
  user-select: none;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  gap: 6px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-primary {
  background: #415A77;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #1B365D;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(65, 90, 119, 0.3);
}

.btn-secondary {
  background: #C7D2DD;
  color: #415A77;
}

.btn-secondary:hover {
  background: #B8C5D1;
}

.icon {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-dialog {
    width: 95%;
    margin: 20px;
  }

  .modal-lg {
    max-width: none;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .radio-group {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .modal-body {
    padding: 16px;
  }

  .modal-header,
  .modal-footer {
    padding: 16px;
  }
}
</style>
