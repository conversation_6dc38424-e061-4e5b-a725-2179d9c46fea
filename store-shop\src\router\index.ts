import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '../components/Layout/index.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    /* redirect: () => {
      // 动态重定向到第一个有权限的菜单
      return '/table/business' // 默认路径，路由守卫会处理权限验证
    }, */
    children: [
      {
        path: 'table/business',
        name: 'TableBusiness',
        component: () => import('../views/table/business/index.vue'),
        meta: { title: '桌台营业', icon: 'table' }
      },
      {
        path: 'retail',
        name: 'Retail',
        component: () => import('../views/retail/index.vue'),
        meta: { title: '商品零售', icon: 'shopping-cart' }
      },
      {
        path: 'reservation',
        name: 'Reservation',
        component: () => import('../views/reservation/index.vue'),
        meta: { title: '预订管理', icon: 'calendar' }
      },
      {
        path: 'system',
        name: 'System',
        component: () => import('../views/Home.vue'),
        meta: { title: '系统概览', icon: 'setting' }
      }
    ]
  },
  // 会员管理
  {
    path: '/member',
    component: Layout,
    meta: { title: '会员管理', icon: 'user' },
    children: [
      {
        path: 'overview',
        name: 'MemberOverview',
        component: () => import('../views/member/overview/index.vue'),
        meta: { title: '会员概览' }
      },
      {
        path: 'users',
        name: 'MemberUsers',
        component: () => import('../views/member/users/index.vue'),
        meta: { title: '会员管理' }
      },
      {
        path: 'points',
        name: 'PointsAcquisition',
        component: () => import('../views/member/points/index.vue'),
        meta: { title: '积分获取规则' }
      },
      {
        path: 'deduction',
        name: 'PointsDeduction',
        component: () => import('../views/member/points_deduction/index.vue'),
        meta: { title: '积分抵现规则' }
      },
      {
        path: 'user-level',
        name: 'UserLevel',
        component: () => import('../views/member/user_level/index.vue'),
        meta: { title: '会员等级管理' }
      },
      {
        path: 'recharge-package',
        name: 'RechargePackage',
        component: () => import('../views/member/recharge_package/index.vue'),
        meta: { title: '充值套餐管理' }
      },
      {
        path: 'points-log',
        name: 'UserPointsLog',
        component: () => import('../views/member/points_log/index.vue'),
        meta: { title: '积分变动' }
      },
      {
        path: 'balance-log',
        name: 'UserBalanceLog',
        component: () => import('../views/member/balance_log/index.vue'),
        meta: { title: '余额明细' }
      }
    ]
  },
  // 商品管理
  {
    path: '/goods',
    component: Layout,
    meta: { title: '商品管理', icon: 'goods' },
    children: [
      {
        path: 'index',
        name: 'GoodsManagement',
        component: () => import('../views/goods/index.vue'),
        meta: { title: '商品管理' }
      },
      {
        path: 'category',
        name: 'GoodsCategory',
        component: () => import('../views/goods/category/index.vue'),
        meta: { title: '商品分类' }
      },
      {
        path: 'accessories-category',
        name: 'AccessoriesCategory',
        component: () => import('../views/goods/category/accessories/index.vue'),
        meta: { title: '配品分类' }
      },
      {
        path: 'accessories-goods',
        name: 'AccessoriesGoods',
        component: () => import('../views/goods/category/accessories/goods/index.vue'),
        meta: { title: '配品管理' }
      },
      {
        path: 'remark-label',
        name: 'GoodsRemarkLabel',
        component: () => import('../views/goods/remark_label/index.vue'),
        meta: { title: '标签管理' }
      },
      {
        path: 'specification-classification',
        name: 'SpecificationClassification',
        component: () => import('../views/goods/specification_classification/index.vue'),
        meta: { title: '规格分类' }
      },
      {
        path: 'specification',
        name: 'Specification',
        component: () => import('../views/goods/specification/index.vue'),
        meta: { title: '规格管理' }
      },
      {
        path: 'set-meal',
        name: 'SetMeal',
        component: () => import('../views/goods/set_meal/index.vue'),
        meta: { title: '套餐管理' }
      }
    ]
  },
  {
    path: '/settings',
    component: Layout,
    meta: { title: '系统设置', icon: 'setting' },
    children: [
      {
        path: 'payment',
        name: 'PaymentSettings',
        redirect: '/settings/payment/method',
        meta: { title: '支付设置', noActiveMenu: true },
        children: [
          {
            path: 'method',
            name: 'PaymentMethod',
            component: () => import('../views/settings/payment/method/index.vue'),
            meta: {
              title: '收款方式设置',
              activeMenu: '/settings/payment/method'
            }
          }
        ]
      },
      {
        path: 'billing',
        name: 'BillingSettings',
        redirect: '/settings/billing/table',
        meta: { title: '计费规则设置', noActiveMenu: true },
        children: [
          {
            path: 'table',
            name: 'TableBillingRule',
            component: () => import('../views/settings/billing/table/index.vue'),
            meta: {
              title: '桌台计费规则',
              activeMenu: '/settings/billing/table'
            }
          },
          {
            path: 'service',
            name: 'ServiceBillingRule',
            component: () => import('../views/settings/billing/service/index.vue'),
            meta: {
              title: '服务费规则',
              activeMenu: '/settings/billing/service'
            }
          }
        ]
      },
      {
        path: 'table',
        name: 'TableSettings',
        component: () => import('../views/settings/table/index.vue'),
        meta: { title: '桌台设置' }
      },
      {
        path: 'print',
        name: 'PrintSettings',
        redirect: '/settings/print/template',
        meta: { title: '打印设置', noActiveMenu: true },
        children: [
          {
            path: 'template',
            name: 'PrintTemplate',
            component: () => import('../views/settings/print/template/index.vue'),
            meta: {
              title: '打印模板管理',
              activeMenu: '/settings/print/template'
            }
          },
          {
            path: 'config',
            name: 'PrintConfig',
            component: () => import('../views/settings/print/config/index.vue'),
            meta: {
              title: '打印机设置',
              activeMenu: '/settings/print/config'
            }
          },
          {
            path: 'auto-print',
            name: 'AutoPrintConfig',
            component: () => import('../views/settings/print/AutoPrintConfig.vue'),
            meta: {
              title: '自动打印配置',
              activeMenu: '/settings/print/auto-print'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/table',
    component: Layout,
    meta: { title: '桌台管理', icon: 'table' },
    children: [
      {
        path: 'classification',
        name: 'TableClassification',
        component: () => import('../views/table/classification/index.vue'),
        meta: { title: '桌台分类' }
      },
      {
        path: 'index',
        name: 'TableManagement',
        component: () => import('../views/table/index.vue'),
        meta: { title: '桌台管理' }
      }
    ]
  },
  {
    path: '/shop',
    component: Layout,
    meta: { title: '门店管理', icon: 'shop' },
    children: [
      {
        path: 'list',
        name: 'ShopList',
        component: () => import('../views/Home.vue'),
        meta: { title: '门店列表' }
      },
      {
        path: 'settings',
        name: 'ShopSettings',
        component: () => import('../views/Home.vue'),
        meta: { title: '门店设置' }
      },
      {
        path: 'devices',
        name: 'ShopDevices',
        component: () => import('../views/Home.vue'),
        meta: { title: '智能设备管理' }
      }
    ]
  },
  // 库存管理
  {
    path: '/inventory',
    component: Layout,
    meta: { title: '库存管理', icon: 'Box' },
    children: [
      {
        path: 'index',
        name: 'InventoryManagement',
        component: () => import('../views/inventory/index.vue'),
        meta: { title: '库存概览' }
      },
      {
        path: 'check',
        name: 'InventoryCheck',
        component: () => import('../views/inventory/check/index.vue'),
        meta: { title: '库存盘点' }
      },
      {
        path: 'warning',
        name: 'InventoryWarning',
        component: () => import('../views/inventory/warning/index.vue'),
        meta: { title: '库存预警' }
      },
      {
        path: 'log',
        name: 'InventoryLog',
        component: () => import('../views/inventory/log/index.vue'),
        meta: { title: '变动记录' }
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    meta: { title: '订单管理', icon: 'Document' },
    children: [
      {
        path: 'table',
        name: 'TableOrderManagement',
        component: () => import('../views/order/table/index.vue'),
        meta: { title: '桌台订单' }
      },
      {
        path: 'retail',
        name: 'RetailOrderManagement',
        component: () => import('../views/order/retail/index.vue'),
        meta: { title: '零售订单' }
      }
    ]
  },
  {
    path: '/staff',
    component: Layout,
    meta: { title: '权限管理', icon: 'user-filled' },
    children: [
      {
        path: 'list',
        name: 'StaffList',
        component: () => import('../views/staff/list/index.vue'),
        meta: { title: '管理员管理' }
      },
      {
        path: 'role',
        name: 'StaffRole',
        component: () => import('../views/staff/role/index.vue'),
        meta: { title: '角色管理' }
      }
    ]
  },
  // 营销促销系统路由
  {
    path: '/marketing',
    component: Layout,
    meta: { title: '营销促销', icon: 'ShoppingCart' },
    children: [
      {
        path: 'coupon',
        name: 'CouponManagement',
        component: () => import('../views/marketing/coupon/index.vue'),
        meta: { title: '优惠券管理' }
      },
      {
        path: 'activity',
        name: 'ActivityManagement',
        component: () => import('../views/marketing/activity/index.vue'),
        meta: { title: '营销活动' }
      }
    ]
  },

  // 通知消息系统路由
  {
    path: '/message',
    component: Layout,
    meta: { title: '消息中心', icon: 'BellFilled' },
    children: [
      {
        path: 'sms-template',
        name: 'SmsTemplateManagement',
        component: () => import('../views/message/sms/template/index.vue'),
        meta: { title: '短信模板管理' }
      },
      {
        path: 'sms-log',
        name: 'SmsLogManagement',
        component: () => import('../views/message/sms/log/index.vue'),
        meta: { title: '短信发送记录' }
      },
      {
        path: 'system-message',
        name: 'SystemMessageManagement',
        component: () => import('../views/message/system/index.vue'),
        meta: { title: '系统消息管理' }
      },
      {
        path: 'message-center',
        name: 'MessageCenter',
        component: () => import('../views/message/center/index.vue'),
        meta: { title: '消息中心' }
      },
      {
        path: 'setting',
        name: 'MessageSettingManagement',
        component: () => import('../views/message/setting/index.vue'),
        meta: { title: '消息推送设置' }
      }
    ]
  },

  // 财务报表系统路由
  {
    path: '/finance',
    component: Layout,
    meta: { title: '财务报表', icon: 'TrendCharts' },
    children: [
      {
        path: 'dashboard',
        name: 'FinanceReport',
        component: () => import('../views/finance/FinanceReport.vue'),
        meta: { title: '财务报表概览' }
      },
      {
        path: 'reports',
        name: 'FinanceReports',
        meta: { title: '详细报表', noActiveMenu: true },
        children: [
          {
            path: 'business-overview',
            name: 'BusinessOverviewDetail',
            component: () => import('../views/finance/reports/BusinessOverviewDetail.vue'),
            meta: {
              title: '营业概况',
              activeMenu: '/finance/reports/business-overview'
            }
          },

          {
            path: 'monthly-report',
            name: 'MonthlyReportDetail',
            component: () => import('../views/finance/reports/MonthlyReportDetail.vue'),
            meta: {
              title: '月营业报表',
              activeMenu: '/finance/reports/monthly-report'
            }
          },
          {
            path: 'goods-ranking',
            name: 'GoodsRankingDetail',
            component: () => import('../views/finance/reports/GoodsRankingDetail.vue'),
            meta: {
              title: '商品销售排行',
              activeMenu: '/finance/reports/goods-ranking'
            }
          },
          {
            path: 'staff-performance',
            name: 'StaffPerformanceDetail',
            component: () => import('../views/finance/reports/StaffPerformanceDetail.vue'),
            meta: {
              title: '员工业绩统计',
              activeMenu: '/finance/reports/staff-performance'
            }
          },
          {
            path: 'profit-analysis',
            name: 'ProfitAnalysisDetail',
            component: () => import('../views/finance/reports/ProfitAnalysisDetail.vue'),
            meta: {
              title: '利润分析',
              activeMenu: '/finance/reports/profit-analysis'
            }
          },
          {
            path: 'cost-analysis',
            name: 'CostAnalysisDetail',
            component: () => import('../views/finance/reports/CostAnalysisDetail.vue'),
            meta: {
              title: '成本分析',
              activeMenu: '/finance/reports/cost-analysis'
            }
          },
          {
            path: 'payment-analysis',
            name: 'PaymentAnalysisDetail',
            component: () => import('../views/finance/reports/PaymentAnalysisDetail.vue'),
            meta: {
              title: '支付方式分析',
              activeMenu: '/finance/reports/payment-analysis'
            }
          }
        ]
      }
    ]
  },
  // 客户服务系统路由
  {
    path: '/customer-service',
    component: Layout,
    meta: { title: '客户服务', icon: 'Phone' },
    children: [
      {
        path: 'feedback',
        name: 'CustomerServiceFeedback',
        component: () => import('../views/customer-service/feedback/index.vue'),
        meta: { title: '意见反馈', requiresAuth: false }
      }
    ]
  },
  // 测试路由（开发环境使用）
  {
    path: '/test',
    component: Layout,
    meta: { title: '测试工具', icon: 'Tools' },
    children: [
      {
        path: 'auto-print',
        name: 'AutoPrintTest',
        component: () => import('../views/test/AutoPrintTestPage.vue'),
        meta: { title: '自动打印测试' }
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('../views/403.vue'),
    meta: { requiresAuth: false }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  // 从localStorage获取token
  const token = localStorage.getItem('token')

  // 如果要访问的是登录页面或注册页面
  if (to.path === '/login' || to.path === '/register') {
    // 如果已登录，则重定向到首页
    if (token) {
      next({ path: '/' })
    } else {
      next()
    }
    return
  }

  // 如果要访问的不是登录页面或注册页面
  if (!token) {
    // 未登录，清理所有认证数据（包括WebSocket连接）并重定向到登录页
    try {
      const { clearAllAuthData } = await import('@/utils/authUtils')
      await clearAllAuthData()
    } catch (error) {
      console.error('清理认证数据失败:', error)
    }
    
    next({ path: '/login' })
    return
  }

  // 已登录，进行权限验证
  try {
    // 动态导入权限管理
    const { usePermission } = await import('@/composables/usePermission')
    const { useMenuStore } = await import('@/stores/menu')
    
    const { hasPermission, isInitialized, getFirstAccessibleMenuPath } = usePermission()
    const menuStore = useMenuStore()
    
    // 如果权限未初始化，先初始化
    if (!isInitialized.value) {
      try {
        await menuStore.fetchUserMenuPermissions()
        
        // 设置权限到权限管理器
        const { setUserPermissions } = usePermission()
        setUserPermissions({
          menus: menuStore.userMenus,
          permissions: menuStore.userPermissions
        })

        // 初始化实时消息系统（确保用户已登录且权限已初始化）
        try {
          const { useRealtimeStore } = await import('@/stores/realtime')
          const realtimeStore = useRealtimeStore()
          
          // 检查是否已经连接，避免重复初始化
          if (!realtimeStore.isConnected) {
            await realtimeStore.initialize()
            console.log('路由守卫：实时消息系统已初始化')
          }
        } catch (realtimeError) {
          console.error('初始化实时消息系统失败:', realtimeError)
          // WebSocket初始化失败不影响页面访问，只记录错误
        }
      } catch (error) {
        console.error('初始化权限失败:', error)
        // 权限初始化失败，清理权限数据并跳转到登录页
        try {
          const { clearUserAuthData } = await import('@/utils/authUtils')
          await clearUserAuthData()
        } catch (clearError) {
          console.error('清理权限数据失败:', clearError)
        }
        
        next({ path: '/login' })
        return
      }
    }
    // 如果访问的是根路径，动态跳转到第一个有权限的菜单
    if (to.path === '/') {
      const firstAccessiblePath = getFirstAccessibleMenuPath()
      if (firstAccessiblePath) {
        next({ path: firstAccessiblePath })
      } else {
        // 没有任何可访问的菜单，跳转到无权限页面
        next({ path: '/403' })
      }
      return
    }

    // 检查路由权限
    if (to.meta?.requiresAuth !== false) {
      // 根据路由路径推断需要的权限
      const requiredPermission = getPermissionByRoute(to.path)
      
      if (requiredPermission && !hasPermission(requiredPermission)) {
        // 没有权限，跳转到第一个有权限的页面
        const firstAccessiblePath = getFirstAccessibleMenuPath()
        
        if (firstAccessiblePath) {
          next({ path: firstAccessiblePath })
        } else {
          // 没有任何可访问的菜单，跳转到无权限页面
          next({ path: '/403' })
        }
        return
      }
    }

    // 权限验证通过，允许访问
    next()
  } catch (error) {
    console.error('路由守卫执行失败:', error)
    next({ path: '/login' })
  }
})

/**
 * 根据路由路径获取需要的权限
 */
function getPermissionByRoute(path: string): string | null {
  const routePermissionMap: Record<string, string> = {
    '/table/business': 'table_business',
    '/retail': 'retail',
    '/reservation': 'reservation',
    '/member/overview': 'member_overview',
    '/member/users': 'member_users',
    '/member/user-level': 'member_level',
    '/member/recharge-package': 'member_recharge',
    '/member/points': 'member_points_get',
    '/member/deduction': 'member_points_use',
    '/member/balance-log': 'member_balance',
    '/member/points-log': 'member_points',
    '/order/table': 'order_table',
    '/order/retail': 'order_retail',
    '/finance/reports/business-overview': 'finance_overview',
    '/finance/reports/monthly-report': 'finance_monthly',
    '/finance/reports/goods-ranking': 'finance_goods_rank',
    '/finance/reports/staff-performance': 'finance_staff',
    '/finance/reports/profit-analysis': 'finance_profit',
    '/marketing/coupon': 'coupon',
    '/marketing/activity': 'marketing_activity',
    '/inventory/index': 'inventory_overview',
    '/inventory/check': 'inventory_check',
    '/inventory/log': 'inventory_log',
    '/message/message-center': 'message_center',
    '/table/classification': 'table_category',
    '/table/index': 'table_manage',
    '/settings/table': 'table_config',
    '/goods/category': 'goods_category',
    '/goods/index': 'goods_manage',
    '/goods/set-meal': 'goods_package',
    '/goods/accessories-category': 'goods_material_category',
    '/goods/accessories-goods': 'goods_material',
    '/goods/remark-label': 'goods_tag',
    '/goods/specification-classification': 'goods_spec_category',
    '/goods/specification': 'goods_spec',
    '/settings/payment/method': 'payment_method',
    '/settings/billing/table': 'table_billing',
    '/settings/billing/service': 'service_fee',
    '/settings/print/template': 'print_template',
    '/settings/print/config': 'printer_config',
    '/staff/list': 'staff_admin',
    '/staff/role': 'staff_role',
    '/customer-service/feedback': 'customer_service_feedback'
  }

  return routePermissionMap[path] || null
}

export default router
