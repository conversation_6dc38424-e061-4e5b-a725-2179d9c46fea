<template>
  <div class="app-sidebar">
    <el-menu
      class="sidebar-menu"
      :default-active="activeMenu"
      :collapse="isCollapse"
      :unique-opened="true"
      background-color="#f5f7f9"
      text-color="#333"
      active-text-color="var(--el-color-primary)"
      @select="handleMenuSelect"
    >
        <!-- 收银台 -->
        <el-sub-menu index="desk">
            <template #title>
                <el-icon><platform /></el-icon>
                <span>收银台</span>
            </template>
            <el-menu-item index="table-business">桌台营业</el-menu-item>
            <el-menu-item index="retail">商品零售</el-menu-item>
            <el-menu-item index="reservation">预订管理</el-menu-item>
        </el-sub-menu>



      <!-- 会员管理 -->
      <el-sub-menu index="member">
        <template #title>
          <el-icon><UserFilled /></el-icon>
          <span>会员</span>
        </template>
        <el-menu-item index="member-users">会员管理</el-menu-item>
        <el-menu-item index="member-user-level">会员等级管理</el-menu-item>
        <el-menu-item index="member-recharge-package">充值套餐管理</el-menu-item>
        <el-menu-item index="member-points">积分获取规则</el-menu-item>
        <el-menu-item index="member-deduction">积分抵现规则</el-menu-item>
        <el-menu-item index="member-balance-log">余额明细</el-menu-item>
        <el-menu-item index="member-points-log">积分明细</el-menu-item>
      </el-sub-menu>

      <!-- 订单管理 -->
      <el-sub-menu index="order">
        <template #title>
          <el-icon><List /></el-icon>
          <span>账单</span>
        </template>
        <el-menu-item index="order-table">桌台账单</el-menu-item>
        <el-menu-item index="order-retail">零售账单</el-menu-item>
      </el-sub-menu>

      <!-- 财务报表 -->
      <el-sub-menu index="finance">
        <template #title>
          <el-icon><TrendCharts /></el-icon>
          <span>报表</span>
        </template>
        <el-menu-item index="finance-business-overview">营业概况</el-menu-item>

        <el-menu-item index="finance-monthly-report">月营业报表</el-menu-item>
        <el-menu-item index="finance-goods-ranking">商品销售排行</el-menu-item>
        <el-menu-item index="finance-staff-performance">员工业绩统计</el-menu-item>
        <el-menu-item index="finance-profit-analysis">利润分析</el-menu-item>
<!--        <el-menu-item index="finance-cost-analysis" @click="navigateTo('/finance/reports/cost-analysis')">成本分析</el-menu-item>
        <el-menu-item index="finance-payment-analysis" @click="navigateTo('/finance/reports/payment-analysis')">支付方式分析</el-menu-item>-->
      </el-sub-menu>

      <!-- 营销促销 -->
      <el-sub-menu index="marketing">
        <template #title>
          <el-icon><Ticket /></el-icon>
          <span>营销</span>
        </template>
        <el-menu-item index="marketing-coupon">优惠券</el-menu-item>
        <el-menu-item index="marketing-activity">营销活动</el-menu-item>
      </el-sub-menu>

      <!-- 库存管理 -->
      <el-sub-menu index="inventory">
        <template #title>
          <el-icon><Box /></el-icon>
          <span>库存</span>
        </template>
        <el-menu-item index="inventory-index">库存概览</el-menu-item>
        <el-menu-item index="inventory-check">库存盘点</el-menu-item>
        <!-- <el-menu-item index="inventory-warning" @click="navigateTo('/inventory/warning')">库存预警</el-menu-item> -->
        <el-menu-item index="inventory-log">变动记录</el-menu-item>
      </el-sub-menu>

      <!-- 通知消息 -->
      <el-sub-menu index="message">
        <template #title>
          <el-icon><BellFilled /></el-icon>
          <span>消息</span>
        </template>
        <!-- <el-menu-item index="message-sms-template" @click="navigateTo('/message/sms-template')">短信模板管理</el-menu-item>
        <el-menu-item index="message-sms-log" @click="navigateTo('/message/sms-log')">短信发送记录</el-menu-item> -->
        <!-- <el-menu-item index="message-system-message" @click="navigateTo('/message/system-message')">系统消息管理</el-menu-item> -->
        <el-menu-item index="message-center">消息中心</el-menu-item>
        <!-- <el-menu-item index="message-setting" @click="navigateTo('/message/setting')">消息推送设置</el-menu-item> -->
      </el-sub-menu>



      <!-- 系统设置 -->
      <el-sub-menu index="settings">
        <template #title>
          <el-icon><Tools /></el-icon>
          <span>设置</span>
        </template>

          <!-- 桌台管理 -->
          <el-sub-menu index="table">
              <template #title>
                  <span>桌台设置</span>
              </template>
              <el-menu-item index="table-classification">桌台分类</el-menu-item>
              <el-menu-item index="table-management">桌台管理</el-menu-item>
              <el-menu-item index="settings-table">桌台设置</el-menu-item>
          </el-sub-menu>
          <!-- 商品管理 -->
          <el-sub-menu index="goods">
              <template #title>
                  <span>商品设置</span>
              </template>
              <el-menu-item index="goods-category">商品分类</el-menu-item>
              <el-menu-item index="goods-index">商品管理</el-menu-item>
              <el-menu-item index="goods-set-meal">套餐管理</el-menu-item>
              <el-menu-item index="goods-accessories-category">配品分类</el-menu-item>
              <el-menu-item index="goods-accessories-goods">配品管理</el-menu-item>
              <el-menu-item index="goods-remark-label">标签管理</el-menu-item>
              <el-menu-item index="goods-specification-classification">规格分类管理</el-menu-item>
              <el-menu-item index="goods-specification">规格管理</el-menu-item>
          </el-sub-menu>

        <!-- 支付设置 -->
        <el-sub-menu index="payment">
          <template #title>
            <span>支付设置</span>
          </template>
          <el-menu-item index="payment-method">收款方式设置</el-menu-item>
        </el-sub-menu>

        <!-- 计费规则设置 -->
        <el-sub-menu index="billing">
          <template #title>
            <span>计费规则设置</span>
          </template>
          <el-menu-item index="billing-table">桌台计费规则</el-menu-item>
          <el-menu-item index="billing-service">服务费规则</el-menu-item>
        </el-sub-menu>

        <!-- 打印设置 -->
        <el-sub-menu index="print">
          <template #title>
            <span>打印设置</span>
          </template>
          <el-menu-item index="print-template">打印模板管理</el-menu-item>
          <el-menu-item index="print-config">打印机设置</el-menu-item>
        </el-sub-menu>
      </el-sub-menu>

      <!-- 权限管理 -->
      <el-sub-menu index="staff">
        <template #title>
          <el-icon><Stamp /></el-icon>
          <span>权限</span>
        </template>
        <el-menu-item index="staff-list">管理员管理</el-menu-item>
        <el-menu-item index="staff-role">角色管理</el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  UserFilled,
  Platform,
  TrendCharts, List, Ticket, Tools, Stamp, BellFilled, Box
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const isCollapse = ref(false)

// 定义类型接口
interface PathIndexMap {
  [key: string]: string;
}

// 菜单路径映射到唯一索引
const pathIndexMap: PathIndexMap = {
  '/table/business': 'table-business',
  '/retail': 'retail',
  '/reservation': 'reservation',
  '/order/table': 'order-table',
  '/order/retail': 'order-retail',
  '/settings/payment/method': 'payment-method',
  '/settings/billing/table': 'billing-table',
  '/settings/billing/service': 'billing-service',
  '/settings/print/template': 'print-template',
  '/settings/print/config': 'print-config',
  '/settings/table': 'settings-table',
  '/staff/list': 'staff-list',
  '/staff/role': 'staff-role',
  '/table/classification': 'table-classification',
  '/table/index': 'table-management',
  '/member/points': 'member-points',
  '/member/deduction': 'member-deduction',
  '/member/user-level': 'member-user-level',
  '/member/recharge-package': 'member-recharge-package',
  '/member/users': 'member-users',
  '/member/points-log': 'member-points-log',
  '/member/balance-log': 'member-balance-log',
  '/goods/index': 'goods-index',
  '/goods/category': 'goods-category',
  '/goods/accessories-category': 'goods-accessories-category',
  '/goods/accessories-goods': 'goods-accessories-goods',
  '/goods/remark-label': 'goods-remark-label',
  '/goods/specification-classification': 'goods-specification-classification',
  '/goods/specification': 'goods-specification',
  '/goods/set-meal': 'goods-set-meal',
  '/finance/reports/business-overview': 'finance-business-overview',
  '/finance/reports/daily-report': 'finance-daily-report',
  '/finance/reports/monthly-report': 'finance-monthly-report',
  '/finance/reports/goods-ranking': 'finance-goods-ranking',
  '/finance/reports/staff-performance': 'finance-staff-performance',
  '/finance/reports/profit-analysis': 'finance-profit-analysis',
  '/finance/reports/cost-analysis': 'finance-cost-analysis',
  '/finance/reports/payment-analysis': 'finance-payment-analysis',
  '/marketing/coupon': 'marketing-coupon',
  '/marketing/activity': 'marketing-activity',
  '/inventory/index': 'inventory-index',
  '/inventory/check': 'inventory-check',
  '/inventory/warning': 'inventory-warning',
  '/inventory/log': 'inventory-log',
  '/message/sms-template': 'message-sms-template',
  '/message/sms-log': 'message-sms-log',
  '/message/system-message': 'message-system-message',
  '/message/message-center': 'message-center',
  '/message/setting': 'message-setting'
}

// 反向映射，用于确定父级菜单
const menuParentMap: PathIndexMap = {
  'table-business': '',
  'retail': '',
  'reservation': '',
  'order-table': 'order',
  'order-retail': 'order',
  'payment-method': 'payment',
  'billing-table': 'billing',
  'billing-service': 'billing',
  'print-template': 'print',
  'print-config': 'print',
  'settings-table': 'table',
  'staff-list': 'staff',
  'staff-role': 'staff',
  'table-classification': 'table',
  'table-management': 'table',
  'member-points': 'member',
  'member-deduction': 'member',
  'member-user-level': 'member',
  'member-recharge-package': 'member',
  'member-users': 'member',
  'member-points-log': 'member',
  'member-balance-log': 'member',
  'goods-index': 'goods',
  'goods-category': 'goods',
  'goods-accessories-category': 'goods',
  'goods-accessories-goods': 'goods',
  'goods-remark-label': 'goods',
  'goods-specification-classification': 'goods',
  'goods-specification': 'goods',
  'goods-set-meal': 'goods',
  'finance-business-overview': 'finance',
  'finance-daily-report': 'finance',
  'finance-monthly-report': 'finance',
  'finance-goods-ranking': 'finance',
  'finance-staff-performance': 'finance',
  'finance-profit-analysis': 'finance',
  'finance-cost-analysis': 'finance',
  'finance-payment-analysis': 'finance',
  'marketing-coupon': 'marketing',
  'marketing-activity': 'marketing',
  'inventory-index': 'inventory',
  'inventory-check': 'inventory',
  'inventory-warning': 'inventory',
  'inventory-log': 'inventory',
  'message-sms-template': 'message',
  'message-sms-log': 'message',
  'message-system-message': 'message',
  'message-center': 'message',
  'message-setting': 'message'
}

// 处理菜单选择事件
const handleMenuSelect = (index: string) => {
  // 根据菜单索引获取对应的路径
  const indexPathMap: Record<string, string> = {
    'table-business': '/table/business',
    'retail': '/retail',
    'reservation': '/reservation',
    'order-table': '/order/table',
    'order-retail': '/order/retail',
    'payment-method': '/settings/payment/method',
    'billing-table': '/settings/billing/table',
    'billing-service': '/settings/billing/service',
    'print-template': '/settings/print/template',
    'print-config': '/settings/print/config',
    'settings-table': '/settings/table',
    'staff-list': '/staff/list',
    'staff-role': '/staff/role',
    'table-classification': '/table/classification',
    'table-management': '/table/index',
    'member-points': '/member/points',
    'member-deduction': '/member/deduction',
    'member-user-level': '/member/user-level',
    'member-recharge-package': '/member/recharge-package',
    'member-users': '/member/users',
    'member-points-log': '/member/points-log',
    'member-balance-log': '/member/balance-log',
    'goods-index': '/goods/index',
    'goods-category': '/goods/category',
    'goods-accessories-category': '/goods/accessories-category',
    'goods-accessories-goods': '/goods/accessories-goods',
    'goods-remark-label': '/goods/remark-label',
    'goods-specification-classification': '/goods/specification-classification',
    'goods-specification': '/goods/specification',
    'goods-set-meal': '/goods/set-meal',
    'finance-business-overview': '/finance/reports/business-overview',
    'finance-daily-report': '/finance/reports/daily-report',
    'finance-monthly-report': '/finance/reports/monthly-report',
    'finance-goods-ranking': '/finance/reports/goods-ranking',
    'finance-staff-performance': '/finance/reports/staff-performance',
    'finance-profit-analysis': '/finance/reports/profit-analysis',
    'finance-cost-analysis': '/finance/reports/cost-analysis',
    'finance-payment-analysis': '/finance/reports/payment-analysis',
    'marketing-coupon': '/marketing/coupon',
    'marketing-activity': '/marketing/activity',
    'inventory-index': '/inventory/index',
    'inventory-check': '/inventory/check',
    'inventory-warning': '/inventory/warning',
    'inventory-log': '/inventory/log',
    'message-sms-template': '/message/sms-template',
    'message-sms-log': '/message/sms-log',
    'message-system-message': '/message/system-message',
    'message-center': '/message/message-center',
    'message-setting': '/message/setting'
  }
  
  const path = indexPathMap[index]
  if (path) {
    router.push(path)
  }
}


// 计算当前激活的菜单项
const activeMenu = computed(() => {
  // 如果路径是根路径或登录页面，则默认选中第一个菜单
  if (route.path === '/' || route.path === '/login') {
    return 'table-business'
  }

  // 根据当前路径返回对应的菜单索引
  return pathIndexMap[route.path] || 'table-business'
})

// 登录后自动跳转到第一个菜单页面
onMounted(() => {
  if (route.path === '/' || route.path === '/login') {
    // 延迟执行，确保路由已准备好
    setTimeout(() => {
      router.push('/table/business')
    }, 100)
  }
})

// 解决父级菜单高亮问题
watch(() => route.path, (newPath) => {
  const currentMenuIndex = pathIndexMap[newPath] || 'dashboard'
  const parentMenuIndex = menuParentMap[currentMenuIndex] || ''

  if (parentMenuIndex) {
    // 查找对应的父菜单DOM元素
    setTimeout(() => {
      const parentMenu = document.querySelector(`.el-sub-menu[index="${parentMenuIndex}"]`)
      if (parentMenu && !parentMenu.classList.contains('is-opened')) {
        // 如果父菜单未展开，则手动展开
        const triggerEl = parentMenu.querySelector('.el-sub-menu__title') as HTMLElement
        if (triggerEl) {
          triggerEl.click()
        }
      }

      // 如果是二级菜单，则确保其父级菜单（一级菜单）也展开
      if (parentMenuIndex === 'payment' || parentMenuIndex === 'billing' || parentMenuIndex === 'print' || parentMenuIndex === 'table' || parentMenuIndex === 'goods') {
        const grandparentMenu = document.querySelector(`.el-sub-menu[index="settings"]`)
        if (grandparentMenu && !grandparentMenu.classList.contains('is-opened')) {
          const triggerEl = grandparentMenu.querySelector('.el-sub-menu__title') as HTMLElement
          if (triggerEl) {
            triggerEl.click()
          }
        }
      }
    }, 100)
  }
})
</script>

<style scoped>
.app-sidebar{height:100%;background-color:#1f1f1f;overflow-y: auto;overflow-x: hidden;padding-top:10px;padding-left:10px;}
.sidebar-menu{height:100%;border-right:none;padding-bottom:60px;background:#1f1f1f;}
.sidebar-menu:deep(.el-menu){border-right: none;}
/* 自定义子菜单样式 */
.sidebar-menu :deep(.el-sub-menu) {width:100%;min-height:60px;height:auto;line-height:60px;float:left;
    border-bottom:1px solid rgba(53, 58, 54, .5);border-top-left-radius:4px;border-bottom-left-radius:4px;overflow: hidden;}
.sidebar-menu :deep(.el-sub-menu .el-menu-item){background:#353a36;color:#c2c2c2;padding-left:40px !important;}
.sidebar-menu :deep(.el-sub-menu .el-sub-menu__title){height:60px;line-height:60px;padding-left:10px !important;}
.sidebar-menu :deep(.el-sub-menu .el-sub-menu){min-height:50px;line-height:50px;padding-left:0 !important;border-radius:0;border-bottom:none;}
.sidebar-menu :deep(.el-sub-menu .el-sub-menu .el-sub-menu__title){height:50px;line-height:50px;padding-left:30px !important;background:#353a36;color:#c2c2c2;}
.sidebar-menu :deep(.el-menu-item){height:50px;line-height:50px;padding-left:10px !important;color:#c2c2c2;}
.sidebar-menu :deep(.el-menu){background:#353a36;}
/* 自定义菜单项选中样式，只为菜单项添加高亮，而不是子菜单 */
.sidebar-menu :deep(.el-menu-item.is-active) {
  background:linear-gradient(90deg,#419e5d, #1f482b);border-left:0;border-top-left-radius:4px;border-bottom-left-radius:4px;
  color: #fff !important;font-weight:500;min-height:50px;height:auto;line-height:50px;padding-left:10px !important;background-size:cover;}
.sidebar-menu :deep(.el-menu-item.is-active):hover{background:linear-gradient(90deg,#419e5d, #1f482b) !important;color:#fff !important;}
/* 自定义子菜单项选中样式 */
.sidebar-menu :deep(.el-menu--inline .el-menu-item.is-active) {padding-left:40px !important;}
.sidebar-menu :deep(.el-menu--inline .el-menu--inline .el-menu-item.is-active) {padding-left:40px !important;}
/* 菜单图标颜色 */
.sidebar-menu :deep(.el-menu-item.is-active .el-icon) {color: #fff !important;}
/* 覆盖Element Plus默认的父级菜单高亮行为 */
/* 即使子菜单被选中，父级菜单也不会高亮 */
.sidebar-menu :deep(.el-sub-menu) > .el-sub-menu__title{color: #c2c2c2 !important;}
.sidebar-menu :deep(.el-sub-menu) > .el-sub-menu__title .el-icon{color: #c2c2c2 !important;}
/* 即使有.is-active类，也不要高亮父级菜单标题 */
.sidebar-menu :deep(.el-sub-menu.is-active) > .el-sub-menu__title{background:#353a36;color:#c2c2c2;}
.sidebar-menu :deep(.el-sub-menu.is-active) > .el-sub-menu__title .el-icon{color: #c2c2c2 !important;}
/* 展开状态下的样式，保持原来的颜色 */
.sidebar-menu :deep(.el-sub-menu.is-opened:not(.is-active)) > .el-sub-menu__title{background:#353a36;color:#c2c2c2;}
.sidebar-menu :deep(.el-sub-menu.is-opened:not(.is-active)) > .el-sub-menu__title .el-icon{color: #c2c2c2 !important;}
/* 父级菜单高亮 */
.sidebar-menu :deep(.el-sub-menu.is-opened) {border-left: none;}
/* 悬停效果 */
.sidebar-menu :deep(.el-menu-item:hover),
.sidebar-menu :deep(.el-sub-menu__title:hover){background: #2f3134!important;color: #2c9a4d;}
/* 自定义滚动条样式 */
::-webkit-scrollbar{width:6px;height:6px;}
::-webkit-scrollbar-thumb{background-color:rgba(100, 108, 255, 0.3);border-radius: 3px;}
::-webkit-scrollbar-track{background-color: transparent;}
@media (max-width:1200px){
    .app-sidebar{padding-top:5px;padding-left:5px;}
    .sidebar-menu{padding-bottom:60px;}
    /* 自定义子菜单样式 */
    .sidebar-menu :deep(.el-sub-menu) {min-height:50px;line-height:50px;}
    .sidebar-menu :deep(.el-sub-menu .el-menu-item){padding-left:10px !important;}
    .sidebar-menu :deep(.el-sub-menu .el-sub-menu__title){height:50px;line-height:50px;padding-left:10px !important;}
    .sidebar-menu :deep(.el-sub-menu .el-sub-menu){min-height:50px;line-height:50px;padding-left:0 !important;}
    .sidebar-menu :deep(.el-sub-menu .el-sub-menu .el-sub-menu__title){height:50px;line-height:50px;padding-left:10px !important;}
    .sidebar-menu :deep(.el-menu-item){height:50px;line-height:50px;padding-left:10px !important;color:#c2c2c2;}
    /* 自定义菜单项选中样式，只为菜单项添加高亮，而不是子菜单 */
    .sidebar-menu :deep(.el-menu-item.is-active) {
        background:linear-gradient(90deg,#419e5d, #1f482b);border-left:0;border-top-left-radius:4px;border-bottom-left-radius:4px;
        color: #fff !important;font-weight:500;min-height:50px;height:auto;line-height:50px;padding-left:10px !important;background-size:cover;}
    .sidebar-menu :deep(.el-menu-item.is-active):hover{background:linear-gradient(90deg,#419e5d, #1f482b) !important;color:#fff !important;}
    /* 自定义子菜单项选中样式 */
    .sidebar-menu :deep(.el-menu--inline .el-menu-item.is-active) {padding-left:10px !important;}
    .sidebar-menu :deep(.el-menu--inline .el-menu--inline .el-menu-item.is-active) {padding-left:10px !important;}
    /* 菜单图标颜色 */
    .sidebar-menu :deep(.el-menu-item.is-active .el-icon) {color: #fff !important;}
}
</style>
