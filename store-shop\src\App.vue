<script setup lang="ts">
import { ref, onMounted } from 'vue';
import LoadingAnimation from './components/LoadingAnimation.vue';
// import DevToolbar from './components/common/DevToolbar.vue';

// 控制加载动画的显示
const isLoading = ref(true);

// 页面加载完成后隐藏动画
onMounted(() => {
  setTimeout(() => {
    isLoading.value = false;
  }, 1500);
});

// 移除beforeunload事件监听，避免重复触发加载动画
// 页面刷新时会自然重新加载整个应用，无需手动设置动画状态
// 注意：WebSocket实时消息系统现在改为在用户登录成功后初始化，确保能获取正确的shop_id
</script>

<template>
  <!-- 加载动画 -->
  <LoadingAnimation :visible="isLoading" />

  <!-- 主内容 - 使用slot方式处理router-view和transition -->
  <router-view v-slot="{ Component }">
    <transition name="fade-in" mode="out-in">
      <component :is="Component" v-if="!isLoading" />
    </transition>
  </router-view>

  <!-- 开发工具栏 -->
<!--<DevToolbar />-->
</template>

<style>
/* 全局过渡效果 */
.fade-in-enter-active,
.fade-in-leave-active {
  transition: opacity 0.3s ease;
}

.fade-in-enter-from,
.fade-in-leave-to {
  opacity: 0;
}
</style>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
