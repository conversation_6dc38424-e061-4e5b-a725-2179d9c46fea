<template>
  <div class="sales-chart">
    <div v-if="showHeader && title" class="chart-header">
      <h3 class="chart-title">
        <el-icon><TrendCharts /></el-icon>
        {{ title }}
      </h3>
      <div class="chart-controls">
        <el-radio-group v-model="chartType" size="small" @change="handleChartTypeChange">
          <el-radio-button value="line">折线图</el-radio-button>
          <el-radio-button value="bar">柱状图</el-radio-button>
          <el-radio-button value="area">面积图</el-radio-button>
        </el-radio-group>
        <el-button-group class="ml-12">
          <el-button size="small" @click="zoomIn">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
          <el-button size="small" @click="zoomOut">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button size="small" @click="resetZoom">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <div class="chart-container" :style="{ height: typeof height === 'number' ? height + 'px' : height }">
      <!-- 图表加载状态 -->
      <div v-if="isLoading" class="chart-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>图表加载中...</span>
      </div>
      
      <!-- 内部图表类型控制 -->
      <div v-if="showInternalControls && hasData" class="internal-controls">
        <el-radio-group
          :model-value="chartType"
          size="small"
          @update:model-value="handleInternalChartTypeChange"
        >
          <el-radio-button value="line">折线图</el-radio-button>
          <el-radio-button value="bar">柱状图</el-radio-button>
          <el-radio-button value="area">面积图</el-radio-button>
        </el-radio-group>
      </div>

      <div v-if="loading" class="chart-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>图表加载中...</span>
      </div>
      <div v-else-if="!hasData" class="chart-empty">
        <el-icon><Warning /></el-icon>
        <span>暂无数据</span>
      </div>
      <v-chart
        v-else
        ref="chartRef"
        :option="chartOption"
        :theme="theme"
        autoresize
        @click="handleChartClick"
        @brush="handleBrush"
        @brushEnd="handleBrushEnd"
        style="width: 100%; height: 100%;"
      />
    </div>

    <!-- 底部统计信息 - 已隐藏 -->
    <!-- <div class="chart-footer" v-if="showFooter && hasData">
      <div class="chart-stats">
        <div class="stat-item">
          <span class="stat-label">总计</span>
          <span class="stat-value">{{ formatValue(totalValue) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">平均值</span>
          <span class="stat-value">{{ formatValue(avgValue) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最大值</span>
          <span class="stat-value">{{ formatValue(maxValue) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">增长率</span>
          <span class="stat-value" :class="growthClass">{{ growthText }}</span>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent,
  BrushComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { ZoomIn, ZoomOut, Refresh, Loading, Warning } from '@element-plus/icons-vue'
import type { TrendChartData, ChartOption } from '@/types/finance'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent,
  BrushComponent
])

interface SalesChartProps {
  title?: string
  data: any
  loading?: boolean
  height?: string | number
  theme?: string
  showFooter?: boolean
  showHeader?: boolean
  showInternalControls?: boolean
  chartType?: 'line' | 'bar' | 'area'
  smooth?: boolean
  showArea?: boolean
  showSymbol?: boolean
  enableBrush?: boolean
  enableDataZoom?: boolean
}

const props = withDefaults(defineProps<SalesChartProps>(), {
  title: '销售趋势',
  loading: false,
  height: '400px',
  theme: 'default',
  showFooter: true,
  showHeader: true,
  showInternalControls: false,
  chartType: 'line',
  smooth: true,
  showArea: false,
  showSymbol: true,
  enableBrush: true,
  enableDataZoom: true
})

const emit = defineEmits<{
  click: [params: any]
  brush: [params: any]
  brushEnd: [params: any]
  chartTypeChange: [type: string]
  'chart-type-change': [type: string]
}>()

const chartRef = ref()
const chartType = ref(props.chartType)
const isLoading = ref(true)

// 监听外部传入的chartType变化
watch(() => props.chartType, (newType) => {
  chartType.value = newType
}, { immediate: true })

// 监听数据变化，控制加载状态
watch(() => props.data, (newData) => {
  if (newData) {
    // 数据到达后，给一点时间让图表渲染
    nextTick(() => {
      setTimeout(() => {
        isLoading.value = false
      }, 300) // 300ms延迟确保图表完全渲染
    })
  } else {
    isLoading.value = true
  }
}, { immediate: true })

// 计算属性 - 检查是否有数据
const hasData = computed(() => {
  if (!props.data) return false

  // 检查不同的数据格式
  if (Array.isArray(props.data)) {
    return props.data.length > 0
  }

  if (props.data.dates && props.data.series) {
    return props.data.dates.length > 0 && props.data.series.length > 0
  }

  if (props.data.xAxis && props.data.series) {
    return props.data.xAxis.length > 0 && props.data.series.length > 0
  }

  return Object.keys(props.data).length > 0
})

// 计算属性 - 数据处理
const processedData = computed(() => {
  if (!hasData.value) return null

  // 直接使用传入的数据，确保格式正确
  return props.data
})

const totalValue = computed(() => {
  if (!processedData.value?.series?.length) return 0
  return processedData.value.series.reduce((total: number, series: any) => {
    return total + (series.data || []).reduce((sum: number, val: number) => sum + (val || 0), 0)
  }, 0)
})


const growthRate = computed(() => {
  if (!processedData.value?.series?.length || !processedData.value.series[0].data?.length || processedData.value.series[0].data.length < 2) return 0
  const firstSeries = processedData.value.series[0]
  const lastValue = firstSeries.data[firstSeries.data.length - 1] || 0
  const firstValue = firstSeries.data[0] || 0
  return firstValue > 0 ? (lastValue - firstValue) / firstValue : 0
})

const growthClass = computed(() => {
  const rate = growthRate.value
  if (rate > 0) return 'positive'
  if (rate < 0) return 'negative'
  return 'neutral'
})

// 图表配置
const chartOption = computed(() => {
  if (!hasData.value || !processedData.value) {
    return {}
  }

  const data = processedData.value
  const xAxisData = data.dates || data.xAxis || []
  const series = (data.series || []).map((item: any) => ({
    name: item.name || '数据',
    type: props.chartType === 'area' ? 'line' : props.chartType,
    data: item.data || [],
    smooth: props.smooth && props.chartType !== 'bar',
    areaStyle: props.chartType === 'area' ? {} : undefined,
    symbol: props.showSymbol ? 'circle' : 'none',
    symbolSize: 6,
    lineStyle: {
      width: 3
    },
    itemStyle: {
      borderRadius: props.chartType === 'bar' ? [4, 4, 0, 0] : undefined
    }
  }))

  return {
    title: props.title ? {
      text: props.title,
      left: 'left',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#1a202c'
      }
    } : undefined,
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e2e8f0',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: (params: any) => {
        let tooltipContent = `<div style="padding: 8px 4px;">`
        tooltipContent += `<div style="font-weight: 600; margin-bottom: 8px; color: #1a202c;">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          tooltipContent += `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="color: #64748b; margin-right: 8px;">${param.seriesName}:</span>
              <span style="font-weight: 600; color: #1a202c;">${formatValue(param.value)}</span>
            </div>
          `
        })
        tooltipContent += `</div>`
        return tooltipContent
      }
    },
    legend: {
      top: 'top',
      right: 'right',
      textStyle: {
        color: '#64748b'
      }
    },
    grid: {
      left: '60px',
      right: '40px',
      bottom: '60px',
      top: '80px',
      containLabel: false
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e2e8f0'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#e2e8f0'
        }
      },
      axisLabel: {
        color: '#64748b',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e2e8f0'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#e2e8f0'
        }
      },
      axisLabel: {
        color: '#64748b',
        fontSize: 12,
        formatter: (value: number) => formatValue(value)
      },
      splitLine: {
        lineStyle: {
          color: '#f1f5f9',
          type: 'dashed'
        }
      }
    },
    series: series,
    color: ['#667eea', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'],
    dataZoom: props.enableDataZoom ? [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        type: 'slider',
        start: 0,
        end: 100,
        height: 30,
        bottom: 20
      }
    ] : undefined,
    brush: props.enableBrush ? {
      toolbox: ['rect', 'polygon', 'clear'],
      xAxisIndex: 0
    } : undefined
  }
})

// 方法
const formatValue = (value: number): string => {
  if (value === null || value === undefined) return '0'
  if (value < 1000) return value.toFixed(0)
  if (value < 1000000) return (value / 1000).toFixed(1) + 'K'
  return (value / 1000000).toFixed(1) + 'M'
}

const handleChartTypeChange = () => {
  emit('chartTypeChange', chartType.value)
}

const handleInternalChartTypeChange = (type: string) => {
  chartType.value = type as 'line' | 'bar' | 'area'
  emit('chart-type-change', type)
}

const handleChartClick = (params: any) => {
  emit('click', params)
}

const handleBrush = (params: any) => {
  emit('brush', params)
}

const handleBrushEnd = (params: any) => {
  emit('brushEnd', params)
}

const zoomIn = () => {
  if (chartRef.value) {
    chartRef.value.dispatchAction({
      type: 'dataZoom',
      start: 10,
      end: 90
    })
  }
}

const zoomOut = () => {
  if (chartRef.value) {
    chartRef.value.dispatchAction({
      type: 'dataZoom',
      start: 0,
      end: 100
    })
  }
}

const resetZoom = () => {
  if (chartRef.value) {
    chartRef.value.dispatchAction({
      type: 'restore'
    })
  }
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    if (chartRef.value) {
      chartRef.value.resize()
    }
  })
}, { deep: true })

// 监听图表类型变化
watch(() => props.chartType, (newType) => {
  chartType.value = newType
})

// 组件挂载后调整图表大小
onMounted(() => {
  nextTick(() => {
    if (chartRef.value) {
      chartRef.value.resize()
    }
  })
})
</script>

<style scoped lang="scss">
.sales-chart {
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;

    .chart-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .chart-controls {
      display: flex;
      align-items: center;
      gap: 12px;

      .ml-12 {
        margin-left: 12px;
      }
    }
  }

  .chart-container {
    height: v-bind(height);
    padding: 0;
    position: relative;
    
    .chart-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      color: var(--el-text-color-secondary);
      z-index: 10;
      
      .el-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }
      
      span {
        font-size: 14px;
      }
    }

    .internal-controls {
      position: absolute;
      top: 0;
      right: 50%;
      z-index: 10;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 6px;
      backdrop-filter: blur(8px);

      :deep(.el-radio-group) {
        .el-radio-button {
          &:first-child .el-radio-button__inner {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
          }

          &:last-child .el-radio-button__inner {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
          }

          &.is-active .el-radio-button__inner {
            background-color: #67c23a;
            border-color: #67c23a;
            color: white;
            box-shadow: -1px 0 0 0 #67c23a;
          }

          .el-radio-button__inner {
            font-size: 11px;
            padding: 6px 10px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            color: #6c757d;
            transition: all 0.25s ease;

            &:hover {
              background-color: #e8f5e8;
              border-color: #95d17a;
              color: #67c23a;
            }
          }
        }
      }
    }
  }

  .chart-loading,
  .chart-empty {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 12px;
    color: #999999;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.9);

    .el-icon {
      font-size: 32px;
      color: #94a3b8;
    }

    .is-loading {
      animation: rotate 2s linear infinite;
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .chart-footer {
    padding: 16px 20px;
    background: #fafafa;
    border-top: 1px solid #f0f0f0;

    .chart-stats {
      display: flex;
      justify-content: space-around;
      align-items: center;

      .stat-item {
        text-align: center;

        .stat-label {
          display: block;
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
        }

        .stat-value {
          display: block;
          font-size: 16px;
          font-weight: 600;
          color: #303133;

          &.positive {
            color: #67c23a;
          }

          &.negative {
            color: #f56c6c;
          }

          &.neutral {
            color: #909399;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sales-chart {
    .chart-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .chart-controls {
        justify-content: space-between;

        .ml-12 {
          margin-left: 0;
        }
      }
    }

    .chart-footer {
      .chart-stats {
        flex-wrap: wrap;
        gap: 16px;

        .stat-item {
          flex: 1;
          min-width: 80px;
        }
      }
    }
  }
}
</style>
