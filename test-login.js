const { chromium } = require('playwright');

async function testLoginPage() {
  console.log('🚀 开始测试登录页面...');
  
  // 启动浏览器 (headless模式避免依赖问题)
  const browser = await chromium.launch({ 
    headless: true,  // 无头模式
    args: [
      '--no-sandbox', 
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu'
    ]
  });
  
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  
  const page = await context.newPage();
  
  try {
    // 导航到登录页面
    console.log('📄 正在加载登录页面...');
    await page.goto('http://localhost:8000/授权公司管理系统.html');
    await page.waitForTimeout(2000);
    
    // 截取登录页面截图
    console.log('📸 截取登录页面截图...');
    await page.screenshot({ path: 'login-page-test.png', fullPage: true });
    
    // 测试输入框
    console.log('⌨️ 测试用户名输入...');
    await page.fill('input[placeholder*="用户名"]', '<EMAIL>');
    await page.waitForTimeout(1000);
    
    console.log('⌨️ 测试密码输入...');
    await page.fill('input[type="password"]', 'testpassword');
    await page.waitForTimeout(1000);
    
    // 截取填写后的截图
    console.log('📸 截取填写表单后的截图...');
    await page.screenshot({ path: 'login-filled-test.png', fullPage: true });
    
    // 测试登录按钮悬停效果
    console.log('🖱️ 测试登录按钮悬停效果...');
    await page.hover('.login-btn');
    await page.waitForTimeout(1000);
    
    // 截取悬停效果截图
    await page.screenshot({ path: 'login-hover-test.png', fullPage: true });
    
    // 测试登录按钮点击
    console.log('🔘 测试登录按钮点击...');
    await page.click('.login-btn');
    await page.waitForTimeout(3000);
    
    // 截取最终截图
    await page.screenshot({ path: 'login-final-test.png', fullPage: true });
    
    console.log('✅ 登录页面测试完成！');
    console.log('📁 截图文件已保存：');
    console.log('   - login-page-test.png (初始页面)');
    console.log('   - login-filled-test.png (填写表单后)');
    console.log('   - login-hover-test.png (按钮悬停效果)');
    console.log('   - login-final-test.png (点击登录后)');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  } finally {
    // 等待5秒让用户查看结果
    await page.waitForTimeout(5000);
    await browser.close();
    console.log('🔚 浏览器已关闭');
  }
}

// 运行测试
testLoginPage().catch(console.error);