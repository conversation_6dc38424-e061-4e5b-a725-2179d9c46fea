<template>
  <div class="member-overview">
    <!-- 骨架屏 -->
    <MemberOverviewSkeleton v-if="loading.summary" />
    
    <!-- 实际内容 -->
    <div v-else class="overview-content">
      <!-- 页面标题 -->
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><User /></el-icon>
            会员概览
          </h1>
          <p class="page-subtitle">全面了解会员运营情况，支持数据驱动的会员管理决策</p>
        </div>
        <div class="header-right">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
          <el-button type="primary" @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>

      <!-- 核心指标卡片区 -->
      <div class="summary-cards">
        <el-row :gutter="20">
          <el-col :span="8" v-for="(card, index) in summaryCards" :key="index">
            <el-card class="summary-card" @click="focusSection(card.key)">
              <div class="card-content">
                <div class="card-icon" :style="{ background: card.color }">
                  <el-icon><component :is="card.icon" /></el-icon>
                </div>
                <div class="card-info">
                  <div class="card-title">{{ card.title }}</div>
                  <div class="card-value">{{ card.value }}</div>
                  <div v-if="card.change" class="card-trend" :class="card.trend">
                    <el-icon><component :is="getTrendIcon(card.trend)" /></el-icon>
                    {{ card.change }}% 较上期
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 图表分析区 -->
      <div class="charts-section">
        <el-row :gutter="20">
          <!-- 会员注册趋势 -->
          <el-col :span="8">
            <el-card class="chart-card">
              <template #header>
                <div class="chart-header">
                  <span>会员注册趋势</span>
                </div>
              </template>
              <div class="chart-container">
                <MemberGrowthChart
                  :data="registerTrendData"
                  :type="'register'"
                  :height="300"
                />
              </div>
            </el-card>
          </el-col>
          
          <!-- 会员活跃趋势 -->
          <el-col :span="8">
            <el-card class="chart-card">
              <template #header>
                <div class="chart-header">
                  <span>会员活跃趋势</span>
                </div>
              </template>
              <div class="chart-container">
                <MemberGrowthChart
                  :data="activeTrendData"
                  :type="'active'"
                  :height="300"
                />
              </div>
            </el-card>
          </el-col>

          <!-- 会员等级分布 -->
          <el-col :span="8">
            <el-card class="chart-card">
              <template #header>
                <div class="chart-header">
                  <span>会员等级分布</span>
                  <el-tooltip content="点击图表区域查看详细信息" placement="top">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="chart-container">
                <MemberLevelChart
                  :data="levelDistributionData"
                  :height="300"
                  @click="handleLevelClick"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 资产管理概览 -->
      <div class="assets-section" v-if="false">
        <el-row :gutter="20">
          <!-- 积分系统概况 -->
          <el-col :span="12">
            <el-card class="asset-card">
              <template #header>
                <div class="asset-header">
                  <el-icon><Star /></el-icon>
                  <span>积分系统概况</span>
                </div>
              </template>
              <div class="asset-stats">
                <div class="stat-item">
                  <div class="stat-label">积分发放总量</div>
                  <div class="stat-value">{{ formatNumber(assetStats.points.issued) }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">积分使用总量</div>
                  <div class="stat-value">{{ formatNumber(assetStats.points.used) }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">积分剩余总量</div>
                  <div class="stat-value">{{ formatNumber(assetStats.points.remaining) }}</div>
                </div>
                <div class="progress-item">
                  <div class="progress-label">使用率</div>
                  <el-progress 
                    :percentage="assetStats.points.usage_rate" 
                    :color="getUsageRateColor(assetStats.points.usage_rate)"
                  />
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 余额系统概况 -->
          <el-col :span="12">
            <el-card class="asset-card">
              <template #header>
                <div class="asset-header">
                  <el-icon><Wallet /></el-icon>
                  <span>余额系统概况</span>
                </div>
              </template>
              <div class="asset-stats">
                <div class="stat-item">
                  <div class="stat-label">余额总额</div>
                  <div class="stat-value">¥{{ formatNumber(assetStats.balance.total) }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">充值总额</div>
                  <div class="stat-value">¥{{ formatNumber(assetStats.balance.recharged) }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">消费总额</div>
                  <div class="stat-value">¥{{ formatNumber(assetStats.balance.consumed) }}</div>
                </div>
                <div class="progress-item">
                  <div class="progress-label">活跃度</div>
                  <el-progress 
                    :percentage="assetStats.balance.activity_rate" 
                    :color="getUsageRateColor(assetStats.balance.activity_rate)"
                  />
                </div>
              </div>
            </el-card>
          </el-col>

        </el-row>
      </div>

      <!-- 会员活跃度分析 -->
      <div class="activity-section">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-card class="chart-card">
              <template #header>
                <div class="chart-header">
                  <span>会员活跃度分层</span>
                  <el-tag type="info" size="small">基于最后消费时间</el-tag>
                </div>
              </template>
              <div class="activity-layers">
                <el-row :gutter="20">
                  <el-col :span="6" v-for="layer in activityLayers" :key="layer.layer_key">
                    <div class="layer-card" :style="{ '--layer-color': layer.color }">
                      <div class="layer-header">
                        <div class="layer-icon" :style="{ backgroundColor: layer.color + '20', color: layer.color }">
                          <el-icon><User /></el-icon>
                        </div>
                        <div class="layer-badge" :style="{ backgroundColor: layer.color }">
                          {{ layer.percentage }}%
                        </div>
                      </div>
                      <div class="layer-content">
                        <div class="layer-title">{{ layer.layer_name }}</div>
                        <div class="layer-count">{{ formatNumber(layer.member_count) }} 人</div>
                        <div class="layer-desc">{{ layer.description }}</div>
                      </div>
                      <div class="layer-progress-wrapper">
                        <div class="layer-progress-bg">
                          <div 
                            class="layer-progress-fill" 
                            :style="{ width: layer.percentage + '%', backgroundColor: layer.color }"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>

        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User, Refresh, QuestionFilled, Star, Wallet,
  ArrowUp, ArrowDown, Minus
} from '@element-plus/icons-vue'

// 组件导入
import MemberOverviewSkeleton from '@/components/member/MemberOverviewSkeleton.vue'
import MemberGrowthChart from '@/components/member/charts/MemberGrowthChart.vue'
import MemberLevelChart from '@/components/member/charts/MemberLevelChart.vue'

// 服务导入
import memberOverviewService from '@/services/memberOverviewService'
import type {
  MemberOverviewSummary,
  MemberGrowthData,
  MemberLevelDistribution,
  MemberAssetStats,
  MemberActivityLayer,
  MemberOverviewParams
} from '@/services/memberOverviewService'

const router = useRouter()

// 加载状态
const loading = reactive({
  summary: true,
  charts: false,
  assets: false
})

// 日期范围
const dateRange = ref<[string, string]>([
  new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  new Date().toISOString().split('T')[0]
])

// 核心指标卡片数据
const summaryCards = ref([
  {
    key: 'total',
    title: '会员总数',
    value: '12,345',
    change: '+12',
    trend: 'up',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    icon: 'User'
  },
  {
    key: 'active',
    title: '活跃会员',
    value: '8,901',
    change: '+5',
    trend: 'up',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    icon: 'Star'
  },
  {
    key: 'consumption',
    title: '会员消费总额',
    value: '¥2,456,789',
    change: '+18',
    trend: 'up',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    icon: 'Wallet'
  }
])

// 图表数据
const levelDistributionData = ref<MemberLevelDistribution[]>([])

// 分离的注册和活跃趋势数据
const registerTrendData = ref<MemberGrowthData[]>([])
const activeTrendData = ref<MemberGrowthData[]>([])

// 资产统计数据
const assetStats = ref<MemberAssetStats>({
  points: {
    issued: 1234567,
    used: 876543,
    remaining: 358024,
    usage_rate: 71
  },
  balance: {
    total: 2345678,
    recharged: 3456789,
    consumed: 1111111,
    activity_rate: 68
  },
  other_assets: []
})


// 活跃度分层数据
const activityLayers = ref<MemberActivityLayer[]>([
  {
    layer_key: 'high',
    layer_name: '高活跃会员',
    description: '7天内有消费',
    member_count: 3456,
    percentage: 28,
    color: '#67C23A'
  },
  {
    layer_key: 'medium',
    layer_name: '中活跃会员', 
    description: '30天内有消费',
    member_count: 4567,
    percentage: 37,
    color: '#E6A23C'
  },
  {
    layer_key: 'low',
    layer_name: '低活跃会员',
    description: '90天内有消费', 
    member_count: 2890,
    percentage: 23,
    color: '#F56C6C'
  },
  {
    layer_key: 'inactive',
    layer_name: '沉睡会员',
    description: '90天以上未消费',
    member_count: 1432,
    percentage: 12,
    color: '#909399'
  }
])

// 方法定义
const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('zh-CN').format(num)
}

const getTrendIcon = (trend: string) => {
  return trend === 'up' ? ArrowUp : trend === 'down' ? ArrowDown : Minus
}

const getUsageRateColor = (rate: number): string => {
  if (rate >= 80) return '#67C23A'
  if (rate >= 60) return '#E6A23C'
  return '#F56C6C'
}

const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    dateRange.value = value
    loadData()
  }
}

const handleRefresh = () => {
  loadData()
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const focusSection = (key: string) => {
  const element = document.querySelector(`[data-section="${key}"]`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const handleLevelClick = (params: any) => {
  console.log('Level clicked:', params)
  ElMessage.info(`点击了${params.name}等级会员`)
}


const loadData = async () => {
  try {
    loading.summary = true
    
    const params: MemberOverviewParams = {
      start_date: dateRange.value[0],
      end_date: dateRange.value[1]
    }
    
    // 可以并行加载多个数据接口以提高性能
    const [
      summaryData,
      registerGrowthData,
      activeGrowthData,
      levelData,
      assetData,
      activityData
    ] = await Promise.all([
      memberOverviewService.getSummaryData(params),
      memberOverviewService.getGrowthTrendData({...params, type: 'register'}),
      memberOverviewService.getGrowthTrendData({...params, type: 'active'}),
      memberOverviewService.getLevelDistributionData(params),
      memberOverviewService.getAssetStatsData(params),
      memberOverviewService.getActivityLayersData(params)
    ])
    
    // 数据安全验证和默认值设置
    const safeSummaryData = summaryData && typeof summaryData === 'object' ? {
      total_members: summaryData.total_members ?? 0,
      active_members: summaryData.active_members ?? 0,
      total_consumption: summaryData.total_consumption ?? 0,
      growth_rates: {
        members: summaryData.growth_rates?.members ?? 0,
        active: summaryData.growth_rates?.active ?? 0,
        consumption: summaryData.growth_rates?.consumption ?? 0
      }
    } : {
      total_members: 0,
      active_members: 0,
      total_consumption: 0,
      growth_rates: {
        members: 0,
        active: 0,
        consumption: 0
      }
    }
    
    // 更新核心指标卡片数据
    summaryCards.value = [
      {
        key: 'total',
        title: '会员总数',
        value: formatNumber(safeSummaryData.total_members),
        change: safeSummaryData.growth_rates.members.toFixed(1),
        trend: safeSummaryData.growth_rates.members >= 0 ? 'up' : 'down',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        icon: 'User'
      },
      {
        key: 'active',
        title: '活跃会员',
        value: formatNumber(safeSummaryData.active_members),
        change: safeSummaryData.growth_rates.active.toFixed(1),
        trend: safeSummaryData.growth_rates.active >= 0 ? 'up' : 'down',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        icon: 'Star'
      },
      {
        key: 'consumption',
        title: '会员消费总额',
        value: '¥' + formatNumber(safeSummaryData.total_consumption),
        change: safeSummaryData.growth_rates.consumption.toFixed(1),
        trend: safeSummaryData.growth_rates.consumption >= 0 ? 'up' : 'down',
        color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        icon: 'Wallet'
      }
    ]
    
    // 更新图表数据（添加安全验证）
    registerTrendData.value = Array.isArray(registerGrowthData) ? registerGrowthData : []
    activeTrendData.value = Array.isArray(activeGrowthData) ? activeGrowthData : []
    levelDistributionData.value = Array.isArray(levelData) ? levelData : []
    // 更新资产统计和活跃度数据（添加安全验证）
    assetStats.value = assetData && typeof assetData === 'object' ? assetData : {
      points: { issued: 0, used: 0, remaining: 0, usage_rate: 0 },
      balance: { total: 0, recharged: 0, consumed: 0, activity_rate: 0 },
      other_assets: []
    }
    activityLayers.value = Array.isArray(activityData) ? activityData : []
    
    loading.summary = false
  } catch (error) {
    console.error('加载会员概览数据失败:', error)
    ElMessage.error('加载数据失败，请重试')
    
    // 设置默认数据，防止页面崩溃
    summaryCards.value = [
      {
        key: 'total',
        title: '会员总数',
        value: '0',
        change: '0.0',
        trend: 'up',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        icon: 'User'
      },
      {
        key: 'active',
        title: '活跃会员',
        value: '0',
        change: '0.0',
        trend: 'up',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        icon: 'Star'
      },
      {
        key: 'consumption',
        title: '会员消费总额',
        value: '¥0',
        change: '0.0',
        trend: 'up',
        color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        icon: 'Wallet'
      }
    ]
    
    loading.summary = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.member-overview {
  padding: 20px;
  background-color: #f5f7f9;
  min-height: 100vh;

  .overview-content {
    // 移除最大宽度限制，让页面占满整个展示区域
    // max-width: 1400px;
    // margin: 0 auto;
    width: 100%;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    .header-left {
      .page-title {
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;

        .el-icon {
          margin-right: 8px;
          color: var(--el-color-primary);
        }
      }

      .page-subtitle {
        color: #606266;
        margin: 0;
        font-size: 14px;
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }

  .summary-cards {
    margin-bottom: 20px;

    .summary-card {
      cursor: pointer;
      transition: all 0.3s ease;
      height: 120px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      .card-content {
        display: flex;
        align-items: center;
        height: 100%;

        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          .el-icon {
            font-size: 28px;
            color: white;
          }
        }

        .card-info {
          flex: 1;

          .card-title {
            font-size: 14px;
            color: #909399;
            margin-bottom: 4px;
          }

          .card-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .card-trend {
            display: flex;
            align-items: center;
            font-size: 12px;

            &.up {
              color: #67C23A;
            }

            &.down {
              color: #F56C6C;
            }

            .el-icon {
              margin-right: 4px;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .charts-section,
  .assets-section,
  .activity-section {
    margin-bottom: 20px;

    .chart-card,
    .asset-card,
    .action-card {
      .chart-header,
      .asset-header,
      .action-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .el-icon {
          margin-right: 8px;
        }
      }

      .chart-container {
        height: 300px;
      }
    }
  }

  .assets-section {
    .asset-stats {
      .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-of-type {
          border-bottom: none;
        }

        .stat-label {
          color: #606266;
          font-size: 14px;
        }

        .stat-value {
          font-weight: 600;
          color: #303133;
        }
      }

      .progress-item {
        padding: 12px 0;

        .progress-label {
          color: #606266;
          font-size: 14px;
          margin-bottom: 8px;
        }
      }
    }

  }

  .activity-section {
    .activity-layers {
      .layer-card {
        background: #fff;
        border: 1px solid #f0f0f0;
        border-radius: 12px;
        padding: 20px;
        height: 180px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
          border-color: #e6f7ff;
        }

        .layer-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .layer-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
          }

          .layer-badge {
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            letter-spacing: 0.5px;
          }
        }

        .layer-content {
          flex: 1;

          .layer-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 8px;
            line-height: 1.2;
          }

          .layer-count {
            font-size: 24px;
            font-weight: 700;
            color: #303133;
            margin-bottom: 8px;
            line-height: 1;
          }

          .layer-desc {
            font-size: 13px;
            color: #909399;
            line-height: 1.4;
          }
        }

        .layer-progress-wrapper {
          margin-top: 16px;

          .layer-progress-bg {
            width: 100%;
            height: 6px;
            background: #f5f7fa;
            border-radius: 3px;
            overflow: hidden;

            .layer-progress-fill {
              height: 100%;
              border-radius: 3px;
              transition: all 0.6s ease;
              position: relative;

              &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
                animation: shimmer 2s infinite;
              }
            }
          }
        }

        // 为每个卡片添加不同的装饰效果
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: var(--layer-color, #67C23A);
          border-radius: 12px 12px 0 0;
        }
      }
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }
  }
}
</style>