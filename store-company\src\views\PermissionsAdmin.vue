<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">管理员管理</h1>
      <p class="page-description">管理系统管理员账户信息和权限设置</p>
    </div>

    <div class="card">
      <div class="card-header">
        <div class="card-actions">
          <button class="btn btn-primary" @click="showAddAdminModal = true">
            <i class="icon">➕</i> 新增管理员
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th width="60">ID</th>
                <th width="120">昵称</th>
                <th width="120">登录账号</th>
                <th width="120">所属角色</th>
                <th width="80">状态</th>
                <th width="150">最后登录时间</th>
                <th width="150">添加时间</th>
                <th width="200">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="admin in adminList" :key="admin.id">
                <td>{{ admin.id }}</td>
                <td>
                  <div class="admin-info">
                    <div class="admin-avatar" :style="{ backgroundImage: admin.avatar ? `url(${admin.avatar})` : '' }">
                      {{ admin.avatar ? '' : admin.nickname.charAt(0) }}
                    </div>
                    <span>{{ admin.nickname }}</span>
                  </div>
                </td>
                <td>{{ admin.username }}</td>
                <td>{{ getRoleName(admin.role_id) }}</td>
                <td>
                  <span class="status-badge" :class="admin.status === 1 ? 'status-active' : 'status-disabled'">
                    {{ admin.status === 1 ? '正常' : '禁用' }}
                  </span>
                </td>
                <td>
                  <span v-if="admin.login_time" class="login-time">{{ formatDate(admin.login_time) }}</span>
                  <span v-else class="never-login">从未登录</span>
                </td>
                <td>{{ formatDate(admin.created_at) }}</td>
                <td>
                  <div class="action-buttons">
                    <button class="btn btn-sm btn-primary" @click="editAdmin(admin)" title="编辑管理员">
                      <i class="icon">✏️</i> 编辑
                    </button>
                    <button class="btn btn-sm btn-warning" @click="toggleAdminStatus(admin)"
                            :title="admin.status === 1 ? '禁用管理员' : '启用管理员'">
                      <i class="icon">{{ admin.status === 1 ? '⏸️' : '▶️' }}</i>
                      {{ admin.status === 1 ? '禁用' : '启用' }}
                    </button>
                    <button class="btn btn-sm btn-danger" @click="deleteAdmin(admin)"
                            :disabled="admin.is_origin === 1" title="删除管理员"
                            :class="{ 'btn-disabled': admin.is_origin === 1 }">
                      <i class="icon">🗑️</i> 删除
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 管理员新增模态框 -->
    <div v-if="showAddAdminModal" class="modal-overlay" @click.self="showAddAdminModal = false">
      <div class="modal-dialog modal-lg">
        <div class="modal-header">
          <h3 class="modal-title">新增管理员</h3>
          <button class="modal-close" @click="showAddAdminModal = false">×</button>
        </div>
        <div class="modal-body">
          <form class="admin-form">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">昵称 <span class="required">*</span></label>
                <input type="text" class="form-control" v-model="adminForm.nickname" placeholder="请输入管理员昵称">
              </div>
              <div class="form-group">
                <label class="form-label">登录账号 <span class="required">*</span></label>
                <input type="text" class="form-control" v-model="adminForm.username" placeholder="请输入登录账号">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">登录密码 <span class="required">*</span></label>
                <input type="password" class="form-control" v-model="adminForm.password" placeholder="请输入登录密码">
              </div>
              <div class="form-group">
                <label class="form-label">确认密码 <span class="required">*</span></label>
                <input type="password" class="form-control" v-model="adminForm.password_confirm" placeholder="请再次输入密码">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">所属角色 <span class="required">*</span></label>
                <select class="form-control" v-model="adminForm.role_id">
                  <option value="">请选择角色</option>
                  <option v-for="role in roleList" :key="role.id" :value="role.id">{{ role.role_name }}</option>
                </select>
              </div>
              <div class="form-group">
                <label class="form-label">头像URL</label>
                <input type="url" class="form-control" v-model="adminForm.avatar" placeholder="请输入头像链接(可选)">
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">状态</label>
              <div class="radio-group">
                <label class="radio-item">
                  <input type="radio" name="adminStatus" :value="1" v-model="adminForm.status">
                  <span class="radio-label">正常</span>
                </label>
                <label class="radio-item">
                  <input type="radio" name="adminStatus" :value="0" v-model="adminForm.status">
                  <span class="radio-label">禁用</span>
                </label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showAddAdminModal = false">取消</button>
          <button class="btn btn-primary" @click="saveAdmin">
            <i class="icon">💾</i> 保存管理员
          </button>
        </div>
      </div>
    </div>
    <!-- 管理员编辑模态框 -->
    <div v-if="showEditAdminModal" class="modal-overlay" @click.self="showEditAdminModal = false">
      <div class="modal-dialog modal-lg">
        <div class="modal-header">
          <h3 class="modal-title">编辑管理员</h3>
          <button class="modal-close" @click="showEditAdminModal = false">×</button>
        </div>
        <div class="modal-body">
          <form class="admin-form">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">昵称 <span class="required">*</span></label>
                <input type="text" class="form-control" v-model="adminForm.nickname" placeholder="请输入管理员昵称">
              </div>
              <div class="form-group">
                <label class="form-label">登录账号 <span class="required">*</span></label>
                <input type="text" class="form-control" v-model="adminForm.username" placeholder="请输入登录账号">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">登录密码</label>
                <input type="password" class="form-control" v-model="adminForm.password" placeholder="留空则不修改密码">
                <small class="form-hint">留空则不修改原密码</small>
              </div>
              <div class="form-group" v-if="adminForm.password">
                <label class="form-label">确认密码 <span class="required">*</span></label>
                <input type="password" class="form-control" v-model="adminForm.password_confirm" placeholder="请再次输入密码">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">所属角色 <span class="required">*</span></label>
                <select class="form-control" v-model="adminForm.role_id">
                  <option value="">请选择角色</option>
                  <option v-for="role in roleList" :key="role.id" :value="role.id">{{ role.role_name }}</option>
                </select>
              </div>
              <div class="form-group">
                <label class="form-label">头像URL</label>
                <input type="url" class="form-control" v-model="adminForm.avatar" placeholder="请输入头像链接(可选)">
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">状态</label>
              <div class="radio-group">
                <label class="radio-item">
                  <input type="radio" name="editAdminStatus" :value="1" v-model="adminForm.status">
                  <span class="radio-label">正常</span>
                </label>
                <label class="radio-item">
                  <input type="radio" name="editAdminStatus" :value="0" v-model="adminForm.status">
                  <span class="radio-label">禁用</span>
                </label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showEditAdminModal = false">取消</button>
          <button class="btn btn-primary" @click="saveAdmin">
            <i class="icon">💾</i> 保存修改
          </button>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <ConfirmDialog
      :visible="confirmDialogState.visible"
      :type="confirmDialogState.type"
      :title="confirmDialogState.title"
      :message="confirmDialogState.message"
      :details="confirmDialogState.details"
      :confirm-text="confirmDialogState.confirmText"
      :cancel-text="confirmDialogState.cancelText"
      :loading="confirmDialogState.loading"
      :loading-text="confirmDialogState.loadingText"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />

    <!-- 消息提示 -->
    <MessageToast ref="messageToastRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import MessageToast from '@/components/MessageToast.vue'
import { useConfirm, useMessage, useDeleteConfirm, useStatusConfirm } from '@/composables/useDialog'

// 对话框组合式函数
const { confirmDialogState, handleConfirm, handleCancel } = useConfirm()
const { setMessageToastRef, success, error, warning } = useMessage()
const { confirmDelete } = useDeleteConfirm()
const { confirmStatusChange } = useStatusConfirm()

// 消息提示组件引用
const messageToastRef = ref<any>(null)

// 管理员管理相关变量
const showAddAdminModal = ref(false)
const showEditAdminModal = ref(false)

// 管理员表单数据
const adminForm = ref({
  id: null as number | null,
  nickname: '',
  username: '',
  password: '',
  password_confirm: '',
  avatar: '',
  role_id: '',
  status: 1
})

// 角色列表
const roleList = ref([
  { id: 1, role_name: '超级管理员' },
  { id: 2, role_name: '系统管理员' },
  { id: 3, role_name: '普通管理员' }
])

// 管理员列表
const adminList = ref([
  {
    id: 1,
    nickname: '系统管理员',
    username: 'admin',
    avatar: '',
    role_id: 1,
    status: 1,
    login_ip: '*************',
    login_time: '2024-01-15 10:30:00',
    created_at: '2024-01-01 09:00:00',
    updated_at: '2024-01-15 10:30:00',
    is_origin: 1
  },
  {
    id: 2,
    nickname: '李经理',
    username: 'manager_li',
    avatar: '',
    role_id: 2,
    status: 1,
    login_ip: '*************',
    login_time: '2024-01-15 14:20:00',
    created_at: '2024-01-15 08:00:00',
    updated_at: '2024-01-15 14:20:00',
    is_origin: 0
  },
  {
    id: 3,
    nickname: '王出纳',
    username: 'cashier_wang',
    avatar: '',
    role_id: 3,
    status: 1,
    login_ip: '*************',
    login_time: '2024-03-05 16:45:00',
    created_at: '2024-03-05 09:00:00',
    updated_at: '2024-03-05 16:45:00',
    is_origin: 0
  },
  {
    id: 4,
    nickname: '张服务员',
    username: 'server_zhang',
    avatar: '',
    role_id: 3,
    status: 0,
    login_ip: '',
    login_time: null,
    created_at: '2024-03-20 10:00:00',
    updated_at: '2024-03-20 10:00:00',
    is_origin: 0
  }
])

// 初始化消息提示
onMounted(() => {
  setMessageToastRef(messageToastRef.value)
})

// 工具方法
const getRoleName = (roleId: number) => {
  const role = roleList.value.find(r => r.id === roleId)
  return role ? role.role_name : '未知角色'
}

const formatDate = (dateStr: string | null) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 管理员管理方法
const editAdmin = (admin: any) => {
  adminForm.value = {
    id: admin.id,
    nickname: admin.nickname,
    username: admin.username,
    password: '',
    password_confirm: '',
    avatar: admin.avatar,
    role_id: admin.role_id,
    status: admin.status
  }
  showEditAdminModal.value = true
}

const deleteAdmin = async (admin: any) => {
  if (admin.is_origin === 1) {
    error('系统管理员不能删除')
    return
  }

  const confirmed = await confirmDelete(
    admin.nickname,
    '管理员'
  )

  if (confirmed) {
    try {
      const index = adminList.value.findIndex(a => a.id === admin.id)
      if (index > -1) {
        adminList.value.splice(index, 1)
        success('管理员删除成功')
      }
    } catch (err) {
      error('删除失败，请重试')
    }
  }
}

const toggleAdminStatus = async (admin: any) => {
  const newStatus = admin.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '启用' : '禁用'

  const confirmed = await confirmStatusChange(
    admin.nickname,
    statusText,
    '管理员'
  )

  if (confirmed) {
    try {
      admin.status = newStatus
      admin.updated_at = new Date().toISOString().substring(0, 19).replace('T', ' ')
      success(`管理员${statusText}成功`)
    } catch (err) {
      error(`${statusText}失败，请重试`)
    }
  }
}

const saveAdmin = async () => {
  try {
    // 表单验证
    if (!adminForm.value.nickname.trim()) {
      error('请输入管理员昵称')
      return
    }
    if (!adminForm.value.username.trim()) {
      error('请输入登录账号')
      return
    }
    if (!adminForm.value.role_id) {
      error('请选择所属角色')
      return
    }

    // 新增模式需要验证密码
    if (!adminForm.value.id) {
      if (!adminForm.value.password) {
        error('请输入登录密码')
        return
      }
      if (adminForm.value.password !== adminForm.value.password_confirm) {
        error('两次输入的密码不一致')
        return
      }
      if (adminForm.value.password.length < 6) {
        error('密码长度至少6位')
        return
      }
    } else {
      // 编辑模式，如果输入了密码需要验证确认密码
      if (adminForm.value.password && adminForm.value.password !== adminForm.value.password_confirm) {
        error('两次输入的密码不一致')
        return
      }
      if (adminForm.value.password && adminForm.value.password.length < 6) {
        error('密码长度至少6位')
        return
      }
    }

    // 检查用户名是否重复
    const existingAdmin = adminList.value.find(admin =>
      admin.username === adminForm.value.username && admin.id !== adminForm.value.id
    )
    if (existingAdmin) {
      error('登录账号已存在，请更换')
      return
    }

    if (adminForm.value.id) {
      // 编辑模式
      const index = adminList.value.findIndex(a => a.id === adminForm.value.id)
      if (index > -1) {
        adminList.value[index] = {
          ...adminList.value[index],
          nickname: adminForm.value.nickname,
          username: adminForm.value.username,
          avatar: adminForm.value.avatar,
          role_id: adminForm.value.role_id,
          status: adminForm.value.status,
          updated_at: new Date().toISOString().substring(0, 19).replace('T', ' ')
        }
      }
      showEditAdminModal.value = false
      success('管理员信息修改成功')
    } else {
      // 新增模式
      const newAdmin = {
        id: Math.max(...adminList.value.map(a => a.id)) + 1,
        nickname: adminForm.value.nickname,
        username: adminForm.value.username,
        avatar: adminForm.value.avatar,
        role_id: adminForm.value.role_id,
        status: adminForm.value.status,
        login_ip: '',
        login_time: null,
        created_at: new Date().toISOString().substring(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().substring(0, 19).replace('T', ' '),
        is_origin: 0
      }
      adminList.value.push(newAdmin)
      showAddAdminModal.value = false
      success('管理员添加成功')
    }

    // 重置表单
    resetAdminForm()
  } catch (err) {
    error('保存失败，请重试')
  }
}

const resetAdminForm = () => {
  adminForm.value = {
    id: null,
    nickname: '',
    username: '',
    password: '',
    password_confirm: '',
    avatar: '',
    role_id: '',
    status: 1
  }
}
</script>

<style scoped>
/* CSS变量定义 - 商务典雅蓝色主题系统 */
:root {
  --primary-deep-blue: #0D1B2A;
  --primary-business-blue: #1B365D;
  --primary-steel-blue: #415A77;
  --primary-ocean-blue: #2E5984;
  --secondary-steel-blue: #778DA9;
  --secondary-elegant-blue: #E0E1DD;
  --secondary-light-blue-gray: #F1F3F4;
  --accent-platinum: #C7D2DD;
  --accent-warm-silver: #B8C5D1;
  --accent-soft-gold: #E8B86D;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --text-primary: #0D1B2A;
  --text-secondary: #415A77;
  --text-light: #778DA9;
  --text-muted: #B8C5D1;
  --shadow-light: rgba(13, 27, 42, 0.08);
  --shadow-medium: rgba(13, 27, 42, 0.15);
  --shadow-strong: rgba(13, 27, 42, 0.25);
}

.page-container {
  padding: 24px;
  background: var(--secondary-light-blue-gray);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--shadow-light);
  overflow: hidden;
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--secondary-light-blue-gray);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-body {
  padding: 0;
}

.card-actions {
  display: flex;
  gap: 12px;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  gap: 6px;
}

.btn:disabled,
.btn.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-primary {
  background: var(--primary-steel-blue);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-business-blue);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(65, 90, 119, 0.3);
}

.btn-secondary {
  background: var(--accent-platinum);
  color: var(--text-secondary);
}

.btn-secondary:hover {
  background: var(--accent-warm-silver);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
}

.btn-warning:hover {
  background: #d4941f;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background: #e85555;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.icon {
  font-size: 14px;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.table th {
  background: var(--secondary-light-blue-gray);
  color: var(--text-secondary);
  font-weight: 600;
  padding: 16px;
  text-align: left;
  border-bottom: 2px solid var(--accent-platinum);
  white-space: nowrap;
}

.table td {
  padding: 16px;
  border-bottom: 1px solid rgba(199, 210, 221, 0.3);
  color: var(--text-primary);
  vertical-align: middle;
}

.table tr:hover {
  background: rgba(65, 90, 119, 0.02);
}

/* 管理员信息样式 */
.admin-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-steel-blue), var(--primary-ocean-blue));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  background-size: cover;
  background-position: center;
}

/* 状态徽章 */
.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.status-active {
  background: rgba(103, 194, 58, 0.1);
  color: var(--success-color);
}

.status-disabled {
  background: rgba(144, 147, 153, 0.1);
  color: var(--info-color);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 时间显示 */
.login-time {
  color: var(--text-secondary);
}

.never-login {
  color: var(--text-muted);
  font-style: italic;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(13, 27, 42, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px var(--shadow-strong);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

.modal-lg {
  max-width: 800px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--secondary-light-blue-gray);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-light);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: var(--secondary-light-blue-gray);
  color: var(--text-secondary);
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--secondary-light-blue-gray);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式 */
.admin-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
}

.required {
  color: var(--danger-color);
}

.form-control {
  padding: 12px 16px;
  border: 1px solid var(--accent-platinum);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-steel-blue);
  box-shadow: 0 0 0 3px rgba(65, 90, 119, 0.1);
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-hint {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 4px;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 20px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

.radio-item input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-steel-blue);
}

.radio-label {
  color: var(--text-secondary);
  user-select: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .modal-dialog {
    width: 95%;
    margin: 20px;
  }

  .modal-lg {
    max-width: none;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .table-container {
    overflow-x: auto;
  }

  .table {
    min-width: 800px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .btn-sm {
    padding: 8px 12px;
    font-size: 12px;
  }

  .radio-group {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .modal-body {
    padding: 16px;
  }

  .modal-header,
  .modal-footer {
    padding: 16px;
  }

  .card-header {
    padding: 16px;
  }

  .table th,
  .table td {
    padding: 12px 8px;
    font-size: 12px;
  }

  .admin-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}
</style>