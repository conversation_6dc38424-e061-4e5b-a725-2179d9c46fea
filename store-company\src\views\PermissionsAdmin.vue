<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">管理员管理</h1>
      <p class="page-description">管理系统管理员账户，包括账户创建、权限分配、状态管理等</p>
    </div>

    <!-- 操作栏 -->
    <div class="page-actions">
      <div class="search-box">
        <input 
          v-model="searchKeyword" 
          type="text" 
          placeholder="搜索管理员姓名、邮箱..." 
          class="search-input"
        />
        <button class="search-btn">🔍</button>
      </div>
      
      <div class="action-buttons">
        <button class="btn btn-primary" @click="openAddDialog">
          <span class="btn-icon">+</span>
          新增管理员
        </button>
        <button class="btn btn-outline">
          <span class="btn-icon">📤</span>
          导出数据
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-row">
      <div class="stat-item">
        <div class="stat-icon">👥</div>
        <div class="stat-info">
          <div class="stat-value">{{ adminStats.total }}</div>
          <div class="stat-label">总管理员</div>
        </div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">✅</div>
        <div class="stat-info">
          <div class="stat-value">{{ adminStats.active }}</div>
          <div class="stat-label">活跃用户</div>
        </div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">🔒</div>
        <div class="stat-info">
          <div class="stat-value">{{ adminStats.disabled }}</div>
          <div class="stat-label">已禁用</div>
        </div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">🌐</div>
        <div class="stat-info">
          <div class="stat-value">{{ adminStats.online }}</div>
          <div class="stat-label">在线用户</div>
        </div>
      </div>
    </div>

    <!-- 管理员列表 -->
    <div class="admin-list-container">
      <div class="list-header">
        <h3>管理员列表</h3>
        <div class="filter-controls">
          <select v-model="statusFilter" class="filter-select">
            <option value="">全部状态</option>
            <option value="active">活跃</option>
            <option value="disabled">禁用</option>
          </select>
          <select v-model="roleFilter" class="filter-select">
            <option value="">全部角色</option>
            <option value="super_admin">超级管理员</option>
            <option value="admin">管理员</option>
            <option value="operator">操作员</option>
          </select>
        </div>
      </div>

      <div class="admin-table-container">
        <table class="admin-table">
          <thead>
            <tr>
              <th>管理员信息</th>
              <th>角色</th>
              <th>状态</th>
              <th>最后登录</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="admin in filteredAdmins" :key="admin.id">
              <td>
                <div class="admin-info-cell">
                  <div class="admin-avatar">{{ admin.name.charAt(0) }}</div>
                  <div class="admin-details">
                    <div class="admin-name">{{ admin.name }}</div>
                    <div class="admin-email">{{ admin.email }}</div>
                    <div class="admin-phone">{{ admin.phone }}</div>
                  </div>
                </div>
              </td>
              <td>
                <span class="role-badge" :class="admin.role">
                  {{ getRoleText(admin.role) }}
                </span>
              </td>
              <td>
                <div class="status-cell">
                  <span class="status-badge" :class="admin.status">
                    {{ getStatusText(admin.status) }}
                  </span>
                  <span v-if="admin.isOnline" class="online-indicator">🟢</span>
                </div>
              </td>
              <td>{{ formatTime(admin.lastLogin) }}</td>
              <td>{{ formatDate(admin.createTime) }}</td>
              <td>
                <div class="table-actions">
                  <button class="table-action-btn" @click="editAdmin(admin)" title="编辑">✏️</button>
                  <button class="table-action-btn" @click="viewAdmin(admin)" title="查看详情">👁️</button>
                  <button 
                    class="table-action-btn" 
                    :class="{ warning: admin.status === 'active', success: admin.status === 'disabled' }"
                    @click="toggleAdminStatus(admin)" 
                    :title="admin.status === 'active' ? '禁用' : '启用'"
                  >
                    {{ admin.status === 'active' ? '🔒' : '🔓' }}
                  </button>
                  <button class="table-action-btn danger" @click="deleteAdmin(admin)" title="删除">🗑️</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <button class="page-btn" :disabled="currentPage === 1" @click="prevPage">上一页</button>
      <span class="page-info">第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
      <button class="page-btn" :disabled="currentPage === totalPages" @click="nextPage">下一页</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

const searchKeyword = ref('')
const statusFilter = ref('')
const roleFilter = ref('')
const currentPage = ref(1)
const pageSize = 10

const adminStats = reactive({
  total: 8,
  active: 6,
  disabled: 2,
  online: 3
})

const admins = reactive([
  {
    id: 1,
    name: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    role: 'super_admin',
    status: 'active',
    isOnline: true,
    lastLogin: new Date(),
    createTime: new Date('2023-01-01')
  },
  {
    id: 2,
    name: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    role: 'admin',
    status: 'active',
    isOnline: false,
    lastLogin: new Date(Date.now() - 3600000),
    createTime: new Date('2023-02-15')
  },
  {
    id: 3,
    name: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    role: 'operator',
    status: 'active',
    isOnline: true,
    lastLogin: new Date(Date.now() - 1800000),
    createTime: new Date('2023-03-20')
  },
  {
    id: 4,
    name: '王五',
    email: '<EMAIL>',
    phone: '13800138003',
    role: 'operator',
    status: 'disabled',
    isOnline: false,
    lastLogin: new Date(Date.now() - 86400000),
    createTime: new Date('2023-04-10')
  }
])

const filteredAdmins = computed(() => {
  let filtered = admins
  
  if (searchKeyword.value) {
    filtered = filtered.filter(admin => 
      admin.name.includes(searchKeyword.value) || 
      admin.email.includes(searchKeyword.value)
    )
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(admin => admin.status === statusFilter.value)
  }
  
  if (roleFilter.value) {
    filtered = filtered.filter(admin => admin.role === roleFilter.value)
  }
  
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filtered.slice(start, end)
})

const totalPages = computed(() => {
  let filtered = admins
  
  if (searchKeyword.value) {
    filtered = filtered.filter(admin => 
      admin.name.includes(searchKeyword.value) || 
      admin.email.includes(searchKeyword.value)
    )
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(admin => admin.status === statusFilter.value)
  }
  
  if (roleFilter.value) {
    filtered = filtered.filter(admin => admin.role === roleFilter.value)
  }
  
  return Math.ceil(filtered.length / pageSize)
})

const getRoleText = (role: string) => {
  const roleMap = {
    super_admin: '超级管理员',
    admin: '管理员',
    operator: '操作员'
  }
  return roleMap[role as keyof typeof roleMap] || '未知'
}

const getStatusText = (status: string) => {
  const statusMap = {
    active: '活跃',
    disabled: '禁用'
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString()
}

const formatTime = (date: Date) => {
  const now = Date.now()
  const diff = now - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return date.toLocaleDateString()
}

const openAddDialog = () => {
  console.log('打开新增管理员对话框')
}

const editAdmin = (admin: any) => {
  console.log('编辑管理员:', admin)
}

const viewAdmin = (admin: any) => {
  console.log('查看管理员详情:', admin)
}

const toggleAdminStatus = (admin: any) => {
  const newStatus = admin.status === 'active' ? 'disabled' : 'active'
  const action = newStatus === 'disabled' ? '禁用' : '启用'
  
  if (confirm(`确定要${action}管理员"${admin.name}"吗？`)) {
    admin.status = newStatus
    if (newStatus === 'disabled') {
      admin.isOnline = false
    }
  }
}

const deleteAdmin = (admin: any) => {
  if (confirm(`确定要删除管理员"${admin.name}"吗？此操作不可恢复！`)) {
    const index = admins.findIndex(a => a.id === admin.id)
    if (index > -1) {
      admins.splice(index, 1)
      adminStats.total--
    }
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}
</script>

<style scoped>
/* CSS变量定义 - 商务典雅蓝色主题系统 */
:root {
  --primary-deep-blue: #0D1B2A;
  --primary-business-blue: #1B365D;
  --primary-steel-blue: #415A77;
  --secondary-steel-blue: #778DA9;
  --secondary-elegant-blue: #E0E1DD;
  --secondary-light-blue-gray: #F1F3F4;
  --accent-platinum: #C7D2DD;
  --accent-warm-silver: #B8C5D1;
  --accent-soft-gold: #E8B86D;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --text-primary: #0D1B2A;
  --text-secondary: #415A77;
  --text-light: #778DA9;
  --text-muted: #B8C5D1;
  --shadow-light: rgba(13, 27, 42, 0.08);
  --shadow-medium: rgba(13, 27, 42, 0.15);
}

.page-container {
  padding: 24px;
  background: var(--secondary-light-blue-gray);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
}

/* 操作栏 */
.page-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.search-box {
  display: flex;
  align-items: center;
  position: relative;
}

.search-input {
  width: 300px;
  height: 40px;
  padding: 0 40px 0 16px;
  border: 1px solid var(--accent-platinum);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-steel-blue);
  box-shadow: 0 0 0 3px rgba(65, 90, 119, 0.1);
}

.search-btn {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: var(--text-light);
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background: var(--primary-steel-blue);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-business-blue);
  transform: translateY(-1px);
}

.btn-outline {
  background: white;
  color: var(--text-secondary);
  border: 1px solid var(--accent-platinum);
}

.btn-outline:hover {
  background: var(--secondary-light-blue-gray);
}

.btn-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 统计行 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--shadow-light);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px var(--shadow-medium);
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 管理员列表容器 */
.admin-list-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px var(--shadow-light);
  margin-bottom: 24px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--secondary-light-blue-gray);
}

.list-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--accent-platinum);
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-steel-blue);
}

/* 管理员表格 */
.admin-table-container {
  overflow-x: auto;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th {
  text-align: left;
  padding: 12px 16px;
  font-weight: 600;
  color: var(--text-secondary);
  border-bottom: 2px solid var(--secondary-light-blue-gray);
  font-size: 14px;
}

.admin-table td {
  padding: 16px;
  border-bottom: 1px solid rgba(65, 90, 119, 0.1);
  font-size: 14px;
  color: var(--text-primary);
}

.admin-table tr:hover {
  background: var(--secondary-light-blue-gray);
}

.admin-info-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--accent-soft-gold) 0%, var(--accent-warm-silver) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: var(--primary-deep-blue);
  font-size: 16px;
}

.admin-details .admin-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.admin-details .admin-email {
  font-size: 13px;
  color: var(--text-light);
  margin-bottom: 2px;
}

.admin-details .admin-phone {
  font-size: 13px;
  color: var(--text-muted);
}

.role-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.role-badge.super_admin {
  background: rgba(245, 108, 108, 0.1);
  color: var(--danger-color);
}

.role-badge.admin {
  background: rgba(230, 162, 60, 0.1);
  color: var(--warning-color);
}

.role-badge.operator {
  background: rgba(144, 147, 153, 0.1);
  color: var(--info-color);
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: rgba(103, 194, 58, 0.1);
  color: var(--success-color);
}

.status-badge.disabled {
  background: rgba(144, 147, 153, 0.1);
  color: var(--info-color);
}

.online-indicator {
  font-size: 10px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.table-action-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: var(--secondary-light-blue-gray);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-action-btn:hover {
  background: var(--accent-platinum);
}

.table-action-btn.danger:hover {
  background: var(--danger-color);
  color: white;
}

.table-action-btn.warning:hover {
  background: var(--warning-color);
  color: white;
}

.table-action-btn.success:hover {
  background: var(--success-color);
  color: white;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid var(--accent-platinum);
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.page-btn:hover:not(:disabled) {
  background: var(--primary-steel-blue);
  color: white;
  border-color: var(--primary-steel-blue);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .page-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .search-input {
    width: 100%;
  }
  
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .filter-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .filter-select {
    flex: 1;
  }
}
</style>