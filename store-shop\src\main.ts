import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './stores'

// 引入 dayjs 配置
import './utils/dayjs'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
// 引入自定义主题色，必须在Element Plus样式之后引入，以便覆盖默认样式
import './assets/theme.css'
// 引入全局表单提示样式
import './assets/styles/form-tips.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import type { Component } from 'vue'
import './style.css'

// 引入权限控制指令
import { vPermission } from '@/composables/usePermission'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component as Component)
}

app.use(router)
app.use(pinia)
app.use(ElementPlus, {
  locale: zhCn
})

// 注册全局权限指令
app.directive('permission', vPermission)

// 预加载关键组件 - 在空闲时间进行
requestIdleCallback(() => {
  // 预加载 ECharts 相关模块
  import('echarts/core').catch(() => {})
  import('echarts/renderers').catch(() => {})
  import('echarts/charts').catch(() => {})
  import('vue-echarts').catch(() => {})
  
  // 延迟预加载财务报表组件，避免影响首屏加载
  setTimeout(() => {
    import('@/components/finance/ReportPageLayout.vue').catch(() => {})
    import('@/components/finance/charts/SalesChart.vue').catch(() => {})
  }, 3000) // 3秒后开始预加载
})

app.mount('#app')
