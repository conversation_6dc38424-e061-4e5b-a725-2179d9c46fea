<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <!-- <div class="auth-logo">🏢</div> -->
        <h1 class="auth-title">企业登录</h1>
        <!-- <p class="auth-subtitle">企业级SaaS管理平台</p> -->
      </div>
      
      <form @submit.prevent="handleLogin" class="auth-form">
        <div class="form-group">
          <label class="form-label">手机号码</label>
          <input 
            type="text" 
            class="form-input" 
            placeholder="请输入手机号码"
            v-model="loginForm.username"
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">登录密码</label>
          <div class="password-container">
            <input 
              :type="showPassword ? 'text' : 'password'" 
              class="form-input" 
              placeholder="请输入登录密码"
              v-model="loginForm.password"
            />
            <button 
              type="button" 
              class="password-toggle" 
              @click="togglePassword"
            >
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
        </div>
        
        <div class="form-group" v-show="showCaptcha">
          <label class="form-label">图形验证码</label>
          <div class="captcha-container">
            <input 
              type="text" 
              class="form-input captcha-input" 
              placeholder="请输入验证码"
              v-model="loginForm.captcha"
            />
            <button 
              type="button" 
              class="captcha-btn" 
              @click="refreshCaptcha"
            >
              {{ captchaCode }}
            </button>
          </div>
          <!-- <p class="captcha-hint">连续登录失败超过2次，请输入验证码</p> -->
        </div>
        
        <div class="remember-forgot">
          <label class="remember-me">
            <input type="checkbox" v-model="loginForm.remember">
            记住登录状态
          </label>
          <router-link to="/forgot-password" class="forgot-password">忘记密码?</router-link>
        </div>
        
        <button type="submit" class="submit-btn" :disabled="loading">
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? '登录中...' : '立即登录' }}
        </button>
        
        <div class="auth-switch">
          <span class="auth-switch-text">没有账号？</span>
          <router-link to="/register" class="auth-switch-link">立即注册</router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

const loginForm = ref({
  username: '',
  password: '',
  captcha: '',
  remember: false
})

const loading = ref(false)
const showPassword = ref(false)
const captchaCode = ref('')
const loginFailureCount = ref(0)
const showCaptcha = ref(false)

// 从localStorage获取登录失败次数
const getLoginFailureCount = () => {
  const count = localStorage.getItem('loginFailureCount')
  return count ? parseInt(count) : 0
}

// 设置登录失败次数到localStorage
const setLoginFailureCount = (count: number) => {
  localStorage.setItem('loginFailureCount', count.toString())
  loginFailureCount.value = count
  showCaptcha.value = count > 2
}

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const refreshCaptcha = () => {
  // 生成四位随机验证码（大写字母和数字）
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  captchaCode.value = result
}


// 页面初始化时生成验证码
onMounted(() => {
  // 初始化登录失败次数
  const count = getLoginFailureCount()
  setLoginFailureCount(count)
  
  // 如果需要显示验证码，则生成验证码
  if (showCaptcha.value) {
    refreshCaptcha()
  }
})

const handleLogin = async () => {
  // 表单验证
  if (!loginForm.value.username.trim()) {
    ElMessage.warning('请输入手机号码')
    return
  }
  
  if (!loginForm.value.password.trim()) {
    ElMessage.warning('请输入登录密码')
    return
  }
  
  // 只有在显示验证码时才需要验证
  if (showCaptcha.value) {
    if (!loginForm.value.captcha.trim()) {
      ElMessage.warning('请输入验证码')
      return
    }
    
    // 验证验证码
    if (loginForm.value.captcha.trim().toUpperCase() !== captchaCode.value) {
      ElMessage.error('验证码错误')
      // 验证码错误后自动刷新
      refreshCaptcha()
      loginForm.value.captcha = ''
      return
    }
  }
  
  loading.value = true
  
  try {
    // 使用auth store的login方法
    const success = await authStore.login({
      username: loginForm.value.username.trim(),
      password: loginForm.value.password.trim(),
      captcha: loginForm.value.captcha.trim(),
      remember: loginForm.value.remember
    })
    
    if (success) {
      // 登录成功，清除失败记录
      localStorage.removeItem('loginFailureCount')
      setLoginFailureCount(0)
      
      // 跳转到仪表板
      router.push('/dashboard')
    } else {
      // 登录失败，增加失败次数
      const newCount = loginFailureCount.value + 1
      setLoginFailureCount(newCount)
      
      ElMessage.error(`手机号码或密码错误${newCount > 3 ? '，请输入验证码' : ''}`)
      
      // 如果需要显示验证码，则刷新验证码
      if (showCaptcha.value) {
        refreshCaptcha()
        loginForm.value.captcha = ''
      }
    }
  } catch (error) {
    // 系统错误也增加失败次数
    const newCount = loginFailureCount.value + 1
    setLoginFailureCount(newCount)
    
    ElMessage.error('登录失败，请重试')
    
    // 如果需要显示验证码，则刷新验证码
    if (showCaptcha.value) {
      refreshCaptcha()
      loginForm.value.captcha = ''
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0D1B2A 0%, #1B365D 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(ellipse at 25% 20%, rgba(232, 184, 109, 0.08) 0%, transparent 60%),
    radial-gradient(ellipse at 75% 80%, rgba(199, 210, 221, 0.12) 0%, transparent 60%),
    radial-gradient(ellipse at 50% 50%, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(0.5deg); }
  66% { transform: translate(-20px, 20px) rotate(-0.5deg); }
}

.auth-card {
  background: rgba(255, 255, 255, 0.92);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 24px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  width: 100%;
  max-width: 480px;
  padding: 48px 40px;
  position: relative;
  z-index: 5;
  animation: cardSlideUp 0.8s ease-out;
}

@keyframes cardSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;
}

.auth-logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #E8B86D 0%, #B8C5D1 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  box-shadow: 0 4px 16px rgba(13, 27, 42, 0.15);
  font-size: 36px;
  color: #0D1B2A;
  font-weight: bold;
}

.auth-title {
  font-size: 28px;
  font-weight: 600;
  color: #0D1B2A;
  margin-bottom: 8px;
  line-height: 1.2;
}

.auth-subtitle {
  font-size: 16px;
  color: #778DA9;
  font-weight: 400;
}

.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 15px;
  font-weight: 500;
  color: #415A77;
}

.form-input {
  width: 100%;
  height: 48px;
  border: 2px solid rgba(65, 90, 119, 0.1);
  border-radius: 12px;
  padding: 0 16px;
  font-size: 16px;
  color: #0D1B2A;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #415A77;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 4px rgba(65, 90, 119, 0.1);
}

.form-input::placeholder {
  color: #B8C5D1;
}

.submit-btn {
  width: 100%;
  height: 52px;
  background: linear-gradient(135deg, #1B365D 0%, #0D1B2A 100%);
  border: none;
  border-radius: 14px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(13, 27, 42, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(13, 27, 42, 0.3);
}

.submit-btn:active {
  transform: translateY(0);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 密码容器 */
.password-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #778DA9;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
}

.password-toggle:hover {
  color: #415A77;
}

/* 验证码容器 */
.captcha-container {
  display: flex;
  gap: 12px;
}

.captcha-input {
  flex: 1;
}

.captcha-btn {
  width: 120px;
  height: 48px;
  background: linear-gradient(135deg, #E8B86D 0%, #B8C5D1 100%);
  border: none;
  border-radius: 12px;
  color: #0D1B2A;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.captcha-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 27, 42, 0.15);
}

/* 记住我选项 */
.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.remember-me {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #415A77;
}

.remember-me input {
  margin-right: 8px;
}

.forgot-password {
  font-size: 14px;
  color: #415A77;
  text-decoration: none;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #0D1B2A;
  text-decoration: underline;
}

/* 页面切换链接样式 */
.auth-switch {
  text-align: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid rgba(65, 90, 119, 0.1);
}

.auth-switch-text {
  color: #778DA9;
  font-size: 14px;
  margin-right: 8px;
}

.auth-switch-link {
  color: #415A77;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.auth-switch-link:hover {
  color: #0D1B2A;
  text-decoration: underline;
}

/* 加载状态 */
.submit-btn.loading {
  pointer-events: none;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 验证码提示样式 */
.captcha-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #E6A23C;
  text-align: center;
  font-weight: 500;
}
</style>