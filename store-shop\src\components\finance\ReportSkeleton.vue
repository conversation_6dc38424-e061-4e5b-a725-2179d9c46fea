<template>
  <div class="report-skeleton">
    <!-- 顶部统计卡片骨架 -->
    <el-row :gutter="20" class="summary-skeleton">
      <el-col :span="6" v-for="i in 4" :key="i">
        <el-card class="summary-card-skeleton">
          <div class="skeleton-content">
            <el-skeleton-item variant="circle" class="skeleton-icon" />
            <div class="skeleton-info">
              <el-skeleton-item variant="text" class="skeleton-title" />
              <el-skeleton-item variant="text" class="skeleton-value" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域骨架 -->
    <div class="charts-skeleton">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card class="chart-card-skeleton">
            <template #header>
              <div class="chart-header-skeleton">
                <el-skeleton-item variant="text" class="chart-title-skeleton" />
                <div class="chart-controls-skeleton">
                  <el-skeleton-item variant="button" style="width: 80px; height: 28px;" />
                  <el-skeleton-item variant="button" style="width: 80px; height: 28px; margin-left: 8px;" />
                </div>
              </div>
            </template>
            <div class="chart-content-skeleton">
              <el-skeleton-item variant="rect" class="chart-rect-skeleton" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="chart-card-skeleton">
            <template #header>
              <el-skeleton-item variant="text" class="chart-title-skeleton" />
            </template>
            <div class="chart-content-skeleton">
              <el-skeleton-item variant="circle" class="pie-chart-skeleton" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 表格区域骨架 -->
    <div class="table-skeleton">
      <el-card>
        <template #header>
          <el-skeleton-item variant="text" class="table-title-skeleton" />
        </template>
        <el-skeleton :rows="8" animated />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
// 不需要任何逻辑，纯展示骨架屏
</script>

<style scoped lang="scss">
.report-skeleton {
  padding: 20px;

  .summary-skeleton {
    margin-bottom: 20px;

    .summary-card-skeleton {
      .skeleton-content {
        display: flex;
        align-items: center;
        padding: 10px;

        .skeleton-icon {
          width: 48px;
          height: 48px;
          margin-right: 16px;
        }

        .skeleton-info {
          flex: 1;

          .skeleton-title {
            width: 80px;
            height: 16px;
            margin-bottom: 8px;
          }

          .skeleton-value {
            width: 120px;
            height: 24px;
          }
        }
      }
    }
  }

  .charts-skeleton {
    margin-bottom: 20px;

    .chart-card-skeleton {
      .chart-header-skeleton {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .chart-title-skeleton {
          width: 120px;
          height: 20px;
        }

        .chart-controls-skeleton {
          display: flex;
        }
      }

      .chart-content-skeleton {
        height: 350px;
        display: flex;
        align-items: center;
        justify-content: center;

        .chart-rect-skeleton {
          width: 100%;
          height: 300px;
        }

        .pie-chart-skeleton {
          width: 200px;
          height: 200px;
        }
      }
    }
  }

  .table-skeleton {
    .table-title-skeleton {
      width: 100px;
      height: 20px;
    }
  }
}
</style>