/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AdminFormDialog: typeof import('./components/AdminFormDialog.vue')['default']
    ConfirmDialog: typeof import('./components/ConfirmDialog.vue')['default']
    Header: typeof import('./components/Header.vue')['default']
    MessageToast: typeof import('./components/MessageToast.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ShopFormDialog: typeof import('./components/ShopFormDialog.vue')['default']
    Sidebar: typeof import('./components/Sidebar.vue')['default']
  }
}
