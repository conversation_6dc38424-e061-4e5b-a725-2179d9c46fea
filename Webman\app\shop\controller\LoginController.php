<?php

namespace app\shop\controller;

use app\model\CsShopAdmin;
use app\model\CsRealtimeMessage;
use app\service\Menu;
use support\Redis;
use support\Request;

class LoginController
{
    use Menu;

    protected $noNeedLogin = ['login'];

    public function login(Request $request)
    {
        $username = $request->post('username','');
        $password = $request->post('password','');
        if (empty($username) || empty($password)) {
            return fail('参数错误');
        }
        $admin = CsShopAdmin::where(['username'=>$username])->first();
        if (empty($admin)) {
            return fail('用户不存在');
        }
        if (md5($admin->password) != $password) {
            return fail('账号与密码不匹配');
        }
        if ($admin->status == 0) {
            return fail('该账号已经被冻结，请联系管理员');
        }
        // 判断公司是否过期
        if ($admin->csShop()->first()->csCompany->expired_at < date('Y-m-d')) {
            return fail('公司授权期限已过，请联系管理员');
        }
        $admin->login_ip = $request->getRealIp();
        $admin->login_time = date('Y-m-d H:i:s');
        $admin->save();
        $admin->avatar = get_image_url($admin->avatar);
        $admin->role_text = $admin?->csShopAdminRole?->role_name;
        $rolePermission = $admin?->csShopAdminRole?->role_permission;
        unset($admin->csShopAdminRole);
        // 用户身份直接存入redis
        $token = md5('shop' . $admin->id . $admin->username . $admin->login_time);
        Redis::setEx('shop:' . $token, config('app.token_expire_time'), $admin->id . '-' . $admin->role_id . '-' . $admin->shop_id);
        $admin->token = $token;
        // 获取该门店的未读消息数量
        $admin->unread_message_count = CsRealtimeMessage::getUnreadCount($admin->shop_id);
        // $admin->menu = $this->getMenuList(2, $rolePermission);
        return success($admin);
    }

}
