import service from './api'
import type { 
  RealtimeMessage, 
  RealtimeMessageListParams, 
  ApiResponse, 
  PaginatedResponse,
  MarkReadResponse,
  DeleteResponse
} from '@/types/realtime'

const API_PREFIX = '/shop/CsRealtimeMessage'

/**
 * 获取实时消息列表
 */
export const getRealtimeMessageList = (params: RealtimeMessageListParams): Promise<PaginatedResponse<RealtimeMessage>> => {
  return service.get(`${API_PREFIX}/index`, { params })
}

/**
 * 获取消息详情
 */
export const getRealtimeMessageDetail = (id: number): Promise<RealtimeMessage> => {
  return service.get(`${API_PREFIX}/detail`, { params: { id } })
}

/**
 * 标记消息为已读
 */
export const markMessageRead = (id: number): Promise<MarkReadResponse> => {
  return service.post(`${API_PREFIX}/markRead`, { id })
}

/**
 * 批量标记消息为已读
 */
export const batchMarkMessagesRead = (ids: number[]): Promise<MarkReadResponse> => {
  return service.post(`${API_PREFIX}/batchMarkRead`, { ids })
}

/**
 * 全部标记为已读
 */
export const markAllMessagesRead = (): Promise<MarkReadResponse> => {
  return service.post(`${API_PREFIX}/markAllRead`)
}

/**
 * 获取未读消息数量
 */
export const getUnreadMessageCount = (): Promise<number> => {
  return service.get(`${API_PREFIX}/unreadCount`)
}

/**
 * 获取最新消息
 */
export const getLatestMessages = (limit: number = 5): Promise<RealtimeMessage[]> => {
  return service.get(`${API_PREFIX}/latest`, { params: { limit } })
}

/**
 * 获取消息统计
 */
export const getMessageStatistics = (type: 'today' | 'week' | 'month' = 'today'): Promise<any> => {
  return service.get(`${API_PREFIX}/statistics`, { params: { type } })
}

/**
 * 创建消息（内部接口，用于WebSocket接收到消息时调用）
 */
export const createRealtimeMessage = (data: {
  shop_id: number
  message_type: string
  title: string
  content: string
  extra_data?: Record<string, any>
}): Promise<RealtimeMessage> => {
  return service.post(`${API_PREFIX}/create`, data)
}

/**
 * 删除消息
 */
export const deleteRealtimeMessage = (id: number): Promise<DeleteResponse> => {
  return service.post(`${API_PREFIX}/delete`, { id })
}

/**
 * 批量删除消息
 */
export const batchDeleteMessages = (ids: number[]): Promise<DeleteResponse> => {
  return service.post(`${API_PREFIX}/batchDelete`, { ids })
}

/**
 * 清理过期消息
 */
export const cleanupExpiredMessages = (retentionDays: number = 30): Promise<ApiResponse<{ deleted_count: number }>> => {
  return service.post(`${API_PREFIX}/cleanup`, { retention_days: retentionDays })
}

/**
 * 获取消息类型选项
 */
export const getMessageTypeOptions = (): Promise<Record<string, string>> => {
  return service.get(`${API_PREFIX}/getTypeOptions`)
}

/**
 * 获取已读状态选项
 */
export const getReadStatusOptions = (): Promise<Record<string, string>> => {
  return service.get(`${API_PREFIX}/getReadStatusOptions`)
}

// 扩展的API接口

/**
 * 获取今日消息统计
 */
export const getTodayStatistics = (): Promise<{
  total: number
  unread: number
  by_type: Record<string, { name: string; total: number; unread: number }>
}> => {
  return getMessageStatistics('today')
}

/**
 * 获取本周消息统计
 */
export const getWeekStatistics = (): Promise<{
  total: number
  unread: number
  by_type: Record<string, { name: string; total: number; unread: number }>
  by_day: Array<{
    date: string
    weekday: string
    total: number
    unread: number
  }>
}> => {
  return getMessageStatistics('week')
}

/**
 * 获取本月消息统计
 */
export const getMonthStatistics = (): Promise<{
  total: number
  unread: number
  by_type: Record<string, { name: string; total: number; unread: number }>
  by_week: Array<{
    week: number
    start_date: string
    end_date: string
    total: number
    unread: number
  }>
}> => {
  return getMessageStatistics('month')
}

/**
 * 搜索消息
 */
export const searchMessages = (params: {
  keyword?: string
  message_type?: string
  is_read?: number
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}): Promise<PaginatedResponse<RealtimeMessage>> => {
  const searchParams: RealtimeMessageListParams = {
    page: params.page || 1,
    page_size: params.page_size || 15,
    sort_field: 'created_at',
    sort: 'desc'
  }

  // 添加搜索条件
  if (params.keyword) {
    // 在title和content中搜索
    searchParams.title = params.keyword
  }

  if (params.message_type) {
    searchParams.message_type = params.message_type as any
  }

  if (params.is_read !== undefined) {
    searchParams.is_read = params.is_read
  }

  if (params.start_date && params.end_date) {
    searchParams.created_at = [params.start_date, params.end_date]
  }

  return getRealtimeMessageList(searchParams)
}

/**
 * 根据消息类型获取消息列表
 */
export const getMessagesByType = (messageType: string, limit: number = 10): Promise<RealtimeMessage[]> => {
  return getRealtimeMessageList({
    page: 1,
    page_size: limit,
    message_type: messageType as any,
    sort_field: 'created_at',
    sort: 'desc'
  }).then(response => response.itemList)
}

/**
 * 获取未读消息列表
 */
export const getUnreadMessages = (limit: number = 20): Promise<RealtimeMessage[]> => {
  return getRealtimeMessageList({
    page: 1,
    page_size: limit,
    is_read: 0,
    sort_field: 'created_at',
    sort: 'desc'
  }).then(response => response.itemList)
}

/**
 * 获取紧急消息列表（呼叫服务类型）
 */
export const getUrgentMessages = (limit: number = 10): Promise<RealtimeMessage[]> => {
  return getMessagesByType('call_service', limit)
}

/**
 * 批量操作接口
 */
export const batchOperations = {
  /**
   * 批量标记已读
   */
  markRead: batchMarkMessagesRead,
  
  /**
   * 批量删除
   */
  delete: batchDeleteMessages,
  
  /**
   * 按类型批量标记已读
   */
  markReadByType: async (messageType: string): Promise<ApiResponse> => {
    // 先获取该类型的未读消息ID
    const messages = await getRealtimeMessageList({
      page: 1,
      page_size: 1000, // 大数量获取
      message_type: messageType as any,
      is_read: 0
    })
    
    if (messages.itemList.length > 0) {
      const ids = messages.itemList.map(msg => msg.id!).filter(id => id)
      return batchMarkMessagesRead(ids)
    }
    
    return Promise.resolve({ code: 1, msg: '没有未读消息', data: null })
  },
  
  /**
   * 按日期范围批量删除
   */
  deleteByDateRange: async (startDate: string, endDate: string): Promise<ApiResponse> => {
    // 先获取日期范围内的消息ID
    const messages = await getRealtimeMessageList({
      page: 1,
      page_size: 1000,
      created_at: [startDate, endDate]
    })
    
    if (messages.itemList.length > 0) {
      const ids = messages.itemList.map(msg => msg.id!).filter(id => id)
      return batchDeleteMessages(ids)
    }
    
    return Promise.resolve({ code: 1, msg: '没有符合条件的消息', data: null })
  }
}

/**
 * 消息导出接口
 */
export const exportMessages = (params: {
  start_date?: string
  end_date?: string
  message_type?: string
  is_read?: number
}): Promise<Blob> => {
  return service.get(`${API_PREFIX}/export`, { 
    params,
    responseType: 'blob'
  })
}

/**
 * 检查是否有新消息（轮询使用）
 */
export const checkNewMessages = (lastCheckTime: string): Promise<{
  hasNew: boolean
  count: number
  latest: RealtimeMessage[]
}> => {
  return service.get(`${API_PREFIX}/checkNew`, { 
    params: { last_check_time: lastCheckTime } 
  })
}

/**
 * 消息去重检查（用于防止重复处理）
 */
export const checkMessageDuplicate = (messageKey: string): Promise<{ isDuplicate: boolean }> => {
  return service.post(`${API_PREFIX}/checkDuplicate`, { message_key: messageKey })
}

/**
 * 预览模式：获取消息摘要
 */
export const getMessageSummary = (): Promise<{
  total: number
  unread: number
  urgent: number
  recent: RealtimeMessage[]
  typeStats: Record<string, number>
}> => {
  return service.get(`${API_PREFIX}/summary`)
}

// 默认导出
export default {
  // 基础CRUD
  getList: getRealtimeMessageList,
  getDetail: getRealtimeMessageDetail,
  create: createRealtimeMessage,
  delete: deleteRealtimeMessage,
  
  // 已读操作
  markRead: markMessageRead,
  batchMarkRead: batchMarkMessagesRead,
  markAllRead: markAllMessagesRead,
  
  // 统计和查询
  getUnreadCount: getUnreadMessageCount,
  getLatest: getLatestMessages,
  getStatistics: getMessageStatistics,
  search: searchMessages,
  
  // 批量操作
  batch: batchOperations,
  
  // 工具接口
  getTypeOptions: getMessageTypeOptions,
  getReadStatusOptions: getReadStatusOptions,
  export: exportMessages,
  cleanup: cleanupExpiredMessages
}