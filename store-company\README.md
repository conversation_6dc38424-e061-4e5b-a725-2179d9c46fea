# 授权企业管理系统

基于Vue 3 + TypeScript + Element Plus构建的现代化企业管理系统，采用商务典雅蓝色主题设计。

## 功能特性

- 🏢 **企业级架构**: 基于Vue 3 Composition API，支持大型项目开发
- 🎨 **商务典雅设计**: 深邃商务蓝色主题系统，专业美观
- 📱 **响应式设计**: 完美适配桌面端和移动端
- 🔐 **完整认证系统**: 登录、注册、权限管理
- 📊 **数据可视化**: ECharts图表展示，直观的数据分析
- 🏪 **门店管理**: 多门店统一管理，实时监控
- 👥 **权限管理**: 角色权限系统，精细化权限控制
- 🛠 **TypeScript**: 完整的类型定义，开发更安全

## 技术栈

- **前端框架**: Vue 3.4+ (Composition API)
- **开发语言**: TypeScript 5.3+
- **构建工具**: Vite 5.0+
- **UI框架**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由管理**: Vue Router 4.2+
- **图表库**: ECharts 5.4+
- **样式预处理**: SCSS
- **图标库**: Element Plus Icons

## 项目结构

```
store-company/
├── public/                 # 静态资源
├── src/
│   ├── assets/            # 资源文件
│   ├── components/        # 组件库
│   │   ├── layout/       # 布局组件
│   │   ├── dashboard/    # 仪表板组件
│   │   └── ...
│   ├── composables/       # 组合式函数
│   ├── layouts/          # 页面布局
│   ├── router/           # 路由配置
│   ├── services/         # API服务层
│   ├── stores/           # 状态管理
│   ├── styles/           # 样式文件
│   ├── types/            # 类型定义
│   ├── utils/            # 工具函数
│   ├── views/            # 页面组件
│   └── main.ts          # 应用入口
├── .env.development      # 开发环境配置
├── .env.production       # 生产环境配置
├── index.html           # HTML模板
├── package.json         # 依赖配置
├── tsconfig.json        # TypeScript配置
├── vite.config.ts       # Vite配置
└── README.md           # 项目文档
```

## 快速开始

### 环境要求

- Node.js 16.0+
- npm 7.0+ 或 yarn 1.22+

### 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 开发运行

```bash
# 启动开发服务器
npm run dev

# 或
yarn dev
```

访问 http://localhost:5174

### 构建部署

```bash
# 构建生产版本
npm run build

# 或
yarn build

# 预览构建结果
npm run preview
```

## 核心功能

### 🏠 仪表板
- 实时数据统计
- 多维度图表展示
- 快速操作入口
- 响应式布局设计

### 🏪 门店管理
- 门店信息管理
- 实时状态监控
- 营收数据统计
- 地理分布展示

### 👥 权限管理
- 角色权限配置
- 管理员账户管理
- 权限继承机制
- 安全访问控制

### 🔐 认证系统
- 用户登录注册
- 短信验证码
- 密码强度检测
- 自动登录状态管理

## 设计系统

### 色彩规范
- **主色调**: 深邃商务蓝 (#0D1B2A, #1B365D, #415A77)
- **辅助色**: 钢铁蓝与淡雅蓝灰 (#778DA9, #E0E1DD, #F1F3F4)
- **点缀色**: 精致金属色 (#C7D2DD, #B8C5D1, #E8B86D)
- **状态色**: 成功绿、警告橙、危险红、信息灰

### 组件规范
- 统一的卡片设计
- 一致的交互反馈
- 标准化的表单组件
- 专业的图表样式

## API接口

系统采用RESTful API设计，所有接口返回统一格式：

```typescript
interface ApiResponse<T> {
  code: number    // 1=成功, 其他=失败
  msg: string     // 响应消息
  data: T         // 响应数据
}
```

### 主要接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/dashboard/stats` - 仪表板统计
- `GET /api/shops` - 门店列表
- `GET /api/permissions/roles` - 角色列表

## 浏览器支持

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 开发说明

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 Vue 3 Composition API 最佳实践
- 组件采用 `<script setup>` 语法
- 使用 ESLint + Prettier 进行代码格式化

### 提交规范
- feat: 新功能
- fix: 修复问题
- docs: 文档修改
- style: 样式修改
- refactor: 代码重构
- test: 测试相关
- chore: 构建工具或依赖修改

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。