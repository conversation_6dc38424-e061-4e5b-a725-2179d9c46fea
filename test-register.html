<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册页面预览</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        :root {
            --primary-deep-blue: #0D1B2A;
            --primary-business-blue: #1B365D;
            --primary-steel-blue: #415A77;
            --secondary-steel-blue: #778DA9;
            --secondary-elegant-blue: #E0E1DD;
            --secondary-light-blue-gray: #F1F3F4;
            --accent-platinum: #C7D2DD;
            --accent-warm-silver: #B8C5D1;
            --accent-soft-gold: #E8B86D;
            --text-primary: #0D1B2A;
            --text-secondary: #415A77;
            --text-light: #778DA9;
            --text-muted: #B8C5D1;
            --glass-background: rgba(255, 255, 255, 0.92);
            --glass-border: rgba(255, 255, 255, 0.8);
            --shadow-deep: rgba(13, 27, 42, 0.25);
            --shadow-medium: rgba(13, 27, 42, 0.15);
            --shadow-light: rgba(13, 27, 42, 0.08);
            --gradient-primary: linear-gradient(135deg, var(--primary-business-blue) 0%, var(--primary-deep-blue) 100%);
            --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent-soft-gold) 0%, var(--accent-warm-silver) 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .register-container {
            min-height: 100vh;
            background: var(--gradient-primary);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .register-card {
            background: var(--glass-background);
            backdrop-filter: blur(25px) saturate(180%);
            -webkit-backdrop-filter: blur(25px) saturate(180%);
            border-radius: 28px;
            padding: 64px 52px;
            box-shadow: 
                0 35px 70px var(--shadow-deep),
                0 18px 36px var(--shadow-medium),
                0 8px 16px var(--shadow-light),
                inset 0 1px 0 rgba(255, 255, 255, 0.95),
                inset 0 -1px 0 rgba(255, 255, 255, 0.8);
            width: 600px;
            max-width: 92vw;
            position: relative;
            z-index: 10;
        }

        .login-header {
            text-align: center;
            margin-bottom: 48px;
        }

        .login-logo {
            width: 96px;
            height: 96px;
            background: var(--gradient-accent);
            border-radius: 24px;
            margin: 0 auto 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 42px;
            color: var(--primary-deep-blue);
            font-weight: 800;
        }

        .login-title {
            font-size: 36px;
            color: var(--text-primary);
            font-weight: 800;
            margin-bottom: 12px;
        }

        .login-subtitle {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 32px;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .form-item {
            margin-bottom: 12px;
        }

        .form-input {
            width: 100%;
            height: 58px;
            padding: 0 26px;
            border: 2px solid rgba(65, 90, 119, 0.25);
            border-radius: 18px;
            background: rgba(255, 255, 255, 0.95);
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 400;
            font-family: inherit;
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .verification-input-wrapper {
            display: flex;
            align-items: stretch;
            background: rgba(255, 255, 255, 0.98);
            border: 2px solid rgba(65, 90, 119, 0.25);
            border-radius: 18px;
            overflow: hidden;
            height: 58px;
        }

        .verification-input {
            flex: 1;
            height: 100%;
            padding: 0 26px;
            border: none;
            background: transparent;
            font-size: 16px;
            color: var(--text-primary);
            outline: none;
        }

        .verification-btn {
            padding: 0 24px;
            border: none;
            background: var(--gradient-primary);
            color: white;
            font-weight: 600;
            cursor: pointer;
            min-width: 120px;
        }

        .login-btn {
            width: 100%;
            height: 58px;
            background: var(--gradient-primary);
            border: none;
            border-radius: 18px;
            color: white;
            font-size: 17px;
            font-weight: 700;
            cursor: pointer;
            margin-top: 28px;
        }

        .login-links {
            text-align: center;
            margin-top: 32px;
        }

        .login-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="register-container">
            <div class="register-card">
                <div class="login-header">
                    <div class="login-logo">JR</div>
                    <h1 class="login-title">公司注册</h1>
                    <p class="login-subtitle">开启您的企业数字化管理之旅</p>
                </div>
                <form class="login-form" @submit.prevent="handleRegister">
                    <div class="form-item">
                        <input type="text" class="form-input" v-model="registerForm.companyName" 
                               placeholder="请输入公司名称" required>
                    </div>
                    <div class="form-item">
                        <input type="tel" class="form-input" v-model="registerForm.phone" 
                               placeholder="请输入手机号码" required>
                    </div>
                    <div class="form-item">
                        <select class="form-input" v-model="registerForm.industry" required>
                            <option value="">请选择行业类型</option>
                            <option value="restaurant">餐饮行业</option>
                            <option value="retail">零售行业</option>
                            <option value="service">服务行业</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <div class="verification-input-wrapper">
                            <input type="text" class="verification-input" 
                                   v-model="registerForm.verificationCode" 
                                   placeholder="请输入验证码" required>
                            <button type="button" class="verification-btn">获取验证码</button>
                        </div>
                    </div>
                    <div class="form-item">
                        <input type="password" class="form-input" v-model="registerForm.password" 
                               placeholder="请输入登录密码" required>
                    </div>
                    <div class="form-item">
                        <input type="password" class="form-input" v-model="registerForm.confirmPassword" 
                               placeholder="请再次输入密码" required>
                    </div>
                    <button type="submit" class="login-btn">立即注册</button>
                </form>
                <div class="login-links">
                    <a href="#" class="login-link">返回登录</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        createApp({
            data() {
                return {
                    registerForm: {
                        companyName: '',
                        phone: '',
                        industry: '',
                        verificationCode: '',
                        password: '',
                        confirmPassword: ''
                    }
                }
            },
            methods: {
                handleRegister() {
                    console.log('注册表单', this.registerForm);
                }
            }
        }).mount('#app');
    </script>
</body>
</html>