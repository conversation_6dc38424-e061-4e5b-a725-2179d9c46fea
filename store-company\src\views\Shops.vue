<template>
  <div class="page-container">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <h1 class="page-title">门店管理</h1>
      <p class="page-description">管理所有门店的基础信息运营状态</p>
    </div>

    <!-- 新增门店按钮 -->
    <div class="action-section">
      <button class="add-shop-btn" @click="handleAddShop">
        + 新增门店
      </button>
    </div>

    <!-- 门店数据表格 -->
    <div class="table-container">
      <table class="shop-table">
        <thead>
          <tr>
            <th>序号</th>
            <th>门店名称</th>
            <th>负责人</th>
            <th>联系手机</th>
            <th>门店地址</th>
            <th>服务电话</th>
            <th>营业状态</th>
            <th>营业时间</th>
            <th>创建时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(shop, index) in shopList" :key="shop.id">
            <td>{{ index + 1 }}</td>
            <td>
              <div class="shop-info">
                <div class="shop-main-name">{{ shop.name }}</div>
                <div class="shop-sub-name">{{ shop.subName }}</div>
              </div>
            </td>
            <td>{{ shop.manager }}</td>
            <td>{{ shop.phone }}</td>
            <td>
              <div class="address-info">
                <div class="address-main">{{ shop.address }}</div>
                <div class="address-detail">{{ shop.addressDetail }}</div>
              </div>
            </td>
            <td>{{ shop.servicePhone }}</td>
            <td>
              <span class="status-tag" :class="shop.status">
                {{ getStatusText(shop.status) }}
              </span>
            </td>
            <td>{{ shop.businessHours }}</td>
            <td>{{ shop.createTime }}</td>
            <td>
              <div class="action-buttons">
                <button class="action-btn edit-btn" @click="handleEdit(shop)" title="编辑">
                  编辑
                </button>
                <button class="action-btn delete-btn" @click="handleDelete(shop)" title="删除">
                  删除
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 门店表单对话框 -->
    <ShopFormDialog
      :visible="showDialog"
      :is-edit="isEdit"
      :editing-shop="editingShop"
      @close="closeDialog"
      @save="handleSaveShop"
    />


    <!-- 删除确认对话框 -->
    <div v-if="showDeleteDialog" class="dialog-overlay">
      <div class="delete-dialog" @click.stop>
        <div class="delete-icon">🗑️</div>
        <h3 class="delete-title">确认删除</h3>
        <p class="delete-message">
          您确定要删除门店 <strong>{{ deleteTarget?.name }}</strong> 吗？<br>
          此操作不可恢复，请谨慎操作。
        </p>
        <div class="delete-actions">
          <button class="btn btn-cancel" @click="closeDeleteDialog">取消</button>
          <button class="btn btn-danger" @click="confirmDelete">确认删除</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import ShopFormDialog from '../components/ShopFormDialog.vue'

// 门店数据类型
interface Shop {
  id: number
  name: string
  subName: string
  manager: string
  phone: string
  address: string
  addressDetail: string
  servicePhone: string
  status: 'active' | 'pending'
  businessHours: string
  createTime: string
  password?: string
  longitude?: string
  latitude?: string
  description?: string
}



// 门店列表数据
const shopList = reactive<Shop[]>([
  {
    id: 1,
    name: '旗舰店',
    subName: '旗舰店，0-7CBD核心区',
    manager: '张经理',
    phone: '***********',
    address: '北京市朝阳区建国路88号1号',
    addressDetail: '(39.906908, 116.397128)',
    servicePhone: '010-12345678',
    status: 'active',
    businessHours: '09:00-22:00',
    createTime: '2025-07-31 08:00:00'
  },
  {
    id: 2,
    name: '中关村店',
    subName: '科技园区店，主要服务科技人员',
    manager: '李经理',
    phone: '***********',
    address: '北京市海淀区中关村大街59号',
    addressDetail: '(39.983780, 116.318622)',
    servicePhone: '010-87654321',
    status: 'active',
    businessHours: '08:30-21:30',
    createTime: '2025-07-31 08:00:00'
  },
  {
    id: 3,
    name: '王府井店',
    subName: '商业中心店，客流量大',
    manager: '王经理',
    phone: '***********',
    address: '北京市东城区王府井大街255号',
    addressDetail: '(39.909989, 116.417570)',
    servicePhone: '010-11223344',
    status: 'active',
    businessHours: '10:00-22:00',
    createTime: '2025-07-31 08:00:00'
  },
  {
    id: 4,
    name: '三里屯店',
    subName: '夜经济店，主打夜宵类商品',
    manager: '赵经理',
    phone: '***********',
    address: '北京市朝阳区三里屯路19号',
    addressDetail: '(39.936580, 116.447850)',
    servicePhone: '010-55667788',
    status: 'active',
    businessHours: '11:00-02:00',
    createTime: '2025-07-31 08:00:00'
  },
  {
    id: 5,
    name: '望京店',
    subName: '美食外卖店，数字经济园',
    manager: '黄经理',
    phone: '***********',
    address: '北京市朝阳区望京SOHO T1号',
    addressDetail: '(39.998210, 116.470310)',
    servicePhone: '010-99887766',
    status: 'pending',
    businessHours: '今日休业',
    createTime: '2025-07-31 08:00:00'
  }
])

// 对话框状态
const showDialog = ref(false)
const showDeleteDialog = ref(false)
const isEdit = ref(false)
const editingShop = ref<Shop | null>(null)
const deleteTarget = ref<Shop | null>(null)

// 获取状态文本
const getStatusText = (status: string) => {
  return status === 'active' ? '营业中' : '待开业'
}



// 事件处理函数
const handleAddShop = () => {
  isEdit.value = false
  editingShop.value = null
  showDialog.value = true
}

const handleEdit = (shop: Shop) => {
  isEdit.value = true
  editingShop.value = shop
  showDialog.value = true
}

const handleDelete = (shop: Shop) => {
  deleteTarget.value = shop
  showDeleteDialog.value = true
}

const closeDialog = () => {
  showDialog.value = false
}

const closeDeleteDialog = () => {
  showDeleteDialog.value = false
  deleteTarget.value = null
}

const confirmDelete = () => {
  if (deleteTarget.value) {
    const index = shopList.findIndex(s => s.id === deleteTarget.value!.id)
    if (index > -1) {
      shopList.splice(index, 1)
    }
  }
  closeDeleteDialog()
}

const handleSaveShop = (formData: any, weekDays: any[]) => {
  const businessHours = weekDays
    .filter(day => day.isOpen)
    .map(day => `${day.startTime}-${day.endTime}`)
    .join(', ')

  if (isEdit.value && editingShop.value) {
    // 编辑模式
    const shop = editingShop.value
    Object.assign(shop, {
      name: formData.name,
      subName: formData.description || shop.subName,
      manager: formData.manager,
      phone: formData.phone,
      address: formData.address,
      addressDetail: formData.longitude && formData.latitude
        ? `(${formData.latitude}, ${formData.longitude})`
        : shop.addressDetail,
      servicePhone: formData.servicePhone,
      status: formData.status,
      businessHours: businessHours || '09:00-22:00'
    })
  } else {
    // 新增模式
    const newShop: Shop = {
      id: Date.now(),
      name: formData.name,
      subName: formData.description || '新门店',
      manager: formData.manager,
      phone: formData.phone,
      address: formData.address,
      addressDetail: formData.longitude && formData.latitude
        ? `(${formData.latitude}, ${formData.longitude})`
        : '',
      servicePhone: formData.servicePhone,
      status: formData.status,
      businessHours: businessHours || '09:00-22:00',
      createTime: new Date().toLocaleString('zh-CN')
    }
    shopList.push(newShop)
  }

  closeDialog()
}
</script>

<style scoped>
/* 页面整体样式 */
.page-container {
  padding: 20px 24px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* 页面标题区域 */
.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

/* 操作按钮区域 */
.action-section {
  margin-bottom: 24px;
}

.add-shop-btn {
  background: #415A77;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-shop-btn:hover {
  background: #1B365D;
  transform: translateY(-1px);
}

/* 表格容器 */
.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.shop-table {
  width: 100%;
  border-collapse: collapse;
}

/* 表头样式 */
.shop-table thead th {
  background: #f5f7fa;
  color: #606266;
  font-weight: 600;
  font-size: 14px;
  padding: 16px 12px;
  text-align: left;
  border-bottom: 1px solid #ebeef5;
  white-space: nowrap;
}

/* 表格行样式 */
.shop-table tbody td {
  padding: 16px 12px;
  border-bottom: 1px solid #ebeef5;
  font-size: 14px;
  color: #606266;
  vertical-align: top;
}

.shop-table tbody tr:hover {
  background: #f5f7fa;
}

/* 门店信息样式 */
.shop-info {
  line-height: 1.4;
}

.shop-main-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.shop-sub-name {
  font-size: 12px;
  color: #909399;
}

/* 地址信息样式 */
.address-info {
  line-height: 1.4;
  max-width: 200px;
}

.address-main {
  color: #303133;
  margin-bottom: 4px;
}

.address-detail {
  font-size: 12px;
  color: #909399;
}

/* 状态标签样式 */
.status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.status-tag.active {
  background: #f0f9ff;
  color: #67C23A;
  border: 1px solid #b3e19d;
}

.status-tag.pending {
  background: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.status-tag.closed {
  background: #f5f7fa;
  color: #909399;
  border: 1px solid #dcdfe6;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid;
  white-space: nowrap;
}

.edit-btn {
  background: #1B365D;
  color: white;
  border-color: #1B365D;
}

.edit-btn:hover {
  background: #0D1B2A;
  border-color: #0D1B2A;
}

.delete-btn {
  background: #F56C6C;
  color: white;
  border-color: #F56C6C;
}

.delete-btn:hover {
  background: #f25959;
  border-color: #f25959;
}



/* 删除确认对话框 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.delete-dialog {
  background: white;
  border-radius: 12px;
  padding: 32px;
  text-align: center;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.delete-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.delete-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.delete-message {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 24px;
}

.delete-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .shop-table {
    font-size: 13px;
  }
  
  .shop-table thead th,
  .shop-table tbody td {
    padding: 12px 8px;
  }
  
  .action-btn {
    font-size: 11px;
    padding: 3px 8px;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .table-container {
    overflow-x: auto;
  }
  
  .shop-table {
    min-width: 1000px;
  }
  
  .dialog-container {
    width: 95%;
    max-height: 95vh;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .tab-navigation {
    flex-direction: column;
  }
  
  .hours-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .hours-controls {
    justify-content: space-between;
  }
}
</style>