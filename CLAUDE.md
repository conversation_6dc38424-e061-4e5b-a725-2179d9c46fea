# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a full-stack shop/restaurant management system built with Vue 3 + TypeScript frontend and Webman PHP backend. It's a multi-tenant SaaS solution providing comprehensive business management for hospitality/retail operations.

## Development Commands

### Frontend (Vue 3 + TypeScript)
```bash
cd store-shop
npm install          # Install dependencies
npm run dev          # Start development server (http://localhost:5173)
npm run build        # Build for production
npm run preview      # Preview production build
```

### Backend (Webman PHP)
```bash
cd Webman
composer install     # Install PHP dependencies
php start.php start  # Start development server (http://localhost:8787)
php start.php start -d  # Start as daemon
php start.php stop   # Stop server
php start.php restart   # Restart server
```

## Architecture Overview

### Technology Stack
- **Frontend**: Vue 3, TypeScript, Element Plus, ECharts, Pinia, Vite
- **Backend**: Webman PHP Framework, MySQL, Redis
- **Communication**: RESTful APIs with custom signature authentication

### Project Structure
```
/
├── store-shop/          # Vue 3 Frontend Application
│   ├── src/
│   │   ├── components/  # Reusable UI components organized by domain
│   │   ├── views/       # Page components (route-level)
│   │   ├── hooks/       # Composition API business logic
│   │   ├── services/    # API service layer
│   │   ├── types/       # TypeScript type definitions
│   │   └── utils/       # Utility functions
├── Webman/              # PHP Backend Application
│   ├── app/
│   │   ├── admin/       # Admin module controllers/middleware
│   │   ├── company/     # Company module controllers/middleware
│   │   ├── shop/        # Shop module controllers/middleware
│   │   ├── model/       # Eloquent models (40+ entities)
│   │   ├── service/     # Business logic services
│   │   ├── validate/    # Request validation classes
│   │   └── enums/       # Status and type enumerations
│   └── database/        # Migrations and seeders
└── docs/                # API documentation and guides
```

### Multi-Tenant Architecture
The system operates with three hierarchical levels:
1. **Admin Level**: System-wide administration
2. **Company Level**: Multi-shop company management  
3. **Shop Level**: Individual shop operations

Each level has isolated controllers, middleware, and authentication systems.

## Frontend Architecture Patterns

### Component Organization
Components are organized by domain (e.g., `message/`, `finance/`, `goods/`) with standardized patterns:
- **Form Components**: Dialogs ending with `Form.vue` (e.g., `SmsTemplateForm.vue`)
- **Detail Components**: Read-only dialogs ending with `DetailDialog.vue`
- **Business Components**: Domain-specific reusable components

### Hook-Based State Management
Business logic is extracted into hooks (Composition API) following the pattern:
- `useEntityName.ts` for CRUD operations
- Return reactive data, computed properties, and methods
- Handle API calls, error handling, and state updates

Example hook structure:
```typescript
export function useEntityName() {
  const loading = ref(false)
  const data = ref([])
  
  const fetchData = async () => { /* implementation */ }
  const handleAdd = () => { /* implementation */ }
  
  return {
    loading,
    data,
    fetchData,
    handleAdd
  }
}
```

### Service Layer Pattern
API calls are centralized in service files:
- Each domain has its own service file
- Consistent error handling and response processing
- TypeScript interfaces for request/response types

## Backend Architecture Patterns

### Service-Oriented Design
Controllers are lightweight and delegate business logic to service classes:
- `BaseService`: Common functionality (transactions, validation, logging)
- Domain services: `TableService`, `OrderService`, `ReservationService`, etc.
- Service classes handle complex business rules and data processing

### Request Flow
```
Request → Middleware → Controller → Service → Model → Database
                   ↓
Response ← Controller ← Service ← Model ← Database
```

### Authentication & Security
- Custom signature-based authentication using MD5 hashing
- Multi-level access control (admin/company/shop)
- Rate limiting and CORS middleware
- JWT token management for session handling

## Key Business Domains

### Core Modules
- **Table Management**: Dining table operations, reservations, billing
- **Point of Sale**: Retail operations, checkout, order management
- **Inventory**: Product/goods management, categories, specifications
- **Customer Management**: Member system, points, coupons, recharge
- **Finance**: Sales reports, profit analysis, payment tracking
- **Message System**: SMS templates, system notifications, push settings

### Critical Business Logic
- **Table Status State Machine**: Complex state transitions for dining tables
- **Order Processing**: Multi-step checkout with promotions and payments
- **Inventory Tracking**: Real-time stock management with deduction rules
- **Points & Rewards**: Member loyalty system with acquisition/deduction rules

## Data Consistency Patterns

### Type Definitions
- Centralized TypeScript interfaces in `src/types/`
- Consistent naming: `EntityName` for main types, `EntityNameForm` for forms
- Proper optional fields and union types for status enums

### Status Management
- Numeric status codes (0/1 for inactive/active)
- Enumeration classes in PHP backend for status constants
- Consistent status text mapping in frontend

### API Response Format
```typescript
interface ApiResponse<T> {
  code: number    // 1=success, 2=error, 1003=auth expired
  msg: string     // Human-readable message
  data: T         // Response payload
}
```

## Development Workflow

### Adding New Features
1. Define TypeScript interfaces in `src/types/`
2. Create backend models, services, and controllers
3. Implement frontend services and hooks
4. Create reusable components following domain patterns
5. Build page components using established hooks and components

### Code Standards
- Use Composition API with `<script setup>` syntax
- Extract business logic to hooks
- Create reusable form dialog components
- Follow TypeScript strict mode
- Use Element Plus for UI consistency
- Implement proper error handling in services

### Message Module Standards (Recently Standardized)
- Form dialogs are separate components in `components/message/`
- Business logic in hooks: `useMessageCenter`, `useSmsTemplate`, etc.
- Consistent type definitions with numeric status fields (0=inactive, 1=active)
- Proper API parameter handling for CRUD operations

## Database Considerations

### Message Types Status Fields
- `is_read`: 0 (unread) / 1 (read) - not boolean
- `status`: 0 (inactive) / 1 (active) for most entities
- Always use numeric comparisons: `row.is_read === 0` not `!row.is_read`

### ID Parameter Handling
- For edit operations, include `id` in the data payload, not just URL parameters
- Use proper type checking for optional ID fields in TypeScript

### Migration Strategy
- Database migrations in `Webman/database/migrations/`
- Seeders for default configuration data
- Careful handling of existing data during status field changes

## Development Precautions

### Git Precautions
- **禁止提交git**: Do not commit Git-related files or configuration

## AI Interaction Guidelines

### Response Guidelines
- **总是使用中文回复**: Always respond in Chinese

## Development Safety Guidelines

### Code Editing Precautions
- **没有明确的命令，禁止自己编辑代码**: Do not edit code without explicit instructions

## Logging Guidelines

### Logging Best Practices
- **日志打印不要使用error_log，使用Log::info函数**: Do not use error_log for logging, use Log::info function instead

## UI Design Standards and Best Practices

### Core UI Design Principles
- **严格按照UI设计稿进行100%还原**: Strictly follow UI design specifications for 100% restoration
- **保持视觉风格的完全一致性**: Maintain complete visual style consistency across all pages
- **使用原生HTML/CSS而非组件库**: Use native HTML/CSS instead of component libraries for maximum control
- **深蓝色商务风格设计系统**: Apply deep blue business-style design system consistently

### Established Page Standards

#### 🔐 Login Page (企业登录) - COMPLETED ✅
**Required Elements:**
- Title: 「企业登录」
- Fields: 「企业账号」「登录密码」「图形验证码」
- Features: Password show/hide toggle (👁️/🙈), captcha refresh, remember password checkbox
- Button: 「立即登录」
- Footer: 「新用户注册 | 忘记密码」

#### 📝 Register Page (企业注册) - COMPLETED ✅
**Required Elements:**
- Title: 「企业注册」
- Fields: 企业名称、联系人姓名、手机号码、短信验证码、登录密码、确认密码
- Features: SMS countdown timer, password strength indicator, agreement checkbox
- Button: 「立即注册」
- Footer: 「已有账户？立即登录」

### Unified Design Language

#### Color System
```css
/* Primary Blue Series - Deep Business Blue */
--primary-deep-blue: #0D1B2A;
--primary-business-blue: #1B365D;
--primary-steel-blue: #415A77;

/* Secondary Blue Series - Steel Blue & Elegant Blue Gray */
--secondary-steel-blue: #778DA9;
--secondary-elegant-blue: #E0E1DD;
--secondary-light-blue-gray: #F1F3F4;

/* Accent Colors - Refined Metallic */
--accent-platinum: #C7D2DD;
--accent-warm-silver: #B8C5D1;
--accent-soft-gold: #E8B86D;

/* Text Colors */
--text-primary: #0D1B2A;
--text-secondary: #415A77;
--text-light: #778DA9;
--text-muted: #B8C5D1;

/* Background Effects */
--glass-background: rgba(255, 255, 255, 0.92);
--glass-border: rgba(255, 255, 255, 0.8);
```

#### Background Pattern
- Deep blue gradient: `linear-gradient(135deg, #0D1B2A 0%, #1B365D 100%)`
- Floating radial overlays with subtle animations
- 20-second background float animation

#### Card Design
- Frosted glass effect with backdrop-filter blur(20px)
- Rounded corners (24px border-radius)
- Deep shadow with inset highlight
- Slide-up entrance animation (0.8s ease-out)

#### Logo Style
- Metallic gradient: `linear-gradient(135deg, #E8B86D 0%, #B8C5D1 100%)`
- 80x80px size, 20px border-radius
- Deep blue text color (#0D1B2A)
- Subtle shadow effect

#### Form Components
- Input height: 48px
- Border-radius: 12px
- Border: 2px solid rgba(65, 90, 119, 0.1)
- Focus state: #415A77 border with rgba(65, 90, 119, 0.1) box-shadow
- Label color: #415A77
- Placeholder color: #B8C5D1

#### Button Design
- Primary button: Deep blue gradient background
- Height: 52px, border-radius: 14px
- Hover effect: translateY(-2px) with enhanced shadow
- Loading spinner: White border with animated top-color

#### Interactive Elements
- Password toggle: Right-positioned eye icons (👁️/🙈)
- Captcha/SMS buttons: Metallic gradient background
- Hover effects: Subtle transforms and shadow enhancements
- Disabled states: 0.6 opacity with cursor: not-allowed

### Implementation Requirements

#### Code Standards
- Always use `<script setup lang="ts">` syntax
- Implement proper TypeScript typing
- Use Vue 3 Composition API patterns
- Extract business logic to composables when appropriate
- Implement proper form validation
- Use native CSS animations and transitions

#### Responsive Design
- Mobile-first approach
- Breakpoint: 768px for mobile adaptations
- Flexible layouts with proper spacing adjustments
- Stack captcha containers vertically on mobile

#### Accessibility
- Proper semantic HTML structure
- ARIA labels where necessary
- Keyboard navigation support
- Focus management and visual indicators

### Future Page Development
When developing new pages, ensure they follow the established design system:
1. Use the same color variables and design tokens
2. Implement consistent spacing and typography
3. Apply unified interaction patterns
4. Maintain the same animation and transition styles
5. Follow the established form validation patterns

### Quality Assurance
- Visual comparison with design specifications
- Cross-browser compatibility testing
- Mobile responsiveness verification
- Accessibility compliance check
- Performance optimization validation