import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { useWebSocket } from '@/composables/useWebSocket'
import { BusinessAction, RealtimeMessageType } from '@/types/realtime'
import type {
  RealtimeMessage,
  RealtimeState,
  ResponseMessage,
  WebSocketStatus,
  CallServiceData,
  SubmitOrderData,
  BookTableData,
  CancelBookData
} from '@/types/realtime'

export const useRealtimeStore = defineStore('realtime', () => {
  // WebSocket管理器
  const ws = useWebSocket()
  
  // 状态数据
  const messages = ref<RealtimeMessage[]>([])
  const unreadCount = ref(0)
  const processingMessages = ref(new Set<number>())
  const duplicateCache = ref(new Set<string>())
  const lastOnlineTime = ref(Date.now())
  const pendingMessages = ref<RealtimeMessage[]>([])
  
  // 消息处理设置
  const soundEnabled = ref(true)
  const notificationEnabled = ref(true)
  const autoMarkRead = ref(false)
  
  // 计算属性
  const isConnected = computed(() => ws.state.connected)
  const connectionStatus = computed(() => ws.state.status)
  const unreadMessages = computed(() => 
    messages.value.filter(msg => msg.is_read === 0)
  )
  const latestMessages = computed(() => 
    messages.value.slice(0, 10)
  )
  
  /**
   * 生成消息去重键
   */
  const generateMessageKey = (message: ResponseMessage): string => {
    const data = message.data || {}
    const shopId = data.shop_id || ''
    const action = message.action || ''
    const tableId = data.table_id || ''
    const orderId = data.order_id || ''
    const bookingId = data.booking_id || ''
    const timestamp = Math.floor((data.timestamp ? new Date(data.timestamp).getTime() : Date.now()) / 30000) // 30秒窗口
    
    return `${shopId}_${action}_${tableId}_${orderId}_${bookingId}_${timestamp}`
  }
  
  /**
   * 检查是否为重复消息
   */
  const isDuplicateMessage = (key: string): boolean => {
    return duplicateCache.value.has(key)
  }
  
  /**
   * 添加到去重缓存
   */
  const addToCache = (key: string): void => {
    duplicateCache.value.add(key)
    
    // 限制缓存大小，保留最近1000条
    if (duplicateCache.value.size > 1000) {
      const keys = Array.from(duplicateCache.value)
      const toDelete = keys.slice(0, keys.length - 1000)
      toDelete.forEach(k => duplicateCache.value.delete(k))
    }
  }
  
  /**
   * 格式化消息内容
   */
  const formatMessageContent = (action: BusinessAction, data: any): { title: string; content: string } => {
    // 处理data为空的情况
    if (!data) {
      return {
        title: '系统通知',
        content: '收到新消息'
      }
    }

    switch (action) {
      case BusinessAction.CALL_SERVICE:
        const callData = data as CallServiceData
        return {
          title: `桌台${callData?.table_name || '未知'}号呼叫服务`,
          content: callData?.message || '客户请求服务员协助'
        }
        
      case BusinessAction.SUBMIT_ORDER:
        const orderData = data as SubmitOrderData
        return {
          title: `新订单通知`,
          content: `桌台${orderData?.table_name || '未知'}号提交了点单，订单号：${orderData?.order_sn || '未知'}`
        }
        
      case BusinessAction.BOOK_TABLE:
        const bookData = data as BookTableData
        return {
          title: `桌台预订通知`,
          content: `客户预订了桌台${bookData?.table_name || '未知'}号，预订时间：${bookData?.booking_time || '未知'}`
        }
        
      case BusinessAction.CANCEL_BOOK:
        const cancelData = data as CancelBookData
        return {
          title: `取消预订通知`,
          content: `桌台${cancelData?.table_name || '未知'}号的预订已取消${cancelData?.reason ? `，原因：${cancelData.reason}` : ''}`
        }
        
      case BusinessAction.SERVICE_RESPONSE:
        return {
          title: `服务响应通知`,
          content: data?.message || `服务请求已处理`
        }
        
      case BusinessAction.ORDER_STATUS_UPDATE:
        return {
          title: `订单状态更新`,
          content: data?.message || `订单${data?.order_id || ''}状态已更新`
        }
        
      case BusinessAction.BOOKING_STATUS_UPDATE:
        return {
          title: `预订状态更新`,
          content: data?.message || `预订${data?.booking_id || ''}状态已更新`
        }
        
      default:
        return {
          title: '系统通知',
          content: data?.message || '收到新消息'
        }
    }
  }
  
  /**
   * 获取消息类型
   */
  const getMessageType = (action: BusinessAction): RealtimeMessageType => {
    switch (action) {
      case BusinessAction.CALL_SERVICE:
        return RealtimeMessageType.CALL_SERVICE
      case BusinessAction.SUBMIT_ORDER:
        return RealtimeMessageType.SUBMIT_ORDER
      case BusinessAction.BOOK_TABLE:
        return RealtimeMessageType.BOOK_TABLE
      case BusinessAction.CANCEL_BOOK:
        return RealtimeMessageType.CANCEL_BOOK
      case BusinessAction.SERVICE_RESPONSE:
        return RealtimeMessageType.SERVICE_RESPONSE
      case BusinessAction.ORDER_STATUS_UPDATE:
        return RealtimeMessageType.ORDER_STATUS_UPDATE
      case BusinessAction.BOOKING_STATUS_UPDATE:
        return RealtimeMessageType.BOOKING_STATUS_UPDATE
      default:
        return RealtimeMessageType.CALL_SERVICE
    }
  }
  
  /**
   * 创建实时消息对象
   */
  const createRealtimeMessage = (responseMessage: ResponseMessage): RealtimeMessage => {
    const { title, content } = formatMessageContent(responseMessage.action!, responseMessage.data)
    const shopId = responseMessage.data?.shop_id || 0
    
    return {
      shop_id: Number(shopId),
      message_type: getMessageType(responseMessage.action!),
      title,
      content,
      is_read: 0,
      extra_data: responseMessage.data || {},
      created_at: new Date().toISOString()
    }
  }
  
  /**
   * 显示消息通知
   */
  const showNotification = (message: RealtimeMessage) => {
    if (!notificationEnabled.value) return
    
    // 浏览器通知
    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(message.title, {
        body: message.content,
        icon: '/favicon.ico',
        silent: !soundEnabled.value
      })
      
      notification.onclick = () => {
        window.focus()
        notification.close()
        // 可以在这里添加跳转到消息详情的逻辑
      }
      
      setTimeout(() => notification.close(), 5000)
    }
    
    // Element Plus 通知
    ElNotification({
      title: message.title,
      message: message.content,
      type: 'info',
      duration: 5000,
      position: 'top-right'
    })
    
    // 播放提示音
    if (soundEnabled.value) {
      playNotificationSound()
    }
  }
  
  /**
   * 播放提示音
   */
  const playNotificationSound = () => {
    try {
      const audio = new Audio('/notification.mp3') // 需要添加音频文件
      audio.volume = 0.5
      audio.play().catch(() => {
        // 忽略播放失败
      })
    } catch (error) {
      // 忽略音频播放错误
    }
  }
  
  /**
   * 判断是否为需要显示和计数的业务消息
   */
  const isNotificationMessage = (action: BusinessAction): boolean => {
    // JOIN_GROUP 消息不需要显示和计数，只是连接确认消息
    return action !== BusinessAction.JOIN_GROUP
  }

  /**
   * 处理接收到的WebSocket消息
   */
  const handleWebSocketMessage = (message: ResponseMessage) => {
    // 只处理业务消息
    if (!message.action || !Object.values(BusinessAction).includes(message.action)) {
      return
    }

    // JOIN_GROUP 消息不需要显示和计数
    if (!isNotificationMessage(message.action)) {
      console.log('忽略非通知类型消息:', message.action)
      return
    }
    
    // 消息去重
    const messageKey = generateMessageKey(message)
    if (isDuplicateMessage(messageKey)) {
      console.log('忽略重复消息:', messageKey)
      return
    }
    
    addToCache(messageKey)
    
    // 创建实时消息
    const realtimeMessage = createRealtimeMessage(message)
    
    // 添加到消息列表
    messages.value.unshift(realtimeMessage)
    
    // 更新未读计数
    if (realtimeMessage.is_read === 0) {
      unreadCount.value++
    }
    
    // 显示通知
    showNotification(realtimeMessage)
    
    // 保存到localStorage作为备份
    saveMessagesToStorage()
    
    // 如果配置了自动调用后端API保存消息，可以在这里实现
    // await saveMessageToServer(realtimeMessage)
  }
  
  /**
   * 保存消息到本地存储
   */
  const saveMessagesToStorage = () => {
    try {
      const dataToSave = {
        messages: messages.value.slice(0, 100), // 只保存最近100条
        unreadCount: unreadCount.value,
        lastOnlineTime: lastOnlineTime.value
      }
      localStorage.setItem('realtime_messages', JSON.stringify(dataToSave))
    } catch (error) {
      console.error('保存消息到本地存储失败:', error)
    }
  }
  
  /**
   * 从本地存储加载消息
   */
  const loadMessagesFromStorage = () => {
    try {
      const data = localStorage.getItem('realtime_messages')
      if (data) {
        const parsed = JSON.parse(data)
        messages.value = parsed.messages || []
        unreadCount.value = parsed.unreadCount || 0
        lastOnlineTime.value = parsed.lastOnlineTime || Date.now()
      }
    } catch (error) {
      console.error('从本地存储加载消息失败:', error)
    }
  }
  
  /**
   * 标记消息为已读
   */
  const markMessageRead = (messageId: number) => {
    const message = messages.value.find(msg => msg.id === messageId)
    if (message && message.is_read === 0) {
      message.is_read = 1
      unreadCount.value = Math.max(0, unreadCount.value - 1)
      saveMessagesToStorage()
    }
  }
  
  /**
   * 批量标记消息为已读
   */
  const markMessagesRead = (messageIds: number[]) => {
    let changedCount = 0
    messageIds.forEach(id => {
      const message = messages.value.find(msg => msg.id === id)
      if (message && message.is_read === 0) {
        message.is_read = 1
        changedCount++
      }
    })
    
    unreadCount.value = Math.max(0, unreadCount.value - changedCount)
    saveMessagesToStorage()
  }
  
  /**
   * 全部标记为已读
   */
  const markAllRead = () => {
    messages.value.forEach(message => {
      if (message.is_read === 0) {
        message.is_read = 1
      }
    })
    unreadCount.value = 0
    saveMessagesToStorage()
  }
  
  /**
   * 清空所有消息
   */
  const clearAllMessages = () => {
    messages.value = []
    unreadCount.value = 0
    duplicateCache.value.clear()
    saveMessagesToStorage()
  }
  
  /**
   * 移除指定消息
   */
  const removeMessage = (messageId: number) => {
    const index = messages.value.findIndex(msg => msg.id === messageId)
    if (index > -1) {
      const message = messages.value[index]
      if (message.is_read === 0) {
        unreadCount.value = Math.max(0, unreadCount.value - 1)
      }
      messages.value.splice(index, 1)
      saveMessagesToStorage()
    }
  }
  
  /**
   * 批量移除消息
   */
  const removeMessages = (messageIds: number[]) => {
    const messageIdSet = new Set(messageIds)
    let removedUnreadCount = 0
    
    // 计算被删除的未读消息数量
    messages.value.forEach(msg => {
      if (messageIdSet.has(msg.id!) && msg.is_read === 0) {
        removedUnreadCount++
      }
    })
    
    // 过滤掉要删除的消息
    messages.value = messages.value.filter(msg => !messageIdSet.has(msg.id!))
    
    unreadCount.value = Math.max(0, unreadCount.value - removedUnreadCount)
    saveMessagesToStorage()
  }
  
  /**
   * 请求浏览器通知权限
   */
  const requestNotificationPermission = async (): Promise<NotificationPermission> => {
    if ('Notification' in window) {
      return await Notification.requestPermission()
    }
    return 'denied'
  }
  
  /**
   * 连接WebSocket
   */
  const connect = async () => {
    try {
      await ws.connect()
      lastOnlineTime.value = Date.now()
      
      // 连接成功后，可以同步离线期间的消息
      // await syncOfflineMessages()
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      ElMessage.error('连接服务器失败，请检查网络连接')
    }
  }
  
  /**
   * 断开WebSocket连接
   */
  const disconnect = () => {
    ws.disconnect()
  }
  
  /**
   * 初始化实时消息系统
   */
  const initialize = async () => {
    // 如果已经连接，跳过初始化
    if (isConnected.value) {
      console.log('WebSocket已连接，跳过重复初始化')
      return
    }

    // 加载本地存储的消息
    loadMessagesFromStorage()
    
    // 设置WebSocket消息处理器
    ws.onMessage(handleWebSocketMessage)
    
    // 设置连接状态处理器
    ws.onConnect(() => {
      console.log('实时消息系统已连接')
      lastOnlineTime.value = Date.now()
    })
    
    ws.onDisconnect(() => {
      console.log('实时消息系统已断开')
    })
    
    ws.onError((error) => {
      console.error('实时消息系统错误:', error)
    })
    
    // 请求通知权限
    if (notificationEnabled.value) {
      await requestNotificationPermission()
    }
    
    // 自动连接
    await connect()
  }
  
  /**
   * 设置未读消息数量（用于登录后初始化）
   */
  const setUnreadCount = (count: number) => {
    unreadCount.value = Math.max(0, count)
    saveMessagesToStorage()
  }

  /**
   * 销毁实时消息系统
   */
  const destroy = () => {
    disconnect()
    clearAllMessages()
    localStorage.removeItem('realtime_messages')
  }
  
  return {
    // 状态
    messages,
    unreadCount,
    processingMessages,
    lastOnlineTime,
    pendingMessages,
    
    // 设置
    soundEnabled,
    notificationEnabled,
    autoMarkRead,
    
    // 计算属性
    isConnected,
    connectionStatus,
    unreadMessages,
    latestMessages,
    
    // 方法
    markMessageRead,
    markMessagesRead,
    markAllRead,
    clearAllMessages,
    removeMessage,
    removeMessages,
    requestNotificationPermission,
    connect,
    disconnect,
    initialize,
    destroy,
    setUnreadCount,
    
    // 工具方法
    generateMessageKey,
    isDuplicateMessage,
    addToCache,
    saveMessagesToStorage,
    loadMessagesFromStorage
  }
})