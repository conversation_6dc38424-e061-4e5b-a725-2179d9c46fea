{"root": ["./src/app.vue", "./src/main.ts", "./src/vite-env.d.ts", "./src/components/loadinganimation.vue", "./src/components/slidercaptcha.vue", "./src/components/index.ts", "./src/components/layout/appheader.vue", "./src/components/layout/appmain.vue", "./src/components/layout/appsidebar.vue", "./src/components/layout/dynamicsidebar.vue", "./src/components/layout/menuicon.vue", "./src/components/layout/sidebaritem.vue", "./src/components/layout/tabnav.vue", "./src/components/layout/index.vue", "./src/components/admin/adminform.vue", "./src/components/common/accessoriesselectordialog.vue", "./src/components/common/autoprinttest.vue", "./src/components/common/devtoolbar.vue", "./src/components/common/goodsselector.vue", "./src/components/common/pagination.vue", "./src/components/common/printpreviewdialog.vue", "./src/components/common/richtexteditor.vue", "./src/components/common/setmealselectordialog.vue", "./src/components/finance/reportpagelayout.vue", "./src/components/finance/charts/goodsrankingchart.vue", "./src/components/finance/charts/monthlytrendchart.vue", "./src/components/finance/charts/paymentanalysischart.vue", "./src/components/finance/charts/profitanalysischart.vue", "./src/components/finance/charts/saleschart.vue", "./src/components/finance/charts/staffperformancechart.vue", "./src/components/finance/charts/universalchart.vue", "./src/components/goods/accessoriesselector.vue", "./src/components/goods/goodsform.vue", "./src/components/goods/remarklabelselector.vue", "./src/components/goods/setmealgoodsform.vue", "./src/components/goods/setmealgoodsselector.vue", "./src/components/goods_category/accessoriescategoryform.vue", "./src/components/goods_category/accessoriesgoodsform.vue", "./src/components/goods_category/goodscategoryform.vue", "./src/components/goods_remark_label/goodsremarklabelform.vue", "./src/components/inventory/checkdetaildialog.vue", "./src/components/inventory/createcheckform.vue", "./src/components/inventory/executecheckdialog.vue", "./src/components/inventory/inventoryadjustform.vue", "./src/components/inventory/inventoryinitform.vue", "./src/components/inventory/inventorylogdialog.vue", "./src/components/inventory/inventorywarningform.vue", "./src/components/marketing/activityform.vue", "./src/components/marketing/activitystatisticsdialog.vue", "./src/components/marketing/batchdistributedialog.vue", "./src/components/marketing/couponform.vue", "./src/components/marketing/statisticsdialog.vue", "./src/components/member/rechargedialog.vue", "./src/components/member/userdialog copy.vue", "./src/components/member/userdialog.vue", "./src/components/member/recharge_package/rechargepackageform.vue", "./src/components/member/user_level/userlevelform.vue", "./src/components/message/messagecenterdialog.vue", "./src/components/message/messagedetaildialog.vue", "./src/components/message/messagesettingform.vue", "./src/components/message/realtimemessagedetail.vue", "./src/components/message/smslogdetaildialog.vue", "./src/components/message/smstemplateform.vue", "./src/components/message/smstemplatetestdialog.vue", "./src/components/message/systemmessageform.vue", "./src/components/order/cancelorderdialog.vue", "./src/components/order/orderdetaildialog.vue", "./src/components/profile/profiledialog.vue", "./src/components/realtime/messagebadge.vue", "./src/components/realtime/messagetoast.vue", "./src/components/realtime/notificationcenter.vue", "./src/components/reservation/reservationcreateorderdialog.vue", "./src/components/reservation/reservationdetaildialog.vue", "./src/components/reservation/reservationeditdialog.vue", "./src/components/reservation/reservationtransfertabledialog.vue", "./src/components/retail/accessorydialog.vue", "./src/components/retail/checkoutconfirmdialog.vue", "./src/components/retail/checkoutdialog.vue", "./src/components/retail/marketingactivityselector.vue", "./src/components/retail/productsdisplay.vue", "./src/components/retail/remarkdialog.vue", "./src/components/retail/retailcore.vue", "./src/components/retail/saveorderdialog.vue", "./src/components/retail/savedorderdetaildialog.vue", "./src/components/retail/savedordersdialog.vue", "./src/components/retail/specificationdialog.vue", "./src/components/settings/payment_method/paymentmethodform.vue", "./src/components/settings/print/discoverprinterdialog.vue", "./src/components/settings/print/printconfigform.vue", "./src/components/settings/print/printtemplateform.vue", "./src/components/settings/print/templatepreview copy.vue", "./src/components/settings/print/templatepreview.vue", "./src/components/settings/print/testresultdialog.vue", "./src/components/settings/service_rules/serviceruledialog.vue", "./src/components/settings/table_rules/billingruledialog.vue", "./src/components/specification/specificationcombiner.vue", "./src/components/specification/specificationform.vue", "./src/components/specification_classification/specificationclassificationform.vue", "./src/components/staff/rolepermissiondialog.vue", "./src/components/table/endmaintenancedialog.vue", "./src/components/table/maintenancedialog.vue", "./src/components/table/openorderdialog.vue", "./src/components/table/orderdetaildialog.vue", "./src/components/table/orderdialog.vue", "./src/components/table/qrcodedialog.vue", "./src/components/table/reservationdialog.vue", "./src/components/table/reservationorderdialog.vue", "./src/components/table/tableform.vue", "./src/components/table/tableoperationdialog.vue", "./src/components/table/voucherorderdialog.vue", "./src/components/table_classification/tableclassificationform.vue", "./src/composables/usepermission.ts", "./src/composables/usewebsocket.ts", "./src/hooks/useaccessoriescategory.ts", "./src/hooks/useaccessoriesgoods.ts", "./src/hooks/useadmin.ts", "./src/hooks/usebalancelog.ts", "./src/hooks/usecart.ts", "./src/hooks/usecheckout.ts", "./src/hooks/usecoupon.ts", "./src/hooks/usegoods.ts", "./src/hooks/usegoodscategory.ts", "./src/hooks/usegoodsremarklabel.ts", "./src/hooks/useinventory.ts", "./src/hooks/useinventorycheck.ts", "./src/hooks/useinventorylog.ts", "./src/hooks/useinventorywarning.ts", "./src/hooks/usemarketingactivity.ts", "./src/hooks/usemessagecenter.ts", "./src/hooks/usemessagesetting.ts", "./src/hooks/useorder.ts", "./src/hooks/useorderbase.ts", "./src/hooks/usepaymentmethod.ts", "./src/hooks/useprintconfig.ts", "./src/hooks/useprinttemplate.ts", "./src/hooks/useprofile.ts", "./src/hooks/userechargepackage.ts", "./src/hooks/usereservation.ts", "./src/hooks/useretail.ts", "./src/hooks/useretailorder.ts", "./src/hooks/userole.ts", "./src/hooks/usesavedorders.ts", "./src/hooks/useselectoptions.ts", "./src/hooks/useservicerulehook.ts", "./src/hooks/usesetmealgoods.ts", "./src/hooks/usesmslog.ts", "./src/hooks/usesmstemplate.ts", "./src/hooks/usespecification.ts", "./src/hooks/usespecificationclassification.ts", "./src/hooks/usesystemmessage.ts", "./src/hooks/usetable.ts", "./src/hooks/usetablebillingrulehook.ts", "./src/hooks/usetablebusiness.ts", "./src/hooks/usetableclassification.ts", "./src/hooks/usetablemanagement.ts", "./src/hooks/usetableorder.ts", "./src/hooks/useuser.ts", "./src/hooks/useuserlevel.ts", "./src/hooks/useuserpointslog.ts", "./src/router/dynamic.ts", "./src/router/index.ts", "./src/services/accessoriescategoryservice.ts", "./src/services/accessoriesgoodsservice.ts", "./src/services/adminapi.ts", "./src/services/adminservice.ts", "./src/services/api.ts", "./src/services/autoprintservice.ts", "./src/services/balancelogservice.ts", "./src/services/billingservice.ts", "./src/services/checkoutapi.ts", "./src/services/couponservice.ts", "./src/services/financeservice.ts", "./src/services/goodscategoryservice.ts", "./src/services/goodsremarklabelservice.ts", "./src/services/goodsservice.ts", "./src/services/inventorycheckservice.ts", "./src/services/inventoryservice.ts", "./src/services/inventorywarningservice.ts", "./src/services/marketingactivityservice.ts", "./src/services/memberapi.ts", "./src/services/memberservice.ts", "./src/services/menuservice.ts", "./src/services/messageapi.ts", "./src/services/messageservice.ts", "./src/services/messagesettingservice.ts", "./src/services/orderservice.ts", "./src/services/paymentmethodservice.ts", "./src/services/pointsacquisitionservice.ts", "./src/services/pointsdeductionservice.ts", "./src/services/printconfigservice.ts", "./src/services/printtemplateservice.ts", "./src/services/printerconnectionservice.ts", "./src/services/printerdiscoveryservice.ts", "./src/services/profileservice.ts", "./src/services/rechargepackageservice.ts", "./src/services/reservationapi.ts", "./src/services/retailapi.ts", "./src/services/roleservice.ts", "./src/services/serviceruleservice.ts", "./src/services/setmealgoodsservice.ts", "./src/services/smslogservice.ts", "./src/services/smstemplateservice.ts", "./src/services/specificationclassificationservice.ts", "./src/services/specificationservice.ts", "./src/services/staffservice.ts", "./src/services/systemconfigservice.ts", "./src/services/systemmessageservice.ts", "./src/services/tablebusinessservice.ts", "./src/services/tableclassificationservice.ts", "./src/services/tableservice.ts", "./src/services/templaterenderservice.ts", "./src/services/userlevelservice.ts", "./src/services/userpointslogservice.ts", "./src/stores/index.ts", "./src/stores/menu.ts", "./src/stores/realtime.ts", "./src/types/checkout.ts", "./src/types/element-plus.d.ts", "./src/types/finance.d.ts", "./src/types/goods.d.ts", "./src/types/index.ts", "./src/types/inventory.ts", "./src/types/message.d.ts", "./src/types/order.d.ts", "./src/types/print.d.ts", "./src/types/realtime.ts", "./src/types/reservation.ts", "./src/types/retail.ts", "./src/types/shims-wangeditor.d.ts", "./src/types/stagewise.d.ts", "./src/types/systemconfig.ts", "./src/types/vue-slider-captcha.d.ts", "./src/utils/auth.ts", "./src/utils/authutils.ts", "./src/utils/chartutils.ts", "./src/utils/common.ts", "./src/utils/constants.ts", "./src/utils/dayjs.ts", "./src/utils/excelutils.ts", "./src/utils/formatbalance.ts", "./src/utils/index.ts", "./src/utils/throttle.ts", "./src/utils/websocket.ts", "./src/views/403.vue", "./src/views/404.vue", "./src/views/dashboard.vue", "./src/views/home.vue", "./src/views/login copy.vue", "./src/views/login.vue", "./src/views/customer-service/feedback/index.vue", "./src/views/finance/chartdemo.vue", "./src/views/finance/financechartdemo.vue", "./src/views/finance/financereport.vue", "./src/views/finance/reports/businessoverviewdetail.vue", "./src/views/finance/reports/businessoverviewsimple.vue", "./src/views/finance/reports/costanalysisdetail.vue", "./src/views/finance/reports/goodsrankingdetail.vue", "./src/views/finance/reports/monthlyreportdetail.vue", "./src/views/finance/reports/paymentanalysisdetail.vue", "./src/views/finance/reports/profitanalysisdetail.vue", "./src/views/finance/reports/staffperformancedetail.vue", "./src/views/goods/index.vue", "./src/views/goods/category/index.vue", "./src/views/goods/category/accessories/index.vue", "./src/views/goods/category/accessories/goods/index.vue", "./src/views/goods/remark_label/index.vue", "./src/views/goods/set_meal/index.vue", "./src/views/goods/specification/index.vue", "./src/views/goods/specification_classification/index.vue", "./src/views/inventory/index.vue", "./src/views/inventory/check/index.vue", "./src/views/inventory/log/index.vue", "./src/views/inventory/warning/index.vue", "./src/views/marketing/activity/index.vue", "./src/views/marketing/coupon/index.vue", "./src/views/member/balance_log/index.vue", "./src/views/member/points/index.vue", "./src/views/member/points_deduction/index.vue", "./src/views/member/points_log/index.vue", "./src/views/member/recharge_package/index.vue", "./src/views/member/user_level/index.vue", "./src/views/member/users/index.vue", "./src/views/message/center/index.vue", "./src/views/message/setting/index.vue", "./src/views/message/sms/log/index.vue", "./src/views/message/sms/template/index.vue", "./src/views/message/system/index.vue", "./src/views/order/retail/index.vue", "./src/views/order/table/index.vue", "./src/views/reservation/index.vue", "./src/views/retail/index.vue", "./src/views/settings/billing/index.vue", "./src/views/settings/billing/service/index.vue", "./src/views/settings/billing/table/index.vue", "./src/views/settings/payment/method/index.vue", "./src/views/settings/print/autoprintconfig.vue", "./src/views/settings/print/config/index.vue", "./src/views/settings/print/template/index.vue", "./src/views/settings/table/index.vue", "./src/views/staff/list/index.vue", "./src/views/staff/menu-config/index.vue", "./src/views/staff/role/index.vue", "./src/views/table/index.vue", "./src/views/table/business/index.vue", "./src/views/table/classification/index.vue", "./src/views/test/autoprinttestpage.vue", "./src/views/test/checkoutprintdemo.vue", "./src/views/test/permissiontest.vue"], "errors": true, "version": "5.8.3"}