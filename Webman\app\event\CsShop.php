<?php

namespace app\event;

use app\model\CsShopAdmin;
use app\model\CsShopAdminRole;
use app\model\CsUserLevel;
use app\model\CsPaymentMethod;
use app\model\CsBillingRule;
use app\model\CsServiceRule;
use app\model\CsTableClassification;
use app\model\CsGoodsCategory;
use app\model\CsAccessoriesGoodsCategory;
use app\model\CsSpecificationClassification;
use app\model\CsGoodsRemarkLabel;
use app\model\CsPointsAcquisition;
use app\model\CsPointsDeduction;
use app\model\CsUserRechargePackage;
use app\model\CsSpecification;
use app\model\CsTable;
use app\model\CsGoods;
use app\model\CsAccessoriesGoods;
use app\model\CsSetMealGoods;
use app\model\CsPrintConfig;
use app\model\CsPrintTemplate;
use app\validate\CsShopAdminValidate;
use app\service\InventoryService;
use support\Log;
use think\exception\ValidateException;

class CsShop
{
    public function add(array $data = [])
    {
        // 添加店铺超级管理员
        try {
            $adminData = [
                'nickname' => $data['mobile'],
                'username' => $data['mobile'],
                'password' => $data['password'],
                'status' => $data['status'],
                'shop_id' => $data['shop_id'],
                'role_id' => 1,
                'is_origin' => 1,
            ];
            $validate = new CsShopAdminValidate();
            if (!$validate->check($adminData)) {
                throw new ValidateException($validate->getError());
            }
            $result = CsShopAdmin::addPost($adminData);
            if (!empty($result['error'])) {
                throw new \Exception($result['msg']);
            }
            // 添加普通会员等级
            $userLevelModel = new CsUserLevel();
            $userLevelModel->user_level_name = '普通会员';
            $userLevelModel->goods_discount = 10;
            $userLevelModel->room_discount = 10;
            $userLevelModel->service_discount = 10;
            $userLevelModel->shop_id = $data['shop_id'];
            $userLevelModel->can_not_delete = 1;
            $userLevelModel->save();
            
            // 初始化基础数据
            $this->initPaymentMethods($data['shop_id']);
            $this->initBillingRules($data['shop_id']);
            $this->initServiceRules($data['shop_id']);
            $this->initTableClassifications($data['shop_id']);
            $this->initGoodsCategories($data['shop_id']);
            $this->initAccessoriesGoodsCategories($data['shop_id']);
            $this->initSpecificationClassifications($data['shop_id']);
            $this->initGoodsRemarkLabels($data['shop_id']);
            $this->initPointsRules($data['shop_id']);
            $this->initRechargePackages($data['shop_id']);
            
            // 初始化规格值（必须在规格分类初始化之后）
            $this->initSpecifications($data['shop_id']);
            
            // 初始化桌台（必须在桌台分类初始化之后）
            $this->initTables($data['shop_id']);
            
            // 初始化商品（必须在商品分类初始化之后）
            $this->initGoods($data['shop_id']);
            
            // 初始化配品（必须在配品分类初始化之后）
            $this->initAccessoriesGoods($data['shop_id']);
            
            // 初始化套餐商品（必须在普通商品初始化之后）
            $this->initSetMealGoods($data['shop_id']);
            
            // 批量初始化所有商品的库存记录（必须在所有商品初始化之后）
            $this->initInventoryRecords($data['shop_id']);
            
            // 初始化打印机配置
            $this->initPrintConfigs($data['shop_id']);
            
            // 初始化打印模板
            $this->initPrintTemplates($data['shop_id']);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function edit(array $data = [])
    {
        // 修改店铺超级管理员
        try {
            $admin = CsShopAdmin::where(['shop_id' => $data['shop_id'], 'is_origin' => 1])->first();
            $admin->password = $data['password'];
            $admin->status = $data['status'];
            $admin->save();
            if (empty($data['status'])) {
                // 将该店铺所有的管理员账号都设置成禁用状态，并将is_origin设置为2用来说明是店铺授权到期禁用的账号，方便后续恢复
                CsShopAdmin::where(['shop_id' => $data['shop_id'], 'status' => 1, 'is_origin' => 0])
                    ->update(['status' => 0, 'is_origin' => 2]);
            } else {
                // 恢复被禁用的账号
                CsShopAdmin::where(['shop_id' => $data['shop_id'], 'status' => 0, 'is_origin' => 2])
                    ->update(['status' => 1, 'is_origin' => 0]);
            }
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function del(array $data = [])
    {
        // 删除店铺超级管理员及角色
        try {
            CsShopAdmin::where(['shop_id' => $data['shop_id']])->delete();
            CsShopAdminRole::where(['shop_id' => $data['shop_id']])->delete();
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function selectDel(array $data = [])
    {
        // 删除店铺超级管理员及角色
        try {
            CsShopAdmin::whereIn('shop_id',$data['shop_id'])->delete();
            CsShopAdminRole::whereIn('shop_id',$data['shop_id'])->delete();
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 初始化支付方式
     * @param int $shopId
     * @throws \Exception
     */
    private function initPaymentMethods(int $shopId)
    {
        // 确保系统级余额支付方式存在
        $balancePayment = CsPaymentMethod::find(\app\enums\PaymentMethodEnum::BALANCE_PAYMENT);
        if (!$balancePayment) {
            // 创建系统级余额支付方式
            $balancePayment = new CsPaymentMethod();
            $balancePayment->id = \app\enums\PaymentMethodEnum::BALANCE_PAYMENT;
            $balancePayment->shop_id = 0; // 0表示系统级别
            $balancePayment->payment_method_name = '会员余额';
            $balancePayment->sort = 999; // 排在最后
            $balancePayment->status = 1;
            $balancePayment->is_default = 0;
            $balancePayment->remark = '系统内置余额支付方式';
            $balancePayment->save();
        }
        
        // 门店特有的支付方式（不包含会员余额）
        $paymentMethods = [
            [
                'shop_id' => $shopId,
                'payment_method_name' => '现金支付',
                'sort' => 1,
                'status' => 1,
                'is_default' => 1,
                'remark' => '现金收款'
            ],
            [
                'shop_id' => $shopId,
                'payment_method_name' => '微信支付',
                'sort' => 2,
                'status' => 1,
                'is_default' => 0,
                'remark' => '微信扫码支付'
            ],
            [
                'shop_id' => $shopId,
                'payment_method_name' => '支付宝支付',
                'sort' => 3,
                'status' => 1,
                'is_default' => 0,
                'remark' => '支付宝扫码支付'
            ],
            [
                'shop_id' => $shopId,
                'payment_method_name' => '银行卡支付',
                'sort' => 4,
                'status' => 1,
                'is_default' => 0,
                'remark' => '刷卡支付'
            ]
        ];

        foreach ($paymentMethods as $method) {
            $model = new CsPaymentMethod();
            foreach ($method as $key => $value) {
                $model->$key = $value;
            }
            $model->save();
        }
    }

    /**
     * 初始化计费规则
     * @param int $shopId
     * @throws \Exception
     */
    private function initBillingRules(int $shopId)
    {
        $billingRules = [
            [
                'shop_id' => $shopId,
                'rule_name' => '无桌台费',
                'rule_type' => 0, // 固定金额
                'hour_nums' => 0,
                'price' => '0.00',
                'free_minutes' => 0,
                'sort' => 1,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'rule_name' => '茶位费',
                'rule_type' => 0, // 固定金额
                'hour_nums' => 0,
                'price' => '15.00',
                'free_minutes' => 0,
                'sort' => 2,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'rule_name' => '按小时计费',
                'rule_type' => 1, // 按小时计费
                'hour_nums' => 0,
                'price' => '25.00',
                'free_minutes' => 30,
                'sort' => 3,
                'status' => 1
            ],
            /* [
                'shop_id' => $shopId,
                'rule_name' => '半日茶',
                'rule_type' => 2, // 按包段计费
                'hour_nums' => 4,
                'price' => '88.00',
                'free_minutes' => 0,
                'sort' => 3,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'rule_name' => '全日茶',
                'rule_type' => 2, // 按包段计费
                'hour_nums' => 8,
                'price' => '158.00',
                'free_minutes' => 0,
                'sort' => 4,
                'status' => 1
            ] */
        ];

        foreach ($billingRules as $rule) {
            $model = new CsBillingRule();
            foreach ($rule as $key => $value) {
                $model->$key = $value;
            }
            $model->save();
        }
    }

    /**
     * 初始化服务费规则
     * @param int $shopId
     * @throws \Exception
     */
    private function initServiceRules(int $shopId)
    {
        $serviceRules = [
            [
                'shop_id' => $shopId,
                'rule_name' => '无服务费',
                'rule_type' => 0, // 固定金额
                'hour_nums' => 0,
                'price' => '0.00',
                'sort' => 1,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'rule_name' => '开水茶具费',
                'rule_type' => 0, // 固定金额
                'hour_nums' => 0,
                'price' => '3.00',
                'sort' => 2,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'rule_name' => '包厢服务费',
                'rule_type' => 0, // 固定金额
                'hour_nums' => 0,
                'price' => '20.00',
                'sort' => 3,
                'status' => 1
            ]
        ];

        foreach ($serviceRules as $rule) {
            $model = new CsServiceRule();
            foreach ($rule as $key => $value) {
                $model->$key = $value;
            }
            $model->save();
        }
    }

    /**
     * 初始化桌台分类
     * @param int $shopId
     * @throws \Exception
     */
    private function initTableClassifications(int $shopId)
    {
        // 先获取默认的计费规则和服务费规则ID
        $billingRuleId = CsBillingRule::where('shop_id', $shopId)->orderBy('id')->value('id');
        $serviceRuleId = CsServiceRule::where('shop_id', $shopId)->orderBy('id')->value('id');

        $tableClassifications = [
            [
                'shop_id' => $shopId,
                'classification_name' => '散座',
                'billing_rule_id' => $billingRuleId,
                'service_rule_id' => $serviceRuleId,
                'default_custom_nums' => 2,
                'minimum_type' => 0,
                'minimum_consumption' => 0,
                'sort' => 1,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'classification_name' => '卡座',
                'billing_rule_id' => $billingRuleId,
                'service_rule_id' => $serviceRuleId,
                'default_custom_nums' => 4,
                'minimum_type' => 0,
                'minimum_consumption' => 80,
                'sort' => 2,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'classification_name' => '雅间',
                'billing_rule_id' => $billingRuleId,
                'service_rule_id' => $serviceRuleId,
                'default_custom_nums' => 6,
                'minimum_type' => 1,
                'minimum_consumption' => 120,
                'sort' => 3,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'classification_name' => '贵宾包厢',
                'billing_rule_id' => $billingRuleId,
                'service_rule_id' => $serviceRuleId,
                'default_custom_nums' => 10,
                'minimum_type' => 1,
                'minimum_consumption' => 300,
                'sort' => 4,
                'status' => 1
            ]
        ];

        foreach ($tableClassifications as $classification) {
            $model = new CsTableClassification();
            foreach ($classification as $key => $value) {
                $model->$key = $value;
            }
            $model->save();
        }
    }

    /**
     * 初始化商品分类
     * @param int $shopId
     * @throws \Exception
     */
    private function initGoodsCategories(int $shopId)
    {
        $goodsCategories = [
            [
                'shop_id' => $shopId,
                'category_name' => '名茶系列',
                'parent_id' => 0,
                'specification_classification_id' => '',
                'sort' => 1,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'category_name' => '花草茶',
                'parent_id' => 0,
                'specification_classification_id' => '',
                'sort' => 2,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'category_name' => '功夫茶',
                'parent_id' => 0,
                'specification_classification_id' => '',
                'sort' => 3,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'category_name' => '茶点小食',
                'parent_id' => 0,
                'specification_classification_id' => '',
                'sort' => 4,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'category_name' => '精品点心',
                'parent_id' => 0,
                'specification_classification_id' => '',
                'sort' => 5,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'category_name' => '时令水果',
                'parent_id' => 0,
                'specification_classification_id' => '',
                'sort' => 6,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'category_name' => '养生汤品',
                'parent_id' => 0,
                'specification_classification_id' => '',
                'sort' => 7,
                'status' => 1
            ]
        ];

        foreach ($goodsCategories as $category) {
            $model = new CsGoodsCategory();
            foreach ($category as $key => $value) {
                $model->$key = $value;
            }
            $model->save();
        }
    }

    /**
     * 初始化配品分类
     * @param int $shopId
     * @throws \Exception
     */
    private function initAccessoriesGoodsCategories(int $shopId)
    {
        $accessoriesCategories = [
            [
                'shop_id' => $shopId,
                'category_name' => '茶具配件',
                'sort' => 1,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'category_name' => '茶食搭配',
                'sort' => 2,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'category_name' => '茶艺用品',
                'sort' => 3,
                'status' => 1
            ]
        ];

        foreach ($accessoriesCategories as $category) {
            $model = new CsAccessoriesGoodsCategory();
            foreach ($category as $key => $value) {
                $model->$key = $value;
            }
            $model->save();
        }
    }

    /**
     * 初始化规格分类
     * @param int $shopId
     * @throws \Exception
     */
    private function initSpecificationClassifications(int $shopId)
    {
        $specificationClassifications = [
            [
                'shop_id' => $shopId,
                'classification_name' => '茶壶规格',
                'sort' => 1,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'classification_name' => '份量规格',
                'sort' => 2,
                'status' => 1
            ]
        ];

        foreach ($specificationClassifications as $classification) {
            $model = new CsSpecificationClassification();
            foreach ($classification as $key => $value) {
                $model->$key = $value;
            }
            $model->save();
        }
    }

    /**
     * 初始化商品标签
     * @param int $shopId
     * @throws \Exception
     */
    private function initGoodsRemarkLabels(int $shopId)
    {
        $remarkLabels = [
            [
                'shop_id' => $shopId,
                'remark_label_name' => '甜度',
                'label_type' => 0, // 单选
                'remark_label_items' => '无糖,少糖,正常,多糖',
                'sort' => 1,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'remark_label_name' => '冰度',
                'label_type' => 0, // 单选
                'remark_label_items' => '去冰,少冰,正常冰,多冰',
                'sort' => 2,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'remark_label_name' => '口感浓度',
                'label_type' => 0, // 单选
                'remark_label_items' => '清淡,适中,浓郁',
                'sort' => 3,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'remark_label_name' => '温度',
                'label_type' => 0, // 单选
                'remark_label_items' => '热饮,温饮,冰饮',
                'sort' => 4,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'remark_label_name' => '特殊要求',
                'label_type' => 1, // 多选
                'remark_label_items' => '去奶泡,加柠檬,加蜂蜜,加冰糖,加牛奶,无咖啡因',
                'sort' => 5,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'remark_label_name' => '辣度',
                'label_type' => 0, // 单选（适用于有辣味的茶点）
                'remark_label_items' => '不辣,微辣,中辣,特辣',
                'sort' => 6,
                'status' => 1
            ],
            [
                'shop_id' => $shopId,
                'remark_label_name' => '包装方式',
                'label_type' => 0, // 单选
                'remark_label_items' => '堂食,打包',
                'sort' => 7,
                'status' => 1
            ]
        ];

        foreach ($remarkLabels as $label) {
            $model = new CsGoodsRemarkLabel();
            foreach ($label as $key => $value) {
                $model->$key = $value;
            }
            $model->save();
        }
    }

    /**
     * 初始化积分规则
     * @param int $shopId
     * @throws \Exception
     */
    private function initPointsRules(int $shopId)
    {
        // 积分获取规则：1元=1积分
        $pointsAcquisition = new CsPointsAcquisition();
        $pointsAcquisition->shop_id = $shopId;
        $pointsAcquisition->money = '1.00';
        $pointsAcquisition->points = 1;
        $pointsAcquisition->save();

        // 积分抵扣规则：100积分=1元
        $pointsDeduction = new CsPointsDeduction();
        $pointsDeduction->shop_id = $shopId;
        $pointsDeduction->points = 100;
        $pointsDeduction->money = '1.00';
        $pointsDeduction->save();
    }

    /**
     * 初始化充值套餐
     * @param int $shopId
     * @throws \Exception
     */
    private function initRechargePackages(int $shopId)
    {
        $rechargePackages = [
            [
                'shop_id' => $shopId,
                'package_name' => '100元充值套餐',
                'recharge_amount' => 100,
                'gift_amount' => 10
            ],
            [
                'shop_id' => $shopId,
                'package_name' => '200元充值套餐',
                'recharge_amount' => 200,
                'gift_amount' => 30
            ],
            [
                'shop_id' => $shopId,
                'package_name' => '500元充值套餐',
                'recharge_amount' => 500,
                'gift_amount' => 80
            ],
            [
                'shop_id' => $shopId,
                'package_name' => '1000元充值套餐',
                'recharge_amount' => 1000,
                'gift_amount' => 200
            ]
        ];

        foreach ($rechargePackages as $package) {
            $model = new CsUserRechargePackage();
            foreach ($package as $key => $value) {
                $model->$key = $value;
            }
            $model->save();
        }
    }

    /**
     * 初始化规格值
     * @param int $shopId
     * @throws \Exception
     */
    private function initSpecifications(int $shopId)
    {
        // 获取刚创建的规格分类ID
        $classificationIds = [];
        $classifications = CsSpecificationClassification::where('shop_id', $shopId)->get();
        foreach ($classifications as $classification) {
            $classificationIds[$classification->classification_name] = $classification->id;
        }

        // 为每个规格分类添加对应的规格值
        $specifications = [
            // 茶壶规格
            [
                'classification_id' => $classificationIds['茶壶规格'] ?? null,
                'specification_name' => '小壶（1-2人）',
                'sort' => 1,
                'status' => 1
            ],
            [
                'classification_id' => $classificationIds['茶壶规格'] ?? null,
                'specification_name' => '中壶（3-4人）',
                'sort' => 2,
                'status' => 1
            ],
            [
                'classification_id' => $classificationIds['茶壶规格'] ?? null,
                'specification_name' => '大壶（5-6人）',
                'sort' => 3,
                'status' => 1
            ],
            [
                'classification_id' => $classificationIds['茶壶规格'] ?? null,
                'specification_name' => '特大壶（7人以上）',
                'sort' => 4,
                'status' => 1
            ],
            
            
            // 份量规格
            [
                'classification_id' => $classificationIds['份量规格'] ?? null,
                'specification_name' => '小份',
                'sort' => 1,
                'status' => 1
            ],
            [
                'classification_id' => $classificationIds['份量规格'] ?? null,
                'specification_name' => '中份',
                'sort' => 2,
                'status' => 1
            ],
            [
                'classification_id' => $classificationIds['份量规格'] ?? null,
                'specification_name' => '大份',
                'sort' => 3,
                'status' => 1
            ],
            [
                'classification_id' => $classificationIds['份量规格'] ?? null,
                'specification_name' => '超大份',
                'sort' => 4,
                'status' => 1
            ]
        ];

        foreach ($specifications as $spec) {
            if ($spec['classification_id']) { // 只有当分类存在时才创建规格值
                $model = new CsSpecification();
                $model->shop_id = $shopId; // 添加缺失的shop_id字段
                foreach ($spec as $key => $value) {
                    $model->$key = $value;
                }
                $model->save();
            }
        }
    }

    /**
     * 初始化桌台
     * @param int $shopId
     * @throws \Exception
     */
    private function initTables(int $shopId)
    {
        // 获取刚创建的桌台分类及其配置
        $classifications = CsTableClassification::where('shop_id', $shopId)->get();
        
        // 为每个桌台分类添加对应的桌台
        foreach ($classifications as $classification) {
            $tableCount = 0;
            
            // 根据分类类型确定桌台数量
            switch ($classification->classification_name) {
                case '散座':
                    $tableCount = 6; // 散座多一些
                    break;
                case '卡座':
                    $tableCount = 4;
                    break;
                case '雅间':
                    $tableCount = 3;
                    break;
                case '贵宾包厢':
                    $tableCount = 2; // 贵宾包厢少一些
                    break;
                default:
                    $tableCount = 3;
                    break;
            }
            
            // 为该分类创建桌台
            for ($i = 1; $i <= $tableCount; $i++) {
                $model = new CsTable();
                $model->shop_id = $shopId;
                $model->classification_id = $classification->id;
                $model->table_name = $classification->classification_name . $i;
                $model->table_qrcode_url = ''; // 二维码URL稍后生成
                $model->billing_rule_id = $classification->billing_rule_id;
                $model->service_rule_id = $classification->service_rule_id;
                $model->default_custom_nums = $classification->default_custom_nums;
                $model->minimum_type = $classification->minimum_type;
                $model->minimum_consumption = $classification->minimum_consumption;
                $model->sort = $i;
                $model->status = 1;
                $model->order_status = 0; // 初始状态为空闲
                $model->order_id = 0; // 初始无订单
                $model->save();
                
                // 为新创建的桌台生成二维码
                // $this->generateTableQrCode($model->id, $shopId);
            }
        }
    }
    
    /**
     * 为桌台生成二维码
     * @param int $tableId 桌台ID
     * @param int $shopId 门店ID
     */
    private function generateTableQrCode(int $tableId, int $shopId): void
    {
        try {
            $table = CsTable::find($tableId);
            if (!$table) {
                return;
            }
            
            $qrCodeService = new \app\service\QrCodeService();
            
            // 生成桌台点餐二维码，传递桌台ID
            $config = [
                'table_id' => $tableId,
                'size' => 300,
                'margin' => 10,
                'label' => $table->table_name . ' 扫码点餐'
            ];
            
            $result = $qrCodeService->generateShopQrCode($shopId, \app\service\QrCodeService::TYPE_TABLE_ORDER, $config);
            
            if ($result['success']) {
                // 更新桌台的二维码URL，使用相对路径
                $qrCodePath = $result['data']['qr_code_path'];
                $relativePath = str_replace(public_path(), '', $qrCodePath);
                $table->table_qrcode_url = $relativePath;
                $table->save();
            }
        } catch (\Exception $e) {
            // 记录错误日志，但不影响桌台的正常创建
            error_log('桌台二维码生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 初始化商品
     * @param int $shopId
     * @throws \Exception
     */
    private function initGoods(int $shopId)
    {
        // 获取刚创建的商品分类及其配置
        $categories = CsGoodsCategory::where('shop_id', $shopId)->get();
        
        // 为每个商品分类添加对应的商品
        foreach ($categories as $category) {
            $goodsList = [];
            
            // 根据分类名称创建对应的商品
            switch ($category->category_name) {
                case '名茶系列':
                    $goodsList = [
                        ['name' => '西湖龙井', 'price' => '88.00', 'cost' => '45.00', 'unit' => '壶'],
                        ['name' => '碧螺春', 'price' => '78.00', 'cost' => '40.00', 'unit' => '壶'],
                        ['name' => '铁观音', 'price' => '98.00', 'cost' => '50.00', 'unit' => '壶'],
                        ['name' => '大红袍', 'price' => '128.00', 'cost' => '65.00', 'unit' => '壶'],
                    ];
                    break;
                case '花草茶':
                    $goodsList = [
                        ['name' => '玫瑰花茶', 'price' => '58.00', 'cost' => '28.00', 'unit' => '壶'],
                        ['name' => '茉莉花茶', 'price' => '48.00', 'cost' => '25.00', 'unit' => '壶'],
                        ['name' => '薰衣草茶', 'price' => '68.00', 'cost' => '35.00', 'unit' => '壶'],
                        ['name' => '洋甘菊茶', 'price' => '55.00', 'cost' => '30.00', 'unit' => '壶'],
                    ];
                    break;
                case '功夫茶':
                    $goodsList = [
                        ['name' => '单枞茶', 'price' => '108.00', 'cost' => '55.00', 'unit' => '壶'],
                        ['name' => '岩茶', 'price' => '118.00', 'cost' => '60.00', 'unit' => '壶'],
                        ['name' => '普洱生茶', 'price' => '98.00', 'cost' => '50.00', 'unit' => '壶'],
                        ['name' => '普洱熟茶', 'price' => '88.00', 'cost' => '45.00', 'unit' => '壶'],
                    ];
                    break;
                case '茶点小食':
                    $goodsList = [
                        ['name' => '绿豆糕', 'price' => '18.00', 'cost' => '8.00', 'unit' => '份'],
                        ['name' => '核桃酥', 'price' => '22.00', 'cost' => '12.00', 'unit' => '份'],
                        ['name' => '瓜子仁', 'price' => '15.00', 'cost' => '7.00', 'unit' => '份'],
                        ['name' => '花生米', 'price' => '12.00', 'cost' => '6.00', 'unit' => '份'],
                    ];
                    break;
                case '精品点心':
                    $goodsList = [
                        ['name' => '蛋黄酥', 'price' => '35.00', 'cost' => '18.00', 'unit' => '份'],
                        ['name' => '凤梨酥', 'price' => '38.00', 'cost' => '20.00', 'unit' => '份'],
                        ['name' => '牛轧糖', 'price' => '28.00', 'cost' => '15.00', 'unit' => '份'],
                        ['name' => '桂花糕', 'price' => '32.00', 'cost' => '16.00', 'unit' => '份'],
                    ];
                    break;
                case '时令水果':
                    $goodsList = [
                        ['name' => '时令水果拼盘', 'price' => '58.00', 'cost' => '28.00', 'unit' => '份'],
                        ['name' => '进口水果拼盘', 'price' => '88.00', 'cost' => '45.00', 'unit' => '份'],
                        ['name' => '季节鲜果茶', 'price' => '48.00', 'cost' => '25.00', 'unit' => '杯'],
                    ];
                    break;
                case '养生汤品':
                    $goodsList = [
                        ['name' => '银耳莲子汤', 'price' => '38.00', 'cost' => '18.00', 'unit' => '份'],
                        ['name' => '红豆薏米汤', 'price' => '35.00', 'cost' => '16.00', 'unit' => '份'],
                        ['name' => '百合雪梨汤', 'price' => '42.00', 'cost' => '20.00', 'unit' => '份'],
                        ['name' => '冰糖燕窝', 'price' => '188.00', 'cost' => '98.00', 'unit' => '份'],
                    ];
                    break;
                default:
                    $goodsList = [
                        ['name' => '特色茶品', 'price' => '68.00', 'cost' => '35.00', 'unit' => '壶'],
                        ['name' => '招牌点心', 'price' => '28.00', 'cost' => '15.00', 'unit' => '份'],
                    ];
                    break;
            }
            
            // 为该分类创建商品
            foreach ($goodsList as $index => $goods) {
                $model = new CsGoods();
                $model->shop_id = $shopId;
                $model->category_id = $category->id;
                $model->goods_name = $goods['name'];
                $model->unit_name = $goods['unit'];
                $model->goods_code = 'G' . str_pad($category->id, 2, '0', STR_PAD_LEFT) . str_pad($index + 1, 3, '0', STR_PAD_LEFT);
                $model->goods_thumb = ''; // 商品图片稍后上传
                $model->goods_thumbs = '';
                $model->goods_price = $goods['price'];
                $model->cost_price = $goods['cost'];
                $model->inventory_nums = 100; // 初始库存
                $model->min_stock = 10; // 最低库存预警
                $model->max_stock = 500; // 最高库存限制
                $model->inventory_management = 1; // 启用库存管理
                $model->specification_type = 0; // 单规格
                $model->specification_item_list = json_encode([]);
                $model->specification_data_list = json_encode([]);
                $model->content = '<p>' . $goods['name'] . '，精心制作，品质保证。</p>';
                $model->accessories_goods_ids = json_encode([]);
                $model->remark_label_ids = json_encode([]);
                $model->sort = $index + 1;
                $model->status = 1; // 上架状态
                $model->save();
            }
        }
    }

    /**
     * 初始化配品商品
     * @param int $shopId
     * @throws \Exception
     */
    private function initAccessoriesGoods(int $shopId)
    {
        // 获取刚创建的配品分类及其配置
        $categories = CsAccessoriesGoodsCategory::where('shop_id', $shopId)->get();
        
        // 为每个配品分类添加对应的配品商品
        foreach ($categories as $category) {
            $goodsList = [];
            
            // 根据分类名称创建对应的配品
            switch ($category->category_name) {
                case '茶具配件':
                    $goodsList = [
                        ['name' => '紫砂茶壶', 'price' => '168.00', 'cost' => '85.00', 'unit' => '个'],
                        ['name' => '公道杯', 'price' => '58.00', 'cost' => '30.00', 'unit' => '个'],
                        ['name' => '品茗杯', 'price' => '38.00', 'cost' => '20.00', 'unit' => '个'],
                        ['name' => '茶漏', 'price' => '28.00', 'cost' => '15.00', 'unit' => '个'],
                        ['name' => '茶夹', 'price' => '25.00', 'cost' => '12.00', 'unit' => '个'],
                        ['name' => '茶盘', 'price' => '88.00', 'cost' => '45.00', 'unit' => '个'],
                    ];
                    break;
                case '茶食搭配':
                    $goodsList = [
                        ['name' => '蜂蜜', 'price' => '28.00', 'cost' => '15.00', 'unit' => '瓶'],
                        ['name' => '柠檬片', 'price' => '18.00', 'cost' => '8.00', 'unit' => '份'],
                        ['name' => '冰糖', 'price' => '15.00', 'cost' => '7.00', 'unit' => '份'],
                        ['name' => '红糖', 'price' => '12.00', 'cost' => '6.00', 'unit' => '份'],
                        ['name' => '牛奶', 'price' => '8.00', 'cost' => '4.00', 'unit' => '份'],
                        ['name' => '奶泡', 'price' => '5.00', 'cost' => '2.00', 'unit' => '份'],
                    ];
                    break;
                case '茶艺用品':
                    $goodsList = [
                        ['name' => '茶巾', 'price' => '18.00', 'cost' => '8.00', 'unit' => '条'],
                        ['name' => '竹茶匙', 'price' => '15.00', 'cost' => '7.00', 'unit' => '个'],
                        ['name' => '茶叶罐', 'price' => '68.00', 'cost' => '35.00', 'unit' => '个'],
                        ['name' => '香炉', 'price' => '98.00', 'cost' => '50.00', 'unit' => '个'],
                        ['name' => '茶席', 'price' => '128.00', 'cost' => '65.00', 'unit' => '套'],
                        ['name' => '花瓶', 'price' => '88.00', 'cost' => '45.00', 'unit' => '个'],
                    ];
                    break;
                default:
                    $goodsList = [
                        ['name' => '茶具套装', 'price' => '188.00', 'cost' => '95.00', 'unit' => '套'],
                        ['name' => '精美茶杯', 'price' => '48.00', 'cost' => '25.00', 'unit' => '个'],
                    ];
                    break;
            }
            
            // 为该分类创建配品
            foreach ($goodsList as $index => $goods) {
                $model = new CsAccessoriesGoods();
                $model->shop_id = $shopId;
                $model->category_id = $category->id;
                $model->goods_name = $goods['name'];
                $model->unit_name = $goods['unit'];
                $model->goods_code = 'A' . str_pad($category->id, 2, '0', STR_PAD_LEFT) . str_pad($index + 1, 3, '0', STR_PAD_LEFT);
                $model->goods_thumb = ''; // 配品图片稍后上传
                $model->goods_price = $goods['price'];
                $model->cost_price = $goods['cost'];
                $model->inventory_nums = 50; // 初始库存（配品库存相对较少）
                $model->sort = $index + 1;
                $model->status = 1; // 上架状态
                $model->save();
            }
        }
    }

    /**
     * 初始化套餐商品
     * @param int $shopId
     * @throws \Exception
     */
    private function initSetMealGoods(int $shopId)
    {
        // 获取已创建的商品，用于构建套餐数据
        $goods = CsGoods::where('shop_id', $shopId)->select(['id', 'goods_name', 'goods_price'])->get();
        $goodsMap = [];
        foreach ($goods as $item) {
            $goodsMap[$item->goods_name] = [
                'goods_id' => $item->id,
                'price' => $item->goods_price
            ];
        }
        
        // 创建茶楼套餐商品列表
        $setMealList = [
            [
                'name' => '下午茶双人套餐',
                'price' => '168.00',
                'cost' => '85.00',
                'unit' => '套',
                'description' => '包含：精选茶品2壶、精美点心4样、时令水果拼盘1份',
                'goods_items' => [
                    ['goods_name' => '西湖龙井', 'quantity' => 1],
                    ['goods_name' => '茉莉花茶', 'quantity' => 1],
                    ['goods_name' => '蛋黄酥', 'quantity' => 1],
                    ['goods_name' => '凤梨酥', 'quantity' => 1],
                    ['goods_name' => '时令水果拼盘', 'quantity' => 1],
                ]
            ],
            [
                'name' => '商务茶歇套餐',
                'price' => '288.00',
                'cost' => '145.00',
                'unit' => '套',
                'description' => '包含：名茶3壶、精品茶点6样、养生汤品2份',
                'goods_items' => [
                    ['goods_name' => '大红袍', 'quantity' => 1],
                    ['goods_name' => '铁观音', 'quantity' => 1],
                    ['goods_name' => '碧螺春', 'quantity' => 1],
                    ['goods_name' => '桂花糕', 'quantity' => 2],
                    ['goods_name' => '牛轧糖', 'quantity' => 2],
                    ['goods_name' => '银耳莲子汤', 'quantity' => 2],
                ]
            ],
            [
                'name' => '情侣浪漫套餐',
                'price' => '228.00',
                'cost' => '115.00',
                'unit' => '套',
                'description' => '包含：花草茶2壶、浪漫点心4样、进口水果拼盘1份、香薰用品',
                'goods_items' => [
                    ['goods_name' => '玫瑰花茶', 'quantity' => 1],
                    ['goods_name' => '薰衣草茶', 'quantity' => 1],
                    ['goods_name' => '蛋黄酥', 'quantity' => 2],
                    ['goods_name' => '凤梨酥', 'quantity' => 2],
                    ['goods_name' => '进口水果拼盘', 'quantity' => 1],
                ]
            ],
            [
                'name' => '家庭聚会套餐',
                'price' => '398.00',
                'cost' => '200.00',
                'unit' => '套',
                'description' => '包含：多种茶品5壶、丰富茶点8样、养生汤品3份、时令水果',
                'goods_items' => [
                    ['goods_name' => '西湖龙井', 'quantity' => 2],
                    ['goods_name' => '普洱熟茶', 'quantity' => 2],
                    ['goods_name' => '茉莉花茶', 'quantity' => 1],
                    ['goods_name' => '绿豆糕', 'quantity' => 2],
                    ['goods_name' => '核桃酥', 'quantity' => 2],
                    ['goods_name' => '银耳莲子汤', 'quantity' => 3],
                    ['goods_name' => '时令水果拼盘', 'quantity' => 2],
                ]
            ],
            [
                'name' => '养生滋补套餐',
                'price' => '328.00',
                'cost' => '165.00',
                'unit' => '套',
                'description' => '包含：养生茶品3壶、滋补汤品4份、健康茶点5样',
                'goods_items' => [
                    ['goods_name' => '洋甘菊茶', 'quantity' => 1],
                    ['goods_name' => '普洱生茶', 'quantity' => 2],
                    ['goods_name' => '冰糖燕窝', 'quantity' => 1],
                    ['goods_name' => '银耳莲子汤', 'quantity' => 2],
                    ['goods_name' => '百合雪梨汤', 'quantity' => 1],
                    ['goods_name' => '桂花糕', 'quantity' => 3],
                ]
            ],
            [
                'name' => '功夫茶体验套餐',
                'price' => '268.00',
                'cost' => '135.00',
                'unit' => '套',
                'description' => '包含：功夫茶4壶、茶艺用品体验、精选茶点6样',
                'goods_items' => [
                    ['goods_name' => '单枞茶', 'quantity' => 1],
                    ['goods_name' => '岩茶', 'quantity' => 1],
                    ['goods_name' => '普洱生茶', 'quantity' => 1],
                    ['goods_name' => '普洱熟茶', 'quantity' => 1],
                    ['goods_name' => '蛋黄酥', 'quantity' => 3],
                    ['goods_name' => '牛轧糖', 'quantity' => 3],
                ]
            ],
        ];
        
        // 创建套餐商品
        foreach ($setMealList as $index => $setMeal) {
            // 构建套餐商品数据列表
            $goodsDataList = [];
            foreach ($setMeal['goods_items'] as $item) {
                if (isset($goodsMap[$item['goods_name']])) {
                    $goodsDataList[] = [
                        'goods_id' => $goodsMap[$item['goods_name']]['goods_id'],
                        'goods_name' => $item['goods_name'],
                        'quantity' => $item['quantity'],
                        'price' => $goodsMap[$item['goods_name']]['price']
                    ];
                }
            }
            
            $model = new CsSetMealGoods();
            $model->shop_id = $shopId;
            $model->goods_name = $setMeal['name'];
            $model->unit_name = $setMeal['unit'];
            $model->goods_code = 'SET' . str_pad($index + 1, 3, '0', STR_PAD_LEFT);
            $model->goods_thumb = ''; // 套餐图片稍后上传
            $model->goods_thumbs = json_encode([]);
            $model->goods_price = $setMeal['price'];
            $model->cost_price = $setMeal['cost'];
            $model->inventory_nums = 30; // 套餐库存相对较少
            $model->goods_data_list = json_encode($goodsDataList);
            $model->content = '<p>' . $setMeal['description'] . '</p>';
            $model->sort = $index + 1;
            $model->status = 1; // 上架状态
            $model->save();
        }
    }

    /**
     * 批量初始化门店所有商品的库存记录
     * @param int $shopId 门店ID
     */
    private function initInventoryRecords(int $shopId): void
    {
        try {
            $inventoryService = new InventoryService();
            
            // 获取所有普通商品
            $goods = CsGoods::where('shop_id', $shopId)->get();
            foreach ($goods as $item) {
                try {
                    $inventoryService->initializeInventory(
                        $item->id,
                        $shopId,
                        $item->inventory_nums ?? 0,
                        floatval($item->cost_price ?? 0),
                        $item->min_stock ?? 0,
                        $item->max_stock ?? 0,
                        \app\enums\GoodsTypeEnum::GOODS // 普通商品类型
                    );
                } catch (\Exception $e) {
                    // 如果库存记录已存在，跳过
                    if (strpos($e->getMessage(), '已存在') === false) {
                        Log::error("商品{$item->id}库存初始化失败: " . $e->getMessage());
                    }
                }
            }
            
            // 获取所有配品
            $accessoriesGoods = CsAccessoriesGoods::where('shop_id', $shopId)->get();
            foreach ($accessoriesGoods as $item) {
                try {
                    $inventoryService->initializeInventory(
                        $item->id,
                        $shopId,
                        $item->inventory_nums ?? 0,
                        floatval($item->cost_price ?? 0),
                        0, // 配品暂不设置最低库存
                        0, // 配品暂不设置最高库存
                        \app\enums\GoodsTypeEnum::ACCESSORIES // 配品类型
                    );
                } catch (\Exception $e) {
                    // 如果库存记录已存在，跳过
                    if (strpos($e->getMessage(), '已存在') === false) {
                        Log::error("配品{$item->id}库存初始化失败: " . $e->getMessage());
                    }
                }
            }
            
            // 获取所有套餐商品
            $setMealGoods = CsSetMealGoods::where('shop_id', $shopId)->get();
            foreach ($setMealGoods as $item) {
                try {
                    $inventoryService->initializeInventory(
                        $item->id,
                        $shopId,
                        $item->inventory_nums ?? 0,
                        floatval($item->cost_price ?? 0),
                        0, // 套餐暂不设置最低库存
                        0, // 套餐暂不设置最高库存
                        \app\enums\GoodsTypeEnum::SET_MEAL // 套餐商品类型
                    );
                } catch (\Exception $e) {
                    // 如果库存记录已存在，跳过
                    if (strpos($e->getMessage(), '已存在') === false) {
                        Log::error("套餐{$item->id}库存初始化失败: " . $e->getMessage());
                    }
                }
            }
            
        } catch (\Exception $e) {
            // 库存初始化失败不影响门店创建
            Log::error("门店{$shopId}库存初始化失败: " . $e->getMessage());
        }
    }

    /**
     * 初始化打印机配置
     * @param int $shopId
     * @throws \Exception
     */
    private function initPrintConfigs(int $shopId)
    {
        $printConfigs = [
            [
                'shop_id' => $shopId,
                'printer_name' => '收银台打印机',
                'printer_type' => 1, // 收银台
                'connection_type' => 1, // USB连接
                'printer_ip' => '',
                'printer_port' => 0,
                'device_name' => 'POS-80',
                'is_default' => 1, // 设为默认收银台打印机
                'status' => 1,
                'print_speed' => 5,
                'paper_size' => '80mm',
                'auto_cut' => 1,
                'remark' => '收银台默认打印机，用于打印订单小票和收据'
            ],
            [
                'shop_id' => $shopId,
                'printer_name' => '后厨打印机',
                'printer_type' => 2, // 后厨
                'connection_type' => 2, // 网络连接
                'printer_ip' => '*************',
                'printer_port' => 9100,
                'device_name' => 'Kitchen-Printer',
                'is_default' => 1, // 设为默认后厨打印机
                'status' => 0,
                'print_speed' => 6,
                'paper_size' => '80mm',
                'auto_cut' => 1,
                'remark' => '后厨默认打印机，用于打印厨房订单和备餐单据'
            ]
        ];

        foreach ($printConfigs as $config) {
            $model = new CsPrintConfig();
            foreach ($config as $key => $value) {
                $model->$key = $value;
            }
            $model->save();
        }
    }

    /**
     * 初始化打印模板
     * @param int $shopId
     * @throws \Exception
     */
    private function initPrintTemplates(int $shopId)
    {
        $printTemplates = [
            [
                'shop_id' => $shopId,
                'template_name' => '订单小票模板',
                'template_type' => 1, // 订单小票
                'template_content' => json_encode([
                    ['id' => 'header_1', 'type' => 'title', 'text' => '{{shop_name}}', 'style' => ['align' => 'left', 'bold' => true, 'fontSize' => 18]],
                    ['id' => 'line_1', 'type' => 'line', 'text' => '', 'style' => ['align' => 'left']],
                    ['id' => 'info_1', 'type' => 'text', 'text' => '订单号：{{order_no}}', 'style' => ['align' => 'left']],
                    ['id' => 'info_2', 'type' => 'text', 'text' => '桌台号：{{classification_name}} - {{table_name}}', 'style' => ['align' => 'left']],
                    ['id' => 'info_3', 'type' => 'text', 'text' => '用餐人数：{{customer_nums}}', 'style' => ['align' => 'left']],
                    ['id' => 'member_info', 'type' => 'text', 'text' => '会员：{{user_name}}（{{user_level_name}}）', 'style' => ['align' => 'left']],
                    ['id' => 'line_2', 'type' => 'line', 'text' => '', 'style' => ['align' => 'left']],
                    ['id' => 'goods_table', 'type' => 'table', 'data' => [['商品', '数量', '单价', '金额'], ['{{goods_name}}', '{{goods_nums}}', '{{goods_price}}', '{{subtotal}}']], 'style' => ['align' => 'left']],
                    ['id' => 'line_3', 'type' => 'line', 'text' => '', 'style' => ['align' => 'left']],
                    ['id' => 'amount_1', 'type' => 'text', 'text' => '商品小计：￥{{goods_money}}', 'style' => ['align' => 'left']],
                    ['id' => 'amount_2', 'type' => 'text', 'text' => '桌台费：￥{{billing_fee}}', 'style' => ['align' => 'left']],
                    ['id' => 'amount_3', 'type' => 'text', 'text' => '服务费：￥{{service_fee}}', 'style' => ['align' => 'left']],
                    ['id' => 'amount_4', 'type' => 'text', 'text' => '会员商品优惠：￥{{user_discount_money}}', 'style' => ['align' => 'left']],
                    ['id' => 'amount_5', 'type' => 'text', 'text' => '会员桌台优惠：￥{{billing_fee_discount_money}}', 'style' => ['align' => 'left']],
                    ['id' => 'amount_6', 'type' => 'text', 'text' => '会员服务费优惠：￥{{service_fee_discount_money}}', 'style' => ['align' => 'left']],
                    ['id' => 'amount_7', 'type' => 'text', 'text' => '整单折扣优惠：￥{{discount_money}}', 'style' => ['align' => 'left']],
                    ['id' => 'amount_8', 'type' => 'text', 'text' => '活动优惠：￥{{marketing_discount_amount}}', 'style' => ['align' => 'left']],
                    ['id' => 'amount_9', 'type' => 'text', 'text' => '手动优惠：￥{{staff_discount_money}}', 'style' => ['align' => 'left']],
                    ['id' => 'amount_10', 'type' => 'text', 'text' => '总优惠金额：￥{{total_discount_money}}', 'style' => ['align' => 'left']],
                    ['id' => 'total_1', 'type' => 'text', 'text' => '实付金额：￥{{real_pay_money}}', 'style' => ['align' => 'left', 'bold' => true, 'fontSize' => 16]],
                    ['id' => 'line_4', 'type' => 'line', 'text' => '', 'style' => ['align' => 'left']],
                    ['id' => 'pay_1', 'type' => 'text', 'text' => '支付方式：{{payment_method_name}}', 'style' => ['align' => 'left']],
                    ['id' => 'pay_2', 'type' => 'text', 'text' => '实收金额：￥{{real_pay_money}}', 'style' => ['align' => 'left']],
                    ['id' => 'line_5', 'type' => 'line', 'text' => '', 'style' => ['align' => 'left']],
                    ['id' => 'footer_1', 'type' => 'text', 'text' => '下单时间：{{create_time}}', 'style' => ['align' => 'left']],
                    ['id' => 'footer_2', 'type' => 'text', 'text' => '支付时间：{{pay_time}}', 'style' => ['align' => 'left']],
                    ['id' => 'footer_3', 'type' => 'text', 'text' => '打印时间：{{print_time}}', 'style' => ['align' => 'left']],
                    ['id' => 'footer_4', 'type' => 'text', 'text' => '收银员：{{shoukuan_admin_name}}', 'style' => ['align' => 'left']],
                    ['id' => 'footer_5', 'type' => 'text', 'text' => '门店地址：{{shop_address}}', 'style' => ['align' => 'left']],
                    ['id' => 'footer_6', 'type' => 'text', 'text' => '门店电话：{{shop_phone}}', 'style' => ['align' => 'left']],
                    ['id' => 'footer_7', 'type' => 'text', 'text' => '谢谢惠顾，欢迎再次光临！', 'style' => ['align' => 'left', 'bold' => true]],
                ]),
                'paper_size' => '80mm',
                'font_size' => 12,
                'is_default' => 1,
                'status' => 1,
                'sort' => 1,
                'remark' => '收银台订单小票打印模板，包含完整订单信息和会员优惠明细'
            ],
            [
                'shop_id' => $shopId,
                'template_name' => '后厨订单模板',
                'template_type' => 2, // 后厨单据
                'template_content' => json_encode([
                    ['id' => 'header_1', 'type' => 'title', 'text' => '后厨订单单', 'style' => ['align' => 'center', 'bold' => true, 'fontSize' => 20]],
                    ['id' => 'line_1', 'type' => 'line', 'text' => '', 'style' => ['align' => 'center']],
                    ['id' => 'info_1', 'type' => 'text', 'text' => '订单号：{{order_no}}', 'style' => ['align' => 'left', 'bold' => true, 'fontSize' => 16]],
                    ['id' => 'info_2', 'type' => 'text', 'text' => '桌台号：{{table_name}}', 'style' => ['align' => 'left', 'bold' => true, 'fontSize' => 16]],
                    ['id' => 'info_3', 'type' => 'text', 'text' => '用餐人数：{{customer_count}}人', 'style' => ['align' => 'left', 'fontSize' => 14]],
                    ['id' => 'info_4', 'type' => 'text', 'text' => '下单时间：{{create_time}}', 'style' => ['align' => 'left']],
                    ['id' => 'info_5', 'type' => 'text', 'text' => '紧急程度：{{urgency}}', 'style' => ['align' => 'left', 'bold' => true]],
                    ['id' => 'line_2', 'type' => 'line', 'text' => '', 'style' => ['align' => 'center']],
                    ['id' => 'goods_title', 'type' => 'text', 'text' => '制作清单', 'style' => ['align' => 'center', 'bold' => true, 'fontSize' => 16]],
                    ['id' => 'goods_table', 'type' => 'table', 'data' => [['商品名称', '数量', '备注'], ['{{name}}', '{{num}}', '{{remark}}']], 'style' => ['align' => 'left', 'fontSize' => 14]],
                    ['id' => 'line_3', 'type' => 'line', 'text' => '', 'style' => ['align' => 'center']],
                    ['id' => 'special_title', 'type' => 'text', 'text' => '特殊要求：', 'style' => ['align' => 'left', 'bold' => true, 'fontSize' => 14]],
                    ['id' => 'special_content', 'type' => 'text', 'text' => '{{special_requirements}}', 'style' => ['align' => 'left', 'fontSize' => 14]],
                    ['id' => 'line_4', 'type' => 'line', 'text' => '', 'style' => ['align' => 'center']],
                    ['id' => 'footer_1', 'type' => 'text', 'text' => '打印时间：{{print_time}}', 'style' => ['align' => 'center']]
                ]),
                'paper_size' => '80mm',
                'font_size' => 14,
                'is_default' => 1,
                'status' => 1,
                'sort' => 2,
                'remark' => '后厨订单打印模板，突出制作要求和特殊备注'
            ],
            [
                'shop_id' => $shopId,
                'template_name' => '预约单模板',
                'template_type' => 3, // 预约单
                'template_content' => json_encode([
                    ['id' => 'header_1', 'type' => 'title', 'text' => '{{shop_name}}', 'style' => ['align' => 'center', 'bold' => true, 'fontSize' => 18]],
                    ['id' => 'header_2', 'type' => 'title', 'text' => '预约确认单', 'style' => ['align' => 'center', 'bold' => true, 'fontSize' => 16]],
                    ['id' => 'line_1', 'type' => 'line', 'text' => '', 'style' => ['align' => 'center']],
                    ['id' => 'info_1', 'type' => 'text', 'text' => '预约号：{{reservation_no}}', 'style' => ['align' => 'left', 'bold' => true]],
                    ['id' => 'info_2', 'type' => 'text', 'text' => '客户姓名：{{customer_name}}', 'style' => ['align' => 'left']],
                    ['id' => 'info_3', 'type' => 'text', 'text' => '联系电话：{{customer_phone}}', 'style' => ['align' => 'left']],
                    ['id' => 'info_4', 'type' => 'text', 'text' => '预约日期：{{reservation_date}}', 'style' => ['align' => 'left', 'bold' => true]],
                    ['id' => 'info_5', 'type' => 'text', 'text' => '预约时间：{{reservation_time}}', 'style' => ['align' => 'left', 'bold' => true]],
                    ['id' => 'info_6', 'type' => 'text', 'text' => '预约桌台：{{table_name}}', 'style' => ['align' => 'left', 'bold' => true]],
                    ['id' => 'info_7', 'type' => 'text', 'text' => '预约人数：{{guest_count}}人', 'style' => ['align' => 'left']],
                    ['id' => 'line_2', 'type' => 'line', 'text' => '', 'style' => ['align' => 'center']],
                    ['id' => 'special_title', 'type' => 'text', 'text' => '特殊要求：', 'style' => ['align' => 'left', 'bold' => true]],
                    ['id' => 'special_content', 'type' => 'text', 'text' => '{{special_requirements}}', 'style' => ['align' => 'left']],
                    ['id' => 'line_3', 'type' => 'line', 'text' => '', 'style' => ['align' => 'center']],
                    ['id' => 'footer_1', 'type' => 'text', 'text' => '预约时间：{{create_time}}', 'style' => ['align' => 'left']],
                    ['id' => 'footer_2', 'type' => 'text', 'text' => '请准时到店，如需取消请提前联系', 'style' => ['align' => 'center', 'bold' => true]]
                ]),
                'paper_size' => '80mm',
                'font_size' => 12,
                'is_default' => 1,
                'status' => 1,
                'sort' => 3,
                'remark' => '预约单打印模板，用于打印客户预约确认单'
            ],
            [
                'shop_id' => $shopId,
                'template_name' => '日结报表模板',
                'template_type' => 4, // 财务报表
                'template_content' => json_encode([
                    ['id' => 'header_1', 'type' => 'title', 'text' => '{{shop_name}}', 'style' => ['align' => 'center', 'bold' => true, 'fontSize' => 18]],
                    ['id' => 'header_2', 'type' => 'title', 'text' => '营业日报表', 'style' => ['align' => 'center', 'bold' => true, 'fontSize' => 16]],
                    ['id' => 'header_3', 'type' => 'text', 'text' => '统计日期：{{report_date}}', 'style' => ['align' => 'center']],
                    ['id' => 'line_1', 'type' => 'line', 'text' => '', 'style' => ['align' => 'center']],
                    ['id' => 'sales_title', 'type' => 'text', 'text' => '营业汇总', 'style' => ['align' => 'center', 'bold' => true, 'fontSize' => 14]],
                    ['id' => 'sales_1', 'type' => 'text', 'text' => '订单总数：{{total_orders}}单', 'style' => ['align' => 'left']],
                    ['id' => 'sales_2', 'type' => 'text', 'text' => '营业总额：￥{{total_amount}}', 'style' => ['align' => 'left', 'bold' => true]],
                    ['id' => 'sales_3', 'type' => 'text', 'text' => '现金收入：￥{{cash_amount}}', 'style' => ['align' => 'left']],
                    ['id' => 'sales_4', 'type' => 'text', 'text' => '刷卡收入：￥{{card_amount}}', 'style' => ['align' => 'left']],
                    ['id' => 'sales_5', 'type' => 'text', 'text' => '线上支付：￥{{online_amount}}', 'style' => ['align' => 'left']],
                    ['id' => 'line_2', 'type' => 'line', 'text' => '', 'style' => ['align' => 'center']],
                    ['id' => 'goods_title', 'type' => 'text', 'text' => '商品汇总', 'style' => ['align' => 'center', 'bold' => true, 'fontSize' => 14]],
                    ['id' => 'goods_table', 'type' => 'table', 'data' => [['商品名称', '数量', '金额'], ['{{goods_name}}', '{{quantity}}', '{{amount}}']], 'style' => ['align' => 'left']],
                    ['id' => 'line_3', 'type' => 'line', 'text' => '', 'style' => ['align' => 'center']],
                    ['id' => 'table_title', 'type' => 'text', 'text' => '桌台汇总', 'style' => ['align' => 'center', 'bold' => true, 'fontSize' => 14]],
                    ['id' => 'table_1', 'type' => 'text', 'text' => '翻台率：{{table_turnover}}', 'style' => ['align' => 'left']],
                    ['id' => 'table_2', 'type' => 'text', 'text' => '平均消费：￥{{avg_consumption}}', 'style' => ['align' => 'left']],
                    ['id' => 'line_4', 'type' => 'line', 'text' => '', 'style' => ['align' => 'center']],
                    ['id' => 'footer_1', 'type' => 'text', 'text' => '打印时间：{{print_time}}', 'style' => ['align' => 'left']],
                    ['id' => 'footer_2', 'type' => 'text', 'text' => '操作员：{{operator}}', 'style' => ['align' => 'left']]
                ]),
                'paper_size' => '80mm',
                'font_size' => 10,
                'is_default' => 1,
                'status' => 1,
                'sort' => 4,
                'remark' => '日结营业报表模板，包含营业数据汇总'
            ]
        ];

        foreach ($printTemplates as $template) {
            $model = new CsPrintTemplate();
            foreach ($template as $key => $value) {
                $model->$key = $value;
            }
            $model->save();
        }
    }
}
