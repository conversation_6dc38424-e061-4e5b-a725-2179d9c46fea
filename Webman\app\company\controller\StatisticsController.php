<?php

namespace app\company\controller;

use app\model\CsCompany;
use app\model\CsShop;
use app\model\CsOrder;
use app\model\CsOrderGoods;
use app\model\CsGoods;
use app\model\CsInventory;
use support\Request;
use support\Response;
use support\Log;
use support\Db;

class StatisticsController
{
    public function statistics(Request $request): Response
    {
        $company = CsCompany::select([
            'id', 'trade_id', 'product_version_id', 'company_name',
            'buy_at', 'expired_at',
        ])->find($request->company_id);
        $company->trade_name = $company->csTrade->trade_name;
        $company->version_name = $company->csProductVersion->version_name;
        $company->loadCount(['csShop', 'csUser']);
        unset($company->csTrade, $company->csProductVersion);
        $backData = [
            'company' => $company
        ];
        return  success($backData);
    }

    /**
     * 获取财务数据汇总
     * @param Request $request
     * @return Response
     */
    public function financeSummary(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'month'); // today, week, month, quarter, year
            $shopId = $request->get('shop_id', 0); // 0表示所有门店
            
            $financialData = $this->getFinancialSummary($companyId, $period, $shopId);
            
            return success([
                'period' => $period,
                'shop_id' => $shopId,
                'financial_data' => $financialData
            ]);
        } catch (\Exception $e) {
            Log::error('获取财务数据汇总失败: ' . $e->getMessage());
            return fail('获取财务数据失败');
        }
    }

    /**
     * 获取成本分析数据
     * @param Request $request
     * @return Response
     */
    public function costAnalysis(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'month');
            $shopId = $request->get('shop_id', 0);
            
            $costData = $this->getCostAnalysisData($companyId, $period, $shopId);
            
            return success([
                'period' => $period,
                'shop_id' => $shopId,
                'cost_analysis' => $costData
            ]);
        } catch (\Exception $e) {
            Log::error('获取成本分析数据失败: ' . $e->getMessage());
            return fail('获取成本分析数据失败');
        }
    }

    /**
     * 获取利润分析数据
     * @param Request $request
     * @return Response
     */
    public function profitAnalysis(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'month');
            $shopId = $request->get('shop_id', 0);
            
            $profitData = $this->getProfitAnalysisData($companyId, $period, $shopId);
            
            return success([
                'period' => $period,
                'shop_id' => $shopId,
                'profit_analysis' => $profitData
            ]);
        } catch (\Exception $e) {
            Log::error('获取利润分析数据失败: ' . $e->getMessage());
            return fail('获取利润分析数据失败');
        }
    }

    /**
     * 导出财务报表
     * @param Request $request
     * @return Response
     */
    public function exportReport(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $reportType = $request->get('type', 'summary'); // summary, detail, profit, cost
            $period = $request->get('period', 'month');
            $shopId = $request->get('shop_id', 0);
            $format = $request->get('format', 'excel'); // excel, pdf
            
            $reportData = $this->generateReportData($companyId, $reportType, $period, $shopId);
            
            // 这里应该调用报表导出服务，生成Excel或PDF文件
            // 为了演示，这里返回数据结构
            return success([
                'report_type' => $reportType,
                'period' => $period,
                'shop_id' => $shopId,
                'format' => $format,
                'data' => $reportData,
                'export_url' => '/exports/financial_report_' . time() . '.' . ($format === 'pdf' ? 'pdf' : 'xlsx')
            ]);
        } catch (\Exception $e) {
            Log::error('导出财务报表失败: ' . $e->getMessage());
            return fail('导出报表失败');
        }
    }

    /**
     * 获取销售趋势分析
     * @param Request $request
     * @return Response
     */
    public function salesTrend(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'month');
            $shopId = $request->get('shop_id', 0);
            $granularity = $request->get('granularity', 'day'); // hour, day, week, month
            
            $trendData = $this->getSalesTrendData($companyId, $period, $shopId, $granularity);
            
            return success([
                'period' => $period,
                'shop_id' => $shopId,
                'granularity' => $granularity,
                'trend_data' => $trendData
            ]);
        } catch (\Exception $e) {
            Log::error('获取销售趋势分析失败: ' . $e->getMessage());
            return fail('获取销售趋势分析失败');
        }
    }

    /**
     * 获取财务汇总数据
     * @param int $companyId
     * @param string $period
     * @param int $shopId
     * @return array
     */
    private function getFinancialSummary(int $companyId, string $period, int $shopId): array
    {
        try {
            $shopIds = $shopId > 0 
                ? [$shopId] 
                : CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            
            $query = CsOrder::whereIn('shop_id', $shopIds);
            $this->applyPeriodFilter($query, $period);
            
            $summary = $query->selectRaw('
                    COUNT(*) as total_orders,
                    SUM(total_amount) as total_revenue,
                    SUM(discount_amount) as total_discount,
                    AVG(total_amount) as avg_order_amount,
                    MAX(total_amount) as max_order_amount,
                    MIN(total_amount) as min_order_amount
                ')
                ->first();

            // 获取支付方式统计
            $paymentStats = $query->selectRaw('
                    payment_method,
                    COUNT(*) as order_count,
                    SUM(total_amount) as amount
                ')
                ->groupBy('payment_method')
                ->get();

            return [
                'summary' => [
                    'total_orders' => $summary->total_orders ?? 0,
                    'total_revenue' => $summary->total_revenue ?? 0,
                    'total_discount' => $summary->total_discount ?? 0,
                    'avg_order_amount' => $summary->avg_order_amount ?? 0,
                    'max_order_amount' => $summary->max_order_amount ?? 0,
                    'min_order_amount' => $summary->min_order_amount ?? 0
                ],
                'payment_stats' => $paymentStats->toArray()
            ];
        } catch (\Exception $e) {
            Log::error('获取财务汇总数据失败: ' . $e->getMessage());
            return [
                'summary' => [
                    'total_orders' => 0,
                    'total_revenue' => 0,
                    'total_discount' => 0,
                    'avg_order_amount' => 0,
                    'max_order_amount' => 0,
                    'min_order_amount' => 0
                ],
                'payment_stats' => []
            ];
        }
    }

    /**
     * 获取成本分析数据
     * @param int $companyId
     * @param string $period
     * @param int $shopId
     * @return array
     */
    private function getCostAnalysisData(int $companyId, string $period, int $shopId): array
    {
        try {
            $shopIds = $shopId > 0 
                ? [$shopId] 
                : CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            
            // 获取商品成本分析
            $query = CsOrderGoods::join('cs_orders', 'cs_order_goods.order_id', '=', 'cs_orders.id')
                ->join('cs_goods', 'cs_order_goods.goods_id', '=', 'cs_goods.id')
                ->whereIn('cs_orders.shop_id', $shopIds);
            
            $this->applyPeriodFilter($query, $period, 'cs_orders.created_at');
            
            $costAnalysis = $query->selectRaw('
                    SUM(cs_order_goods.quantity * cs_goods.cost_price) as total_cost,
                    SUM(cs_order_goods.quantity * cs_order_goods.price) as total_revenue,
                    SUM(cs_order_goods.quantity * (cs_order_goods.price - cs_goods.cost_price)) as total_profit
                ')
                ->first();

            // 按商品分类的成本分析
            $categoryAnalysis = $query->join('cs_goods_categories', 'cs_goods.category_id', '=', 'cs_goods_categories.id')
                ->selectRaw('
                    cs_goods_categories.category_name,
                    SUM(cs_order_goods.quantity * cs_goods.cost_price) as category_cost,
                    SUM(cs_order_goods.quantity * cs_order_goods.price) as category_revenue,
                    SUM(cs_order_goods.quantity * (cs_order_goods.price - cs_goods.cost_price)) as category_profit
                ')
                ->groupBy('cs_goods_categories.id', 'cs_goods_categories.category_name')
                ->get();

            return [
                'overall' => [
                    'total_cost' => $costAnalysis->total_cost ?? 0,
                    'total_revenue' => $costAnalysis->total_revenue ?? 0,
                    'total_profit' => $costAnalysis->total_profit ?? 0,
                    'profit_margin' => $costAnalysis->total_revenue > 0 
                        ? round(($costAnalysis->total_profit / $costAnalysis->total_revenue) * 100, 2) 
                        : 0
                ],
                'by_category' => $categoryAnalysis->toArray()
            ];
        } catch (\Exception $e) {
            Log::error('获取成本分析数据失败: ' . $e->getMessage());
            return [
                'overall' => [
                    'total_cost' => 0,
                    'total_revenue' => 0,
                    'total_profit' => 0,
                    'profit_margin' => 0
                ],
                'by_category' => []
            ];
        }
    }

    /**
     * 获取利润分析数据
     * @param int $companyId
     * @param string $period
     * @param int $shopId
     * @return array
     */
    private function getProfitAnalysisData(int $companyId, string $period, int $shopId): array
    {
        try {
            $shopIds = $shopId > 0 
                ? [$shopId] 
                : CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            
            // 按门店的利润分析
            $shopProfitQuery = CsOrderGoods::join('cs_orders', 'cs_order_goods.order_id', '=', 'cs_orders.id')
                ->join('cs_goods', 'cs_order_goods.goods_id', '=', 'cs_goods.id')
                ->join('cs_shops', 'cs_orders.shop_id', '=', 'cs_shops.id')
                ->whereIn('cs_orders.shop_id', $shopIds);
            
            $this->applyPeriodFilter($shopProfitQuery, $period, 'cs_orders.created_at');
            
            $shopProfit = $shopProfitQuery->selectRaw('
                    cs_shops.id as shop_id,
                    cs_shops.shop_name,
                    SUM(cs_order_goods.quantity * cs_goods.cost_price) as shop_cost,
                    SUM(cs_order_goods.quantity * cs_order_goods.price) as shop_revenue,
                    SUM(cs_order_goods.quantity * (cs_order_goods.price - cs_goods.cost_price)) as shop_profit
                ')
                ->groupBy('cs_shops.id', 'cs_shops.shop_name')
                ->get();

            // 按时间的利润趋势
            $profitTrendQuery = CsOrderGoods::join('cs_orders', 'cs_order_goods.order_id', '=', 'cs_orders.id')
                ->join('cs_goods', 'cs_order_goods.goods_id', '=', 'cs_goods.id')
                ->whereIn('cs_orders.shop_id', $shopIds);
            
            $this->applyPeriodFilter($profitTrendQuery, $period, 'cs_orders.created_at');
            
            $profitTrend = $profitTrendQuery->selectRaw('
                    DATE(cs_orders.created_at) as date,
                    SUM(cs_order_goods.quantity * cs_goods.cost_price) as daily_cost,
                    SUM(cs_order_goods.quantity * cs_order_goods.price) as daily_revenue,
                    SUM(cs_order_goods.quantity * (cs_order_goods.price - cs_goods.cost_price)) as daily_profit
                ')
                ->groupBy(Db::raw('DATE(cs_orders.created_at)'))
                ->orderBy('date')
                ->get();

            return [
                'by_shop' => $shopProfit->toArray(),
                'trend' => $profitTrend->toArray()
            ];
        } catch (\Exception $e) {
            Log::error('获取利润分析数据失败: ' . $e->getMessage());
            return [
                'by_shop' => [],
                'trend' => []
            ];
        }
    }

    /**
     * 生成报表数据
     * @param int $companyId
     * @param string $reportType
     * @param string $period
     * @param int $shopId
     * @return array
     */
    private function generateReportData(int $companyId, string $reportType, string $period, int $shopId): array
    {
        switch ($reportType) {
            case 'summary':
                return $this->getFinancialSummary($companyId, $period, $shopId);
            case 'cost':
                return $this->getCostAnalysisData($companyId, $period, $shopId);
            case 'profit':
                return $this->getProfitAnalysisData($companyId, $period, $shopId);
            default:
                return $this->getFinancialSummary($companyId, $period, $shopId);
        }
    }

    /**
     * 获取销售趋势数据
     * @param int $companyId
     * @param string $period
     * @param int $shopId
     * @param string $granularity
     * @return array
     */
    private function getSalesTrendData(int $companyId, string $period, int $shopId, string $granularity): array
    {
        try {
            $shopIds = $shopId > 0 
                ? [$shopId] 
                : CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            
            $query = CsOrder::whereIn('shop_id', $shopIds);
            $this->applyPeriodFilter($query, $period);
            
            switch ($granularity) {
                case 'hour':
                    $groupBy = 'DATE(created_at), HOUR(created_at)';
                    $selectRaw = 'DATE(created_at) as date, HOUR(created_at) as hour';
                    break;
                case 'day':
                    $groupBy = 'DATE(created_at)';
                    $selectRaw = 'DATE(created_at) as date';
                    break;
                case 'week':
                    $groupBy = 'YEARWEEK(created_at)';
                    $selectRaw = 'YEARWEEK(created_at) as week';
                    break;
                case 'month':
                    $groupBy = 'YEAR(created_at), MONTH(created_at)';
                    $selectRaw = 'YEAR(created_at) as year, MONTH(created_at) as month';
                    break;
                default:
                    $groupBy = 'DATE(created_at)';
                    $selectRaw = 'DATE(created_at) as date';
            }
            
            $trendData = $query->selectRaw("
                    {$selectRaw},
                    COUNT(*) as orders,
                    SUM(total_amount) as revenue,
                    AVG(total_amount) as avg_order_amount
                ")
                ->groupBy(Db::raw($groupBy))
                ->orderBy('created_at')
                ->get();

            return $trendData->toArray();
        } catch (\Exception $e) {
            Log::error('获取销售趋势数据失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 应用时间段过滤
     * @param $query
     * @param string $period
     * @param string $dateField
     */
    private function applyPeriodFilter($query, string $period, string $dateField = 'created_at'): void
    {
        switch ($period) {
            case 'today':
                $query->whereDate($dateField, date('Y-m-d'));
                break;
            case 'week':
                $query->where($dateField, '>=', date('Y-m-d', strtotime('-6 days')));
                break;
            case 'month':
                $query->whereMonth($dateField, date('m'))
                      ->whereYear($dateField, date('Y'));
                break;
            case 'quarter':
                $query->where($dateField, '>=', date('Y-m-d', strtotime('-89 days')));
                break;
            case 'year':
                $query->whereYear($dateField, date('Y'));
                break;
        }
    }
}
