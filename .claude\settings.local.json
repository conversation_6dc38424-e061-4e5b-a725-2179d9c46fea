{"permissions": {"allow": ["Bash(npx vue-tsc:*)", "Bash(npm run build:*)", "Bash(ls:*)", "Bash(npx vite:*)", "Bash(rg:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(grep:*)", "Bash(find:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(php:*)", "Bash(cp:*)", "<PERSON><PERSON>(composer show:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(chmod:*)", "Bash(npm install)", "Bash(node:*)", "Bash(npm create vue:*)", "Bash(npm install:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run type-check:*)", "<PERSON><PERSON>(touch:*)", "Bash(timeout 10s npm run dev)", "<PERSON><PERSON>(python3:*)", "Bash(npm:*)", "Bash(npx:*)", "<PERSON><PERSON>(timeout 300 npx playwright install chromium)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "<PERSON><PERSON>(timeout 30 npx @playwright/test --init)", "mcp__playwritht__playwright_navigate", "<PERSON><PERSON>(curl:*)", "Bash(git reset:*)"], "deny": []}}