<template>
  <!-- 如果有子菜单，渲染子菜单 -->
  <el-sub-menu 
    v-if="hasChildren" 
    :index="menu.menu_sign"
  >
    <template #title>
      <menu-icon :icon="menu.menu_img || menu.custom_icon" v-if="menu.pid === 0" />
      <span>{{ menu.custom_name || menu.menu_name }}</span>
    </template>
    
    <sidebar-item
      v-for="child in menu.children"
      :key="child.id"
      :menu="child"
      :base-path="basePath"
      @navigate="$emit('navigate', $event)"
    />
  </el-sub-menu>
  
  <!-- 如果没有子菜单，渲染菜单项 -->
  <el-menu-item 
    v-else
    :index="menu.menu_sign"
    @click="handleClick"
  >
    <menu-icon :icon="menu.menu_img || menu.custom_icon" v-if="menu.pid === 0" />
    <span>{{ menu.custom_name || menu.menu_name }}</span>
  </el-menu-item>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import MenuIcon from './MenuIcon.vue'
import type { MenuInfo } from '@/services/menuService'

interface Props {
  menu: MenuInfo
  basePath?: string
}

interface Emits {
  (e: 'navigate', path: string): void
}

const props = withDefaults(defineProps<Props>(), {
  basePath: ''
})

const emit = defineEmits<Emits>()

// 是否有子菜单
const hasChildren = computed(() => {
  return props.menu.children && props.menu.children.length > 0
})

// 菜单路径映射
const getMenuPath = (menuSign: string): string => {
  const pathMap: Record<string, string> = {
    // 收银台
    table_business: '/table/business',
    retail: '/retail',
    reservation: '/reservation',
    
    // 会员管理
    member_overview: '/member/overview',
    member_users: '/member/users',
    member_level: '/member/user-level',
    member_recharge: '/member/recharge-package',
    member_points_get: '/member/points',
    member_points_use: '/member/deduction',
    member_balance: '/member/balance-log',
    member_points: '/member/points-log',
    
    // 账单管理
    order_table: '/order/table',
    order_retail: '/order/retail',
    
    // 报表
    finance_overview: '/finance/reports/business-overview',
    finance_monthly: '/finance/reports/monthly-report',
    finance_goods_rank: '/finance/reports/goods-ranking',
    finance_staff: '/finance/reports/staff-performance',
    finance_profit: '/finance/reports/profit-analysis',
    
    // 营销
    coupon: '/marketing/coupon',
    marketing_activity: '/marketing/activity',
    
    // 库存
    inventory_overview: '/inventory/index',
    inventory_check: '/inventory/check',
    inventory_log: '/inventory/log',
    
    // 消息
    message_center: '/message/message-center',
    
    // 设置 - 桌台
    table_category: '/table/classification',
    table_manage: '/table/index',
    table_config: '/settings/table',
    
    // 设置 - 商品
    goods_category: '/goods/category',
    goods_manage: '/goods/index',
    goods_package: '/goods/set-meal',
    goods_material_category: '/goods/accessories-category',
    goods_material: '/goods/accessories-goods',
    goods_tag: '/goods/remark-label',
    goods_spec_category: '/goods/specification-classification',
    goods_spec: '/goods/specification',
    
    // 设置 - 支付
    payment_method: '/settings/payment/method',
    
    // 设置 - 计费
    table_billing: '/settings/billing/table',
    service_fee: '/settings/billing/service',
    
    // 设置 - 打印
    print_template: '/settings/print/template',
    printer_config: '/settings/print/config',
    
    // 权限管理
    staff_admin: '/staff/list',
    staff_role: '/staff/role'
  }
  
  return pathMap[menuSign] || `/${menuSign.replace(/_/g, '/')}`
}

// 处理菜单点击
const handleClick = () => {
  const path = getMenuPath(props.menu.menu_sign)
  emit('navigate', path)
}
</script>

<script lang="ts">
export default {
  name: 'SidebarItem'
}
</script>