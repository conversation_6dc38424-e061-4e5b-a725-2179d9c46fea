import { ref, reactive, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  WebSocketStatus,
  WebSocketMessageType,
  BusinessAction,
  type WebSocketMessage,
  type HeartbeatMessage,
  type ResponseMessage,
  type WebSocketConfig,
  type WebSocketState,
  type WebSocketManager,
  type MessageHandler,
  type BusinessMessage,
  type JoinGroupData
} from '@/types/realtime'
import { getCurrentShopId } from '@/utils/auth'

// 默认配置
const DEFAULT_CONFIG: WebSocketConfig = {
  url: 'ws://127.0.0.1:39888',
  /*url: 'ws://121.41.3.40:39888',*/
  heartbeatInterval: 20000, // 20秒
  reconnectInterval: 3000,  // 3秒
  maxReconnectAttempts: 10,
  timeout: 10000 // 10秒
}

// 全局WebSocket实例（单例模式）
let globalWebSocketInstance: ReturnType<typeof createWebSocketManager> | null = null

/**
 * 创建WebSocket管理器
 */
function createWebSocketManager(config: Partial<WebSocketConfig> = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  
  // WebSocket实例
  let ws: WebSocket | null = null
  let heartbeatTimer: number | null = null
  let reconnectTimer: number | null = null
  let connectPromise: Promise<void> | null = null
  
  // 状态管理
  const state = reactive<WebSocketState>({
    status: WebSocketStatus.DISCONNECTED,
    connected: false,
    reconnectAttempts: 0,
    lastHeartbeat: 0,
    lastMessage: 0
  })
  
  // 事件处理器
  const messageHandlers: MessageHandler[] = []
  const connectHandlers: (() => void)[] = []
  const disconnectHandlers: (() => void)[] = []
  const errorHandlers: ((error: Event) => void)[] = []
  
  /**
   * 发送心跳
   */
  const sendHeartbeat = () => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      const heartbeat: HeartbeatMessage = {
        type: WebSocketMessageType.PING
      }
      ws.send(JSON.stringify(heartbeat))
      state.lastHeartbeat = Date.now()
    }
  }
  
  /**
   * 发送加入群组消息
   */
  const sendJoinGroup = () => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      const shopId = getCurrentShopId()
      const joinMessage: BusinessMessage = {
        type: WebSocketMessageType.BUSINESS,
        action: BusinessAction.JOIN_GROUP,
        data: {
          shop_id: shopId
        }
      }
      console.log('发送加入群组消息:', joinMessage)
      ws.send(JSON.stringify(joinMessage))
    }
  }
  
  /**
   * 启动心跳
   */
  const startHeartbeat = () => {
    stopHeartbeat()
    heartbeatTimer = window.setInterval(sendHeartbeat, finalConfig.heartbeatInterval)
  }
  
  /**
   * 停止心跳
   */
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }
  
  /**
   * 处理接收到的消息
   */
  const handleMessage = (event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      state.lastMessage = Date.now()
      
      // 处理心跳响应
      if (message.type === WebSocketMessageType.PONG) {
        return
      }
      
      // 触发消息处理器
      messageHandlers.forEach(handler => {
        try {
          handler(message as ResponseMessage)
        } catch (error) {
          console.error('消息处理器执行失败:', error)
        }
      })
    } catch (error) {
      console.error('WebSocket消息解析失败:', error)
    }
  }
  
  /**
   * 处理连接打开
   */
  const handleOpen = () => {
    console.log('WebSocket连接已建立')
    state.status = WebSocketStatus.CONNECTED
    state.connected = true
    state.reconnectAttempts = 0
    
    // 启动心跳
    startHeartbeat()
    
    // 发送加入群组消息
    sendJoinGroup()
    
    // 触发连接事件
    connectHandlers.forEach(handler => {
      try {
        handler()
      } catch (error) {
        console.error('连接处理器执行失败:', error)
      }
    })
  }
  
  /**
   * 处理连接关闭
   */
  const handleClose = (event: CloseEvent) => {
    console.log('WebSocket连接已关闭:', event.code, event.reason)
    state.connected = false
    stopHeartbeat()
    
    // 只有在非主动关闭的情况下才重连
    if (state.status !== WebSocketStatus.DISCONNECTED && event.code !== 1000) {
      state.status = WebSocketStatus.RECONNECTING
      scheduleReconnect()
    } else {
      state.status = WebSocketStatus.DISCONNECTED
    }
    
    // 触发断开连接事件
    disconnectHandlers.forEach(handler => {
      try {
        handler()
      } catch (error) {
        console.error('断开连接处理器执行失败:', error)
      }
    })
  }
  
  /**
   * 处理连接错误
   */
  const handleError = (event: Event) => {
    console.error('WebSocket连接错误:', event)
    state.status = WebSocketStatus.ERROR
    
    // 触发错误事件
    errorHandlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        console.error('错误处理器执行失败:', error)
      }
    })
  }
  
  /**
   * 安排重连
   */
  const scheduleReconnect = () => {
    if (state.reconnectAttempts >= finalConfig.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达到最大限制')
      state.status = WebSocketStatus.ERROR
      ElMessage.error('网络连接失败，请检查网络后刷新页面')
      return
    }
    
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
    }
    
    const delay = Math.min(
      finalConfig.reconnectInterval * Math.pow(2, state.reconnectAttempts),
      30000 // 最大30秒
    )
    
    reconnectTimer = window.setTimeout(() => {
      state.reconnectAttempts++
      console.log(`WebSocket重连尝试 ${state.reconnectAttempts}/${finalConfig.maxReconnectAttempts}`)
      connect()
    }, delay)
  }
  
  /**
   * 建立连接
   */
  const connect = async (): Promise<void> => {
    if (state.connected) {
      return Promise.resolve()
    }
    
    if (connectPromise) {
      return connectPromise
    }
    
    connectPromise = new Promise((resolve, reject) => {
      try {
        state.status = WebSocketStatus.CONNECTING
        
        // 关闭现有连接
        if (ws) {
          ws.close()
        }
        
        // 创建新连接
        console.log('正在连接WebSocket:', finalConfig.url)
        ws = new WebSocket(finalConfig.url)
        
        // 设置事件监听器
        ws.onopen = () => {
          handleOpen()
          connectPromise = null
          resolve()
        }
        
        ws.onmessage = handleMessage
        ws.onclose = handleClose
        ws.onerror = (event) => {
          handleError(event)
          connectPromise = null
          reject(new Error('WebSocket连接失败'))
        }
        
        // 连接超时处理
        setTimeout(() => {
          if (ws && ws.readyState === WebSocket.CONNECTING) {
            ws.close()
            connectPromise = null
            reject(new Error('WebSocket连接超时'))
          }
        }, finalConfig.timeout)
        
      } catch (error) {
        connectPromise = null
        reject(error)
      }
    })
    
    return connectPromise
  }
  
  /**
   * 断开连接
   */
  const disconnect = () => {
    state.status = WebSocketStatus.DISCONNECTED
    stopHeartbeat()
    
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    if (ws) {
      ws.close(1000, '主动断开连接')
      ws = null
    }
    
    connectPromise = null
  }
  
  /**
   * 重新连接
   */
  const reconnect = () => {
    disconnect()
    state.reconnectAttempts = 0
    connect().catch(error => {
      console.error('重连失败:', error)
    })
  }
  
  /**
   * 发送消息
   */
  const send = (message: WebSocketMessage) => {
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      console.error('WebSocket未连接，无法发送消息')
      ElMessage.warning('网络连接断开，请稍后重试')
      return
    }
    
    try {
      ws.send(JSON.stringify(message))
    } catch (error) {
      console.error('发送WebSocket消息失败:', error)
      ElMessage.error('发送消息失败')
    }
  }
  
  /**
   * 添加消息处理器
   */
  const onMessage = (handler: MessageHandler) => {
    messageHandlers.push(handler)
    
    // 返回移除函数
    return () => {
      const index = messageHandlers.indexOf(handler)
      if (index > -1) {
        messageHandlers.splice(index, 1)
      }
    }
  }
  
  /**
   * 添加连接处理器
   */
  const onConnect = (handler: () => void) => {
    connectHandlers.push(handler)
    
    // 返回移除函数
    return () => {
      const index = connectHandlers.indexOf(handler)
      if (index > -1) {
        connectHandlers.splice(index, 1)
      }
    }
  }
  
  /**
   * 添加断开连接处理器
   */
  const onDisconnect = (handler: () => void) => {
    disconnectHandlers.push(handler)
    
    // 返回移除函数
    return () => {
      const index = disconnectHandlers.indexOf(handler)
      if (index > -1) {
        disconnectHandlers.splice(index, 1)
      }
    }
  }
  
  /**
   * 添加错误处理器
   */
  const onError = (handler: (error: Event) => void) => {
    errorHandlers.push(handler)
    
    // 返回移除函数
    return () => {
      const index = errorHandlers.indexOf(handler)
      if (index > -1) {
        errorHandlers.splice(index, 1)
      }
    }
  }
  
  /**
   * 获取连接状态
   */
  const getStatus = (): WebSocketStatus => state.status
  
  /**
   * 检查是否已连接
   */
  const isConnected = (): boolean => state.connected
  
  // 清理函数
  const cleanup = () => {
    disconnect()
    messageHandlers.length = 0
    connectHandlers.length = 0
    disconnectHandlers.length = 0
    errorHandlers.length = 0
  }
  
  // 返回管理器实例
  const manager: WebSocketManager & { state: WebSocketState; cleanup: () => void } = {
    // 连接管理
    connect,
    disconnect,
    reconnect,
    
    // 消息发送
    send,
    sendHeartbeat,
    
    // 事件监听
    onMessage,
    onConnect,
    onDisconnect,
    onError,
    
    // 状态获取
    getStatus,
    isConnected,
    
    // 内部状态（用于调试和状态显示）
    state,
    
    // 清理函数
    cleanup
  }
  
  return manager
}

/**
 * 获取全局WebSocket实例（单例模式）
 */
export function useWebSocket(config?: Partial<WebSocketConfig>) {
  if (!globalWebSocketInstance) {
    globalWebSocketInstance = createWebSocketManager(config)
  }
  
  // 组件卸载时不清理全局实例，因为其他组件可能还在使用
  // 但会清理当前组件的事件监听器
  onUnmounted(() => {
    // 这里可以添加当前组件特定的清理逻辑
  })
  
  return globalWebSocketInstance
}

/**
 * 清理全局WebSocket实例（应用关闭时调用）
 */
export function cleanupGlobalWebSocket() {
  if (globalWebSocketInstance) {
    globalWebSocketInstance.cleanup()
    globalWebSocketInstance = null
  }
}

/**
 * 计算属性：连接状态信息
 */
export function useWebSocketStatus() {
  const ws = useWebSocket()
  
  const statusText = computed(() => {
    switch (ws.state.status) {
      case WebSocketStatus.CONNECTING:
        return '连接中...'
      case WebSocketStatus.CONNECTED:
        return '已连接'
      case WebSocketStatus.DISCONNECTED:
        return '已断开'
      case WebSocketStatus.RECONNECTING:
        return `重连中... (${ws.state.reconnectAttempts})`
      case WebSocketStatus.ERROR:
        return '连接错误'
      default:
        return '未知状态'
    }
  })
  
  const statusClass = computed(() => {
    switch (ws.state.status) {
      case WebSocketStatus.CONNECTED:
        return 'success'
      case WebSocketStatus.CONNECTING:
      case WebSocketStatus.RECONNECTING:
        return 'warning'
      case WebSocketStatus.ERROR:
      case WebSocketStatus.DISCONNECTED:
        return 'danger'
      default:
        return 'info'
    }
  })
  
  const connectionQuality = computed(() => {
    const now = Date.now()
    const heartbeatAge = now - ws.state.lastHeartbeat
    const messageAge = now - ws.state.lastMessage
    
    if (!ws.state.connected) return 'offline'
    if (heartbeatAge > 60000 || messageAge > 60000) return 'poor'
    if (heartbeatAge > 30000 || messageAge > 30000) return 'fair'
    return 'good'
  })
  
  return {
    status: computed(() => ws.state.status),
    connected: computed(() => ws.state.connected),
    reconnectAttempts: computed(() => ws.state.reconnectAttempts),
    statusText,
    statusClass,
    connectionQuality
  }
}