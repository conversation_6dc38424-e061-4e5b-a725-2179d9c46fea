import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { LoginForm, RegisterForm, User } from '@/types/auth'
import { ElMessage } from 'element-plus'

// Mock用户数据
const mockUser: User = {
  id: 1,
  username: 'admin',
  nickname: '系统管理员',
  avatar: '',
  role_id: 1,
  role_name: '超级管理员',
  status: 1,
  created_at: '2024-01-01'
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)

  // 模拟登录
  const login = async (form: LoginForm) => {
    try {
      loading.value = true
      
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 简单的模拟验证
      if (form.username === 'admin' && form.password === '123456') {
        const mockToken = 'mock_token_' + Date.now()
        token.value = mockToken
        user.value = mockUser
        localStorage.setItem('token', token.value)
        ElMessage.success('登录成功')
        return true
      } else {
        ElMessage.error('用户名或密码错误（请使用 admin/123456）')
        return false
      }
    } catch (error) {
      ElMessage.error('登录失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 模拟注册
  const register = async (form: RegisterForm) => {
    try {
      loading.value = true
      
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      ElMessage.success('注册成功，请登录')
      return true
    } catch (error) {
      ElMessage.error('注册失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = () => {
    token.value = ''
    user.value = null
    localStorage.removeItem('token')
    ElMessage.success('已退出登录')
  }

  // 初始化用户信息（如果有token）
  const initUserInfo = () => {
    if (token.value) {
      user.value = mockUser
    }
  }

  return {
    token,
    user,
    loading,
    isAuthenticated,
    login,
    register, 
    logout,
    initUserInfo
  }
})