<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <!-- <div class="auth-logo">🏢</div> -->
        <h1 class="auth-title">企业注册</h1>
        <!-- <p class="auth-subtitle">开启您的数字化管理之旅</p> -->
      </div>
      
      <form @submit.prevent="handleRegister" class="auth-form">
        <div class="form-group">
          <label class="form-label">企业名称</label>
          <input 
            type="text" 
            class="form-input" 
            placeholder="请输入企业名称"
            v-model="registerForm.companyName"
          />
        </div>
        
        <!-- <div class="form-group">
          <label class="form-label">联系人姓名</label>
          <input 
            type="text" 
            class="form-input" 
            placeholder="请输入联系人姓名"
            v-model="registerForm.contactName"
          />
        </div> -->
        
        <div class="form-group">
          <label class="form-label">手机号码</label>
          <input 
            type="tel" 
            class="form-input" 
            placeholder="请输入手机号码"
            v-model="registerForm.phone"
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">短信验证码</label>
          <div class="captcha-container">
            <input 
              type="text" 
              class="form-input captcha-input" 
              placeholder="请输入短信验证码"
              v-model="registerForm.smsCode"
            />
            <button 
              type="button" 
              class="captcha-btn" 
              :disabled="smsCountdown > 0"
              @click="sendSmsCode"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '获取验证码' }}
            </button>
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">登录密码</label>
          <div class="password-container">
            <input 
              :type="showPassword ? 'text' : 'password'" 
              class="form-input" 
              placeholder="请设置登录密码"
              v-model="registerForm.password"
            />
            <button 
              type="button" 
              class="password-toggle" 
              @click="togglePassword"
            >
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
          <!-- <div class="password-strength" v-if="registerForm.password">
            <div 
              class="password-strength-bar" 
              :class="passwordStrength.level"
            ></div>
            <div class="password-strength-text">
              密码强度：{{ passwordStrength.text }}
            </div>
          </div> -->
        </div>
        
        <div class="form-group">
          <label class="form-label">确认密码</label>
          <div class="password-container">
            <input 
              :type="showConfirmPassword ? 'text' : 'password'" 
              class="form-input" 
              placeholder="请确认登录密码"
              v-model="registerForm.confirmPassword"
            />
            <button 
              type="button" 
              class="password-toggle" 
              @click="toggleConfirmPassword"
            >
              {{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
        </div>
        
        <div class="agreement">
          <label class="checkbox-label">
            <input type="checkbox" v-model="registerForm.agreement">
            我已阅读并同意
            <a href="#" @click.prevent="viewAgreement">《用户协议》</a>
            和
            <a href="#" @click.prevent="viewPrivacy">《隐私政策》</a>
          </label>
        </div>
        
        <button type="submit" class="submit-btn" :disabled="loading">
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? '注册中...' : '立即注册' }}
        </button>
        
        <div class="auth-switch">
          <span class="auth-switch-text">已有账户？</span>
          <router-link to="/login" class="auth-switch-link">立即登录</router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

const registerForm = ref({
  companyName: '',
  contactName: '',
  phone: '',
  smsCode: '',
  password: '',
  confirmPassword: '',
  agreement: false
})

const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const smsCountdown = ref(0)

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

// 密码强度检测
const passwordStrength = computed(() => {
  const password = registerForm.value.password.trim()
  if (!password) return { level: '', text: '' }
  
  let score = 0
  if (password.length >= 8) score++
  if (/[a-z]/.test(password)) score++
  if (/[A-Z]/.test(password)) score++
  if (/[0-9]/.test(password)) score++
  if (/[^a-zA-Z0-9]/.test(password)) score++
  
  if (score <= 2) {
    return { level: 'weak', text: '弱' }
  } else if (score <= 3) {
    return { level: 'medium', text: '中' }
  } else {
    return { level: 'strong', text: '强' }
  }
})

// 发送短信验证码
const sendSmsCode = () => {
  if (!registerForm.value.phone.trim().trim()) {
    ElMessage.warning('请先输入手机号码')
    return
  }
  
  if (!/^1[3-9]\d{9}$/.test(registerForm.value.phone.trim())) {
    ElMessage.error('请输入正确的手机号码')
    return
  }
  
  // 模拟发送短信验证码
  smsCountdown.value = 60
  ElMessage.success('验证码发送成功')
  
  const timer = setInterval(() => {
    smsCountdown.value--
    if (smsCountdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

const viewAgreement = () => {
  ElMessage.info('用户协议页面开发中...')
}

const viewPrivacy = () => {
  ElMessage.info('隐私政策页面开发中...')
}

const handleRegister = async () => {
  // 基本验证
  if (!registerForm.value.companyName.trim()) {
    ElMessage.warning('请输入企业名称')
    return
  }
  
  /* if (!registerForm.value.contactName.trim()) {
    ElMessage.warning('请输入联系人姓名')
    return
  } */
  
  if (!registerForm.value.phone.trim() || !/^1[3-9]\d{9}$/.test(registerForm.value.phone.trim())) {
    ElMessage.warning('请输入正确的手机号码')
    return
  }
  
  if (!registerForm.value.smsCode.trim()) {
    ElMessage.warning('请输入短信验证码')
    return
  }
  
  if (!registerForm.value.password.trim() || registerForm.value.password.trim().length < 6) {
    ElMessage.warning('请输入至少6位密码')
    return
  }
  
  if (registerForm.value.password.trim() !== registerForm.value.confirmPassword.trim()) {
    ElMessage.warning('两次输入的密码不一致')
    return
  }
  
  if (!registerForm.value.agreement) {
    ElMessage.warning('请阅读并同意用户协议和隐私政策')
    return
  }
  
  loading.value = true
  
  try {
    // 模拟注册API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('注册成功！请登录')
    router.push('/login')
  } catch (error) {
    ElMessage.error('注册失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0D1B2A 0%, #1B365D 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(ellipse at 25% 20%, rgba(232, 184, 109, 0.08) 0%, transparent 60%),
    radial-gradient(ellipse at 75% 80%, rgba(199, 210, 221, 0.12) 0%, transparent 60%),
    radial-gradient(ellipse at 50% 50%, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(0.5deg); }
  66% { transform: translate(-20px, 20px) rotate(-0.5deg); }
}

.auth-card {
  background: rgba(255, 255, 255, 0.92);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 24px;
  box-shadow: 
    0 8px 32px rgba(13, 27, 42, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  width: 100%;
  max-width: 520px;
  padding: 48px 40px;
  position: relative;
  z-index: 5;
  animation: cardSlideUp 0.8s ease-out;
}

@keyframes cardSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;
}

.auth-logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #E8B86D 0%, #B8C5D1 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  box-shadow: 0 4px 16px rgba(13, 27, 42, 0.15);
  font-size: 36px;
  color: #0D1B2A;
  font-weight: bold;
}

.auth-title {
  font-size: 28px;
  font-weight: 600;
  color: #0D1B2A;
  margin-bottom: 8px;
  line-height: 1.2;
}

.auth-subtitle {
  font-size: 16px;
  color: #778DA9;
  font-weight: 400;
}

.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 15px;
  font-weight: 500;
  color: #415A77;
}

.form-input {
  width: 100%;
  height: 48px;
  border: 2px solid rgba(65, 90, 119, 0.1);
  border-radius: 12px;
  padding: 0 16px;
  font-size: 16px;
  color: #0D1B2A;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #415A77;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 4px rgba(65, 90, 119, 0.1);
}

.form-input::placeholder {
  color: #B8C5D1;
}

/* 密码容器 */
.password-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #778DA9;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
}

.password-toggle:hover {
  color: #415A77;
}

/* 验证码容器 */
.captcha-container {
  display: flex;
  gap: 12px;
}

.captcha-input {
  flex: 1;
}

.captcha-btn {
  width: 120px;
  height: 48px;
  background: linear-gradient(135deg, #E8B86D 0%, #B8C5D1 100%);
  border: none;
  border-radius: 12px;
  color: #0D1B2A;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.captcha-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 27, 42, 0.15);
}

.captcha-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 8px;
  height: 4px;
  background: rgba(65, 90, 119, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.password-strength-bar {
  height: 100%;
  width: 0%;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.password-strength-bar.weak {
  width: 33%;
  background: #f56565;
}

.password-strength-bar.medium {
  width: 66%;
  background: #ed8936;
}

.password-strength-bar.strong {
  width: 100%;
  background: #48bb78;
}

.password-strength-text {
  font-size: 12px;
  margin-top: 4px;
  color: #B8C5D1;
}

/* 协议同意 */
.agreement {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  font-size: 14px;
  color: #415A77;
  line-height: 1.5;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #415A77;
  cursor: pointer;
  line-height: 1.5;
}

.checkbox-label input {
  margin-top: 2px;
  flex-shrink: 0;
}

.agreement a {
  color: #415A77;
  text-decoration: none;
  margin: 0 4px;
}

.agreement a:hover {
  text-decoration: underline;
}

.submit-btn {
  width: 100%;
  height: 52px;
  background: linear-gradient(135deg, #1B365D 0%, #0D1B2A 100%);
  border: none;
  border-radius: 14px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(13, 27, 42, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(13, 27, 42, 0.3);
}

.submit-btn:active {
  transform: translateY(0);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 页面切换链接样式 */
.auth-switch {
  text-align: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid rgba(65, 90, 119, 0.1);
}

.auth-switch-text {
  color: #778DA9;
  font-size: 14px;
  margin-right: 8px;
}

.auth-switch-link {
  color: #415A77;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.auth-switch-link:hover {
  color: #0D1B2A;
  text-decoration: underline;
}

/* 加载状态 */
.submit-btn.loading {
  pointer-events: none;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-card {
    padding: 32px 24px;
    margin: 16px;
  }

  .auth-title {
    font-size: 24px;
  }

  .auth-subtitle {
    font-size: 14px;
  }
  
  .captcha-container {
    flex-direction: column;
  }
  
  .captcha-btn {
    width: 100%;
  }
}
</style>