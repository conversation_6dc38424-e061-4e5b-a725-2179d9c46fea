<?php

namespace app\company\controller;

use app\model\CsAdmin;
use app\model\CsCompany;
use app\model\CsCompanyAdmin;
use app\model\CsSystem;
use app\service\Menu;
use support\Redis;
use support\Request;
use support\Response;
use Webman\Event\Event;

class LoginController
{
    use Menu;

    protected $noNeedLogin = ['login'];

    public function getLoginPage(Request $request): Response
    {
        $system = CsSystem::find(3);
        $backData = [
            'software_version' => $system->software_version,
            'serial_number' => $system->serial_number,
            'copyright_info' => $system->copyright_info,
            'software_name' => $system->software_name,
            'login_background' => get_image_url($system->login_background),
        ];
        return success($backData);
    }

    /**
     * 注册
     * @param Request $request
     * @return Response
     */
    public function register(Request $request): Response
    {
        $mobile = $request->post('mobile','');
        $vsCode = $request->post('vsCode','');
        $password = $request->post('password','');
        if (empty($mobile) || empty($vsCode) || empty($password)) {
            return fail('参数错误');
        }
        if (!is_mobile($mobile)) {
            return  fail('请输入正确的手机号码');
        }
        if (!is_alpha_dash($password)) {
            return fail('密码只能包含字母和数字及下划线');
        }
        if (strlen($password) < 5 || strlen($password) > 20) {
            return fail('密码长度需要介于5-20个字符');
        }
        // 验证验证码
        $code = Redis::get('sms:register:' . $mobile);
        if (empty($code) || $code != $vsCode) {
            return fail('验证码错误');
        }
        // 验证手机号是否已注册
        $company = CsCompany::where('mobile', $mobile)->first();
        if (!empty($company)) {
            return fail('手机号已注册');
        }
        $company = new CsCompany();
        $company->company_name = $mobile;
        $company->nickname = $mobile;
        $company->mobile = $mobile;
        $company->password = $password;
        $company->status = 1;
        $company->buy_at = date('Y-m-d');
        $company->expired_at = date('Y-m-d', time() + 10 * 86400);
        $company->product_version_id = 1;
        $company->shop_nums = 1;
        $company->buy_year = 0;
        $company->save();
        $eventParams = [
            'product_version_id' => 1,
            'company_id' => $company->id,
            'company_name' => $mobile,
            'mobile' => $mobile,
            'password' => $password,
            'status' => 1,
            'buy_year' => 0,
        ];
        Event::dispatch('company.add',$eventParams);
        $admin = CsCompanyAdmin::where(['username'=>$mobile])->first();
        $admin->login_ip = $request->getRealIp();
        $admin->login_time = date('Y-m-d H:i:s');
        $admin->save();
        $admin->avatar = get_image_url($admin->avatar);
        $admin->role_text = $admin?->csCompanyAdminRole?->role_name;
        $rolePermission = $admin?->csCompanyAdminRole?->role_permission;
        unset($admin->csCompanyAdminRole);
        // 用户身份直接存入redis
        $token = md5('company' . $admin->id . $admin->username . $admin->login_time);
        Redis::setEx('company:' . $token, config('app.token_expire_time'), $admin->id . '-' . $admin->role_id . '-' . $admin->company_id);
        $admin->token = $token;
        $admin->menu = $this->getMenuList(1, $rolePermission);
        return success($admin);
        return success();
    }

    public function login(Request $request): Response
    {
        $username = $request->post('username','');
        $password = $request->post('password','');
        if (empty($username) || empty($password)) {
            return fail('参数错误');
        }
        $admin = CsCompanyAdmin::where(['username'=>$username])->first();
        if (empty($admin)) {
            return fail('用户不存在');
        }
        if (md5($admin->password) != $password) {
            return fail('账号与密码不匹配');
        }
        if ($admin->status == 0) {
            return fail('该账号已经被冻结，请联系管理员');
        }
        // 判断公司是否过期
        if ($admin->csCompany->expired_at < date('Y-m-d')) {
            return fail('公司授权期限已过，请联系管理员');
        }
        $admin->login_ip = $request->getRealIp();
        $admin->login_time = date('Y-m-d H:i:s');
        $admin->save();
        $admin->avatar = get_image_url($admin->avatar);
        $admin->role_text = $admin?->csCompanyAdminRole?->role_name;
        $rolePermission = $admin?->csCompanyAdminRole?->role_permission;
        unset($admin->csCompanyAdminRole);
        // 用户身份直接存入redis
        $token = md5('company' . $admin->id . $admin->username . $admin->login_time);
        Redis::setEx('company:' . $token, config('app.token_expire_time'), $admin->id . '-' . $admin->role_id . '-' . $admin->company_id);
        $admin->token = $token;
        $admin->menu = $this->getMenuList(1, $rolePermission);
        return success($admin);
    }

    /**
     * 修改密码
     * @param Request $request
     * @return Response
     */
    public function savePwd(Request $request): Response
    {
        $mobile = $request->post('mobile','');
        $vsCode = $request->post('vs_code','');
        $password = $request->post('password','');
        $conPassword = $request->post('con_password','');
        if (empty($mobile) || empty($vsCode) || empty($password) || empty($conPassword)) {
            return fail('参数为空');
        }
        if ($password != $conPassword) {
            return  fail('两次输入的密码不一致');
        }
        if (strlen($password) < 5 || strlen($password) > 20) {
            return fail('密码长度应该介于5-20字符之间');
        }
        if (!preg_match('/^[A-Za-z0-9\-\_]+$/',$password)) {
            return fail('密码只能包含字母和数字及下划线');
        }
        $admin = CsCompanyAdmin::where(['username' => $mobile])->first();
        if (empty($admin)) {
            return fail('用户不存在');
        }
        if ($admin->status == 0) {
            return fail('该账号已经被冻结，请联系管理员');
        }
        $admin->password = $password;
        $admin->save();
        return success();
    }

}
