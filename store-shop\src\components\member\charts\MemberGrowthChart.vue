<template>
  <div class="member-growth-chart">
    <!-- 图表加载状态 -->
    <div v-if="isLoading" class="chart-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>图表加载中...</span>
    </div>
    
    <!-- 无数据状态 -->
    <div v-else-if="!hasData" class="chart-empty">
      <el-icon><Warning /></el-icon>
      <span>暂无数据</span>
    </div>

    <!-- ECharts 图表 -->
    <v-chart
      v-else
      ref="chartRef"
      :option="chartOption"
      :style="{ height: typeof height === 'number' ? height + 'px' : height }"
      autoresize
      @click="handleClick"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { Loading, Warning } from '@element-plus/icons-vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必要的组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent
])

interface Props {
  data: any[]
  type: 'register' | 'active'
  height?: number | string
}

const props = withDefaults(defineProps<Props>(), {
  height: 300
})

const emit = defineEmits<{
  click: [params: any]
}>()

const chartRef = ref()
const isLoading = ref(true)

// 监听数据变化，控制加载状态
watch(() => props.data, (newData) => {
  if (newData && newData.length > 0) {
    nextTick(() => {
      setTimeout(() => {
        isLoading.value = false
      }, 300)
    })
  } else {
    isLoading.value = true
  }
}, { immediate: true })

// 检查是否有数据
const hasData = computed(() => {
  return props.data && props.data.length > 0
})

// 处理图表数据
const processedData = computed(() => {
  if (!hasData.value) return { dates: [], values: [] }
  
  // 模拟数据处理逻辑
  const dates = props.data.map(item => item.date || item.x)
  const values = props.data.map(item => {
    if (props.type === 'register') {
      return item.register_count || item.registerCount || item.value || 0
    } else {
      return item.active_count || item.activeCount || item.value || 0
    }
  })
  
  return { dates, values }
})

// ECharts配置选项
const chartOption = computed(() => {
  const { dates, values } = processedData.value
  
  const title = props.type === 'register' ? '会员注册趋势' : '会员活跃趋势'
  const color = props.type === 'register' ? '#5470c6' : '#91cc75'
  
  return {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: (params: any) => {
        const param = params[0]
        const date = param.axisValue
        const value = param.value
        const label = props.type === 'register' ? '新增会员' : '活跃会员'
        
        return `
          <div style="margin: 0px 0 0; line-height:1;">
            <div style="font-size:14px; color:#666; font-weight:400; line-height:1;">
              ${date}
            </div>
            <div style="margin: 10px 0 0; line-height:1;">
              <div style="margin: 0px 0 0; line-height:1;">
                <span style="display:inline-block; margin-right:4px; border-radius:10px; width:10px; height:10px; background-color:${color};"></span>
                <span style="font-size:14px; color:#666; font-weight:400; margin-left:2px">
                  ${label}
                </span>
                <span style="float:right; margin-left:20px; font-size:14px; color:#666; font-weight:900">
                  ${value} 人
                </span>
                <div style="clear:both"></div>
              </div>
            </div>
          </div>
        `
      }
    },
    legend: {
      show: false
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates,
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      axisLabel: {
        color: '#606266'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#606266'
      },
      splitLine: {
        lineStyle: {
          color: '#F2F6FC'
        }
      }
    },
    series: [
      {
        name: title,
        type: 'line',
        smooth: true,
        showSymbol: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: color,
          width: 3
        },
        itemStyle: {
          color: color,
          borderColor: '#fff',
          borderWidth: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: color + '40' // 添加透明度
              },
              {
                offset: 1,
                color: color + '00'
              }
            ]
          }
        },
        data: values
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }
})

// 处理点击事件
const handleClick = (params: any) => {
  emit('click', params)
}
</script>

<style scoped lang="scss">
.member-growth-chart {
  position: relative;
  
  .chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--el-text-color-secondary);
    z-index: 10;
    
    .el-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }
    
    span {
      font-size: 14px;
    }
  }

  .chart-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--el-text-color-secondary);
    
    .el-icon {
      font-size: 48px;
      margin-bottom: 8px;
      color: #C0C4CC;
    }
    
    span {
      font-size: 14px;
    }
  }
}
</style>