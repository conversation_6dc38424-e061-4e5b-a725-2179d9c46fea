<template>
  <div class="app-header">
    <div class="header-left">
      <div class="logo" @click="goToHome">
          <img src="../../assets/images/logo_text.png" alt="">
      </div>
    </div>
    <div class="header-right">
        <div class="store_info">
            <div class="title-with-switch">
              <b>{{ currentModeTitle }}</b>
              <!-- 模式切换按钮 -->
              <div class="mode-switch-btn" @click="toggleMode" :title="'切换到' + nextMode.title">
                <el-icon class="switch-icon">
                  <component :is="currentModeInfo.icon" />
                </el-icon>
                <span>{{ currentModeInfo.title }}</span>
              </div>
            </div>
        </div>
        <div class="store_nav">
            <div class="nav_scan">
                <i></i>
                <span>扫码收款</span>
            </div>
            <!-- 消息通知 -->
            <div class="store_nav_li message-badge" @click="showMessageCenter">
              <MessageBadge />
            </div>
            <el-dropdown class="store_nav_li" trigger="click">
                <div class="store_nav_li_text">
                    <el-icon><Briefcase /></el-icon>
                    <span>工具</span>
                    <el-icon class="arrow_down"><CaretBottom /></el-icon>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item>记账本</el-dropdown-item>
                        <el-dropdown-item>定时提醒</el-dropdown-item>
                        <el-dropdown-item>数据导入</el-dropdown-item>
                        <el-dropdown-item>数据导出</el-dropdown-item>
                        <el-dropdown-item>寄存管理</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
            <el-dropdown class="store_nav_li" trigger="click">
                <div class="store_nav_li_text">
<!--                    <el-icon><Avatar /></el-icon>-->
                    <el-avatar :size="32" :src="userInfo.avatar || defaultAvatar" />
                    <span>{{ userInfo.nickname || '超级管理员' }}</span>
                    <el-icon class="arrow_down"><CaretBottom /></el-icon>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item @click="handleCommand('profile')">账号信息</el-dropdown-item>
                        <el-dropdown-item @click="handleCommand('settings')">系统设置</el-dropdown-item>
                        <el-dropdown-item divided @click="handleCommand('logout')">退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
            <el-dropdown class="store_nav_li last">
                <div class="store_nav_li_text">
                    <el-icon><Shop /></el-icon>
                    <span>心路远茶叶直营体验店</span>
                    <el-icon class="arrow_down"><CaretBottom /></el-icon>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item>心路远茶叶直营体验店</el-dropdown-item>
                        <el-dropdown-item>心路远茶叶直营店</el-dropdown-item>
                        <el-dropdown-item>心路远茶叶闽江店</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
            
            <el-tooltip content="全屏" placement="bottom">
                <el-icon class="header-icon" @click="toggleFullscreen">
                    <FullScreen />
                </el-icon>
            </el-tooltip>
        </div>
    </div>

    <!-- 个人中心对话框 -->
    <ProfileDialog
      v-model:visible="profileDialogVisible"
      @user-updated="handleUserInfoUpdated"
    />

    <!-- 消息中心对话框 -->
    <MessageCenterDialog v-model="messageCenterVisible" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import {
    FullScreen,
    BellFilled,
    Briefcase, CaretBottom, Shop,
    Grid, ShoppingCart
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import ProfileDialog from '../profile/ProfileDialog.vue'
import MessageBadge from '@/components/realtime/MessageBadge.vue'
import MessageCenterDialog from '@/components/message/MessageCenterDialog.vue'

const router = useRouter()
const route = useRoute()
const defaultAvatar = 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'

// 个人中心对话框显示状态
const profileDialogVisible = ref(false)

// 消息中心对话框显示状态
const messageCenterVisible = ref(false)

// 模式配置
const modes = [
  {
    path: '/table/business',
    title: '桌台营业',
    fullTitle: '收银台 - 桌台营业',
    icon: Grid
  },
  {
    path: '/retail',
    title: '商品零售',
    fullTitle: '收银台 - 商品零售',
    icon: ShoppingCart
  }
]

// 当前模式
const currentMode = computed(() => {
  return route.path
})

// 当前模式标题
const currentModeTitle = computed(() => {
  const mode = modes.find(m => m.path === currentMode.value)
  return mode?.fullTitle || '收银台 - 桌台管理'
})

// 当前模式信息
const currentModeInfo = computed(() => {
  return modes.find(m => m.path === currentMode.value) || modes[0]
})

// 下一个模式
const nextMode = computed(() => {
  const currentIndex = modes.findIndex(m => m.path === currentMode.value)
  const nextIndex = (currentIndex + 1) % modes.length
  return modes[nextIndex]
})

// 用户信息
interface UserInfo {
  nickname?: string;
  avatar?: string;
  username?: string;
  [key: string]: any;
}

const userInfo = ref<UserInfo>({})


// 点击 logo 回到首页
const goToHome = () => {
  router.push('/table/business')
}

// 显示消息中心对话框
const showMessageCenter = () => {
  messageCenterVisible.value = true
}


// 切换到下一个模式
const toggleMode = () => {
  router.push(nextMode.value.path)
}

// 获取用户信息
const getUserInfo = () => {
  try {
    const userInfoStr = localStorage.getItem('user_info')
    if (userInfoStr) {
      const userData = JSON.parse(userInfoStr)
      // 处理用户信息格式，适配不同的数据结构
      if (userData.user_info) {
        userInfo.value = userData.user_info
      } else {
        userInfo.value = userData
      }

      // 确保头像路径正确
      if (userInfo.value.avatar && !userInfo.value.avatar.startsWith('http')) {
        userInfo.value.avatar = userInfo.value.avatar
      }
    }
  } catch (error) {
    console.error('获取用户信息失败', error)
  }
}

onMounted(() => {
  getUserInfo()
})

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }
}

const handleCommand = async (command: string) => {
  if (command === 'logout') {
    // 断开WebSocket连接
    try {
      const { cleanupGlobalWebSocket } = await import('@/composables/useWebSocket')
      cleanupGlobalWebSocket()
    } catch (error) {
      console.error('断开WebSocket连接失败:', error)
    }

    const { useSavedOrders } = await import('@/hooks/useSavedOrders')
    const { clearAllData } = useSavedOrders()
    clearAllData()
    
    localStorage.removeItem('token')
    localStorage.removeItem('user_info')
    localStorage.removeItem('scanHistory')
    localStorage.removeItem('saved_orders')
    localStorage.removeItem('realtime_messages')
    ElMessage.success('退出登录成功')
    router.push('/login')
  } else if (command === 'profile') {
    // 显示个人中心对话框
    profileDialogVisible.value = true
  } else if (command === 'settings') {
    // 跳转到系统设置
    ElMessage.info('系统设置功能开发中')
  } else if (command === 'messages') {
    // 消息功能
    ElMessage.info('消息功能开发中')
  }
}

// 处理用户信息更新
const handleUserInfoUpdated = (updatedInfo: any) => {
      //console.log('用户信息已更新:', updatedInfo)

  // 直接更新本地数据
  if (updatedInfo) {
    // 如果更新的信息嵌套在user_info中，则取user_info
    const info = updatedInfo.user_info || updatedInfo

    // 更新当前组件状态
    if (info.nickname) {
      userInfo.value.nickname = info.nickname
    }

    if (info.avatar) {
      userInfo.value.avatar = info.avatar
    }
  }

  // 重新获取用户信息以确保数据同步
  getUserInfo()

  // 确保对话框关闭
  profileDialogVisible.value = false
}
</script>

<style scoped>
.app-header{display: flex;align-items: center;justify-content: space-between;height: 61px;
  background-color: #fff;color: #333;position: fixed;top: 0;left: 0;right: 0;z-index: 999;}
.header-left{display: flex;align-items:center;width:160px;height:61px;}
.logo{width:160px;height:61px;padding:5px 10px 6px;overflow:hidden;display:flex;justify-content: center;background:#2c9a4d;cursor:pointer;transition: background-color 0.3s;}
.logo:hover{background:#1e7a3a;}
.logo img{height:50px;width:auto;display:block;}
.header-right{display:flex;align-items: center;flex:1;padding:0 15px;height:61px;justify-content:space-between;
    border-bottom: 1px solid rgba(53, 58, 54, .3);border-top:3px solid #2c9a4d;}
.header-right .store_info{min-width:10px;width:auto;height:57px;display:inline-flex;align-items:center;justify-content:center;}
.store_info{width:auto;height:57px;display:inline-flex;padding:0 15px 3px 0;align-items:center;}
.store_info b{font-size:18px;color:#333;line-height:20px;max-width:360px;}

/* 标题与切换按钮横向布局 */
.title-with-switch{
  display:flex;
  align-items:center;
  gap:12px;
}

/* 模式切换按钮样式 */
.mode-switch-btn{
  display:flex;
  align-items:center;
  gap:6px;
  padding:6px 12px;
  font-size:13px;
  cursor:pointer;
  transition: all 0.3s;
  border-radius:4px;
  background:#d4751a;
  color:#fff;
  border:1px solid #d4751a;
  font-weight:500;
  min-height:28px;
  box-shadow: 0 2px 4px rgba(212, 117, 26, 0.2);
}
.mode-switch-btn:hover{
  background:#c96b18;
  border-color:#c96b18;
  box-shadow: 0 2px 6px rgba(212, 117, 26, 0.3);
}
.switch-icon{
  font-size:14px;
}
.arrow-icon{
  font-size:12px;
  opacity:0.8;
}

.store_info .switch{width:auto;height:54px;margin-left:10px;overflow:hidden;white-space:nowrap;cursor:pointer;}
.store_info .switch span{font-size:14px;color:#999;line-height:60px;display:inline-block;margin-left:5px;}
.store_info .switch .el-icon{font-size:18px;vertical-align:top;color:#999;margin-top:21px;}
.store_info .switch:hover span{color:#c4272e;}
.store_info .switch:hover .el-icon{color:#c4272e;}
.header-right .store_nav{width:auto;height:57px;display:inline-flex;padding:0 15px;align-items:center;}
.nav_scan{width:130px;height:44px;display:inline-flex;align-items:center;justify-content:center;cursor:pointer;
    background:linear-gradient(to right,rgba(44,154,77,0.8),rgba(0,97,29,0.8));border-radius:4px;}
.nav_scan:hover{background:linear-gradient(to right,rgba(44,154,77,1),rgba(0,97,29,1));border-radius:4px;}
.nav_scan i{width:18px;height:18px;background:url(../../assets/images/scan_icon.png) center no-repeat;
    background-size:18px 18px;display:inline-flex;}
.nav_scan span{width:auto;font-size:14px;color:#fff;display:inline-flex;margin-left:10px;}
.store_nav_li{min-width:80px;padding:0 20px;height:24px;display:inline-flex;
    align-items:center;justify-content:center;cursor:pointer;border-right:1px dashed #2c9a4d;}
.store_nav_li_text{display:flex;align-items:center;}
.store_nav_li_text:focus-visible{outline:none;}
.store_nav_li .el-icon{font-size:22px;color:#333;display:inline-flex;}
.store_nav_li span{width:auto;font-size:14px;color:#333;display:inline-flex;margin-left:5px;align-items: center;}
.store_nav_li span.el-avatar{width:32px;height:32px;display:inline-flex;}
.store_nav_li:hover .el-icon{color:#c4272e;}
.store_nav_li:hover span{color:#c4272e;}
.store_nav_li .el-icon.arrow_down{font-size:14px;color:#333;display:inline-flex;margin-left:5px;transition:0.5s;}
.store_nav_li:hover .el-icon.arrow_down{color:#c4272e;}
:deep(.store_nav_li:hover .el-icon.arrow_down){transform: rotate(180deg);transition:0.5s;}
.store_nav_li.last{border-right:none;}
:deep(.tips_span .el-dialog__header){display:none;}
.tips_span span{font-size:18px;color:#000;padding:10px 0;display:block;}
:deep(.el-dropdown-menu__item){padding:7px 22px;color:#333;}
:deep(.el-dropdown-menu__item:not(.is-disabled):focus),
:deep(.el-dropdown-menu__item:not(.is-disabled):hover){background:#f6f7fb;color:#c4272e;}
:deep(.store_nav .el-icon){cursor:pointer;}
@media (min-width:992px) and (max-width:1200px){
    .app-header{height:51px;}
    .header-left{width:120px;height:51px;}
    .header-left .logo{height:51px;padding:5px 10px 6px;}
    .logo img{height:40px;}
    .header-right{height:51px;border-top:2px solid #2c9a4d;padding:0 10px;}
    .header-right .store_info{height:48px;padding:0 10px 2px 0;}
    .store_info b{font-size:15px;line-height:18px;max-width:200px;}
    .store_info .switch{height:46px;margin-left:10px;}
    .store_info .switch span{font-size:12px;line-height:56px;margin-left:4px;}
    .store_info .switch .el-icon{font-size:16px;margin-top:19px;}
    .header-right .store_nav{height:48px;padding:0 10px;}
    .nav_scan{width:100px;height:34px;box-sizing:border-box;}
    .nav_scan i{width:14px;height:14px;background:url(../../assets/images/scan_icon.png) center no-repeat;
        background-size:14px 14px;}
    .nav_scan span{font-size:12px;margin-left:6px;}
    .store_nav_li{min-width:60px;padding:0 15px;height:20px;}
    .store_nav_li .el-icon{font-size:18px;}
    .store_nav_li span{font-size:12px;margin-left:5px;max-width:120px;}
    .store_nav_li .el-icon.arrow_down{font-size:12px;margin-left:5px;}
    .tips_span span{font-size:16px;padding:0;}
    :deep(.el-dropdown-menu__item){padding:6px 20px;color:#333;}
}
@media (max-width:991px){
    .app-header{height:51px;}
    .header-left{width:120px;height:51px;}
    .header-left .logo{height:51px;padding:5px 10px 6px;}
    .logo img{height:40px;}
    .header-right{height:51px;border-top:2px solid #2c9a4d;padding:0 10px;justify-content:flex-end;}
    .header-right .store_info{display:none;}
    .header-right .store_nav{height:48px;padding:0;}
    .nav_scan{width:100px;height:34px;box-sizing:border-box;}
    .nav_scan i{width:14px;height:14px;background:url(../../assets/images/scan_icon.png) center no-repeat;
        background-size:14px 14px;}
    .nav_scan span{font-size:12px;margin-left:6px;}
    .store_nav_li{min-width:60px;padding:0 15px;height:20px;}
    .store_nav_li .el-icon{font-size:18px;}
    .store_nav_li span{font-size:12px;margin-left:5px;max-width:120px;}
    .store_nav_li .el-icon.arrow_down{font-size:12px;margin-left:5px;}
    .tips_span span{font-size:16px;padding:0;}
    :deep(.el-dropdown-menu__item){padding:6px 15px;color:#333;font-size:12px;}
}

/* 消息徽章样式 */
.message-badge {
    margin-right: 10px;
}

.message-badge :deep(.el-badge__content) {
    background-color: #f56c6c;
    border: 1px solid #fff;
}

.header-icon {
    font-size: 20px;
    color: #333;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s;
}

.header-icon:hover {
    background-color: #f5f7fa;
    color: #409EFF;
}
</style>
