<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权企业管理系统</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/echarts/dist/echarts.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        /* CSS变量定义 - 商务典雅蓝色主题系统 */
        :root {
            /* 主要蓝色系 - 深邃商务蓝 */
            --primary-deep-blue: #0D1B2A;
            --primary-business-blue: #1B365D;
            --primary-steel-blue: #415A77;
            
            /* 辅助蓝色系 - 钢铁蓝与淡雅蓝灰 */
            --secondary-steel-blue: #778DA9;
            --secondary-elegant-blue: #E0E1DD;
            --secondary-light-blue-gray: #F1F3F4;
            
            /* 点缀色系 - 精致金属色 */
            --accent-platinum: #C7D2DD;
            --accent-warm-silver: #B8C5D1;
            --accent-soft-gold: #E8B86D;
            
            /* 状态色系 */
            --success-color: #67C23A;
            --warning-color: #E6A23C;
            --danger-color: #F56C6C;
            --info-color: #909399;
            
            /* 文字颜色系统 */
            --text-primary: #0D1B2A;
            --text-secondary: #415A77;
            --text-light: #778DA9;
            --text-muted: #B8C5D1;
            
            /* 背景和效果 */
            --glass-background: rgba(255, 255, 255, 0.92);
            --glass-border: rgba(255, 255, 255, 0.8);
            --shadow-deep: rgba(13, 27, 42, 0.25);
            --shadow-medium: rgba(13, 27, 42, 0.15);
            --shadow-light: rgba(13, 27, 42, 0.08);
            
            /* 渐变定义 */
            --gradient-primary: linear-gradient(135deg, var(--primary-business-blue) 0%, var(--primary-deep-blue) 100%);
            --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent-soft-gold) 0%, var(--accent-warm-silver) 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: #f5f7fa;
            line-height: 1.6;
        }

        /* 系统容器 */
        .app-container {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        /* 侧边栏 */
        .sidebar {
            width: 260px;
            background: var(--gradient-primary);
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 100;
            box-shadow: 2px 0 8px var(--shadow-light);
        }

        .sidebar.collapsed {
            width: 64px;
        }

        /* Logo区域 */
        .logo-area {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            width: 48px;
            height: 48px;
            background: var(--gradient-accent);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 24px;
            color: var(--primary-deep-blue);
            font-weight: bold;
        }

        .logo-text {
            color: white;
            font-size: 18px;
            font-weight: 600;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .logo-text {
            opacity: 0;
        }

        /* 导航菜单 */
        .nav-menu {
            flex: 1;
            padding: 20px 0;
            overflow-y: auto;
        }

        .nav-item {
            position: relative;
            margin: 4px 16px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .nav-link:hover,
        .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            backdrop-filter: blur(10px);
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .nav-text {
            opacity: 0;
        }

        .nav-badge {
            margin-left: auto;
            background: var(--danger-color);
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 12px;
            min-width: 18px;
            text-align: center;
        }

        /* 子菜单样式 */
        .nav-submenu {
            margin-left: 20px;
            margin-top: 4px;
            border-left: 2px solid rgba(255, 255, 255, 0.1);
        }

        .nav-sub-link {
            padding: 8px 16px;
            font-size: 13px;
        }

        .nav-sub-link .nav-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }

        .nav-arrow-expanded {
            transform: rotate(90deg);
            transition: transform 0.3s ease;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 顶部栏 */
        .header {
            height: 60px;
            background: white;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: 0 1px 4px var(--shadow-light);
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .sidebar-toggle {
            width: 40px;
            height: 40px;
            border: none;
            background: none;
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: var(--secondary-light-blue-gray);
        }

        .breadcrumb {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--text-muted);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .header-notification {
            position: relative;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: none;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            transition: all 0.3s ease;
        }

        .header-notification:hover {
            background: var(--secondary-light-blue-gray);
        }

        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            background: var(--danger-color);
            border-radius: 50%;
        }

        .user-info {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .user-info:hover {
            background: var(--secondary-light-blue-gray);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--gradient-accent);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-deep-blue);
            font-weight: bold;
            margin-right: 8px;
        }

        .user-name {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }

        /* 内容区域 */
        .content-area {
            flex: 1;
            overflow: auto;
            background: #f5f7fa;
        }

        /* 认证页面样式 */
        .auth-container {
            min-height: 100vh;
            background: var(--gradient-primary);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow: hidden;
        }

        /* 动态背景装饰层 */
        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(ellipse at 25% 20%, rgba(232, 184, 109, 0.08) 0%, transparent 60%),
                radial-gradient(ellipse at 75% 80%, rgba(199, 210, 221, 0.12) 0%, transparent 60%),
                radial-gradient(ellipse at 50% 50%, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
            animation: backgroundFloat 20s ease-in-out infinite;
        }

        @keyframes backgroundFloat {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            33% { transform: translate(30px, -30px) rotate(0.5deg); }
            66% { transform: translate(-20px, 20px) rotate(-0.5deg); }
        }

        .auth-card {
            background: var(--glass-background);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: 
                0 8px 32px var(--shadow-deep),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
            width: 100%;
            max-width: 480px;
            padding: 48px 40px;
            position: relative;
            z-index: 5;
            animation: cardSlideUp 0.8s ease-out;
        }

        @keyframes cardSlideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .auth-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .auth-logo {
            width: 80px;
            height: 80px;
            background: var(--gradient-accent);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            box-shadow: 0 4px 16px var(--shadow-light);
            font-size: 36px;
            color: var(--primary-deep-blue);
            font-weight: bold;
        }

        .auth-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .auth-subtitle {
            font-size: 16px;
            color: var(--text-light);
            font-weight: 400;
        }

        /* 页面切换链接样式 */
        .auth-switch {
            text-align: center;
            margin-top: 24px;
            padding-top: 20px;
            border-top: 1px solid rgba(65, 90, 119, 0.1);
        }

        .auth-switch-text {
            color: var(--text-light);
            font-size: 14px;
            margin-right: 8px;
        }

        .auth-switch-link {
            color: var(--primary-steel-blue);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .auth-switch-link:hover {
            color: var(--primary-deep-blue);
            text-decoration: underline;
        }

        /* 表单容器 */
        .auth-form {
            position: relative;
        }

        .form-section {
            opacity: 0;
            transform: translateX(30px);
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            pointer-events: none;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            filter: blur(2px);
        }

        .form-section.active {
            opacity: 1;
            transform: translateX(0);
            position: relative;
            pointer-events: auto;
            filter: blur(0);
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 15px;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .form-input {
            width: 100%;
            height: 48px;
            border: 2px solid rgba(65, 90, 119, 0.1);
            border-radius: 12px;
            padding: 0 16px;
            font-size: 16px;
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-steel-blue);
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 0 0 4px rgba(65, 90, 119, 0.1);
        }

        .form-input::placeholder {
            color: var(--text-muted);
        }

        /* 密码输入框容器 */
        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            font-size: 16px;
            padding: 4px;
        }

        .password-toggle:hover {
            color: var(--text-secondary);
        }

        /* 验证码容器 */
        .captcha-container {
            display: flex;
            gap: 12px;
        }

        .captcha-input {
            flex: 1;
        }

        .captcha-btn {
            width: 120px;
            height: 48px;
            background: var(--gradient-accent);
            border: none;
            border-radius: 12px;
            color: var(--primary-deep-blue);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .captcha-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px var(--shadow-light);
        }

        .captcha-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 记住我选项 */
        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .remember-me input {
            margin-right: 8px;
        }

        .forgot-password {
            font-size: 14px;
            color: var(--primary-steel-blue);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: var(--primary-deep-blue);
            text-decoration: underline;
        }

        /* 协议同意 */
        .agreement {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        .agreement input {
            margin-right: 8px;
            margin-top: 2px;
        }

        .agreement a {
            color: var(--primary-steel-blue);
            text-decoration: none;
        }

        .agreement a:hover {
            text-decoration: underline;
        }

        .submit-btn {
            width: 100%;
            height: 52px;
            background: var(--gradient-primary);
            border: none;
            border-radius: 14px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px var(--shadow-medium);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px var(--shadow-medium);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .submit-btn:hover::before {
            left: 100%;
        }

        /* 加载状态 */
        .submit-btn.loading {
            pointer-events: none;
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        /* 错误提示样式 */
        .error-message {
            color: #f56565;
            font-size: 14px;
            margin-top: 6px;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .error-message.show {
            opacity: 1;
            transform: translateY(0);
        }

        .form-input.error {
            border-color: #f56565;
            background: rgba(245, 101, 101, 0.05);
        }

        /* 密码强度指示器 */
        .password-strength {
            margin-top: 8px;
            height: 4px;
            background: rgba(65, 90, 119, 0.1);
            border-radius: 2px;
            overflow: hidden;
        }

        .password-strength-bar {
            height: 100%;
            width: 0%;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .password-strength-bar.weak {
            width: 33%;
            background: #f56565;
        }

        .password-strength-bar.medium {
            width: 66%;
            background: #ed8936;
        }

        .password-strength-bar.strong {
            width: 100%;
            background: #48bb78;
        }

        .password-strength-text {
            font-size: 12px;
            margin-top: 4px;
            color: var(--text-muted);
        }

        /* 页面内容样式 */
        .page-container {
            padding: 24px;
            height: 100%;
            overflow-y: auto;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .page-description {
            color: var(--text-light);
            font-size: 14px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px var(--shadow-light);
            border: 1px solid #e4e7ed;
            margin-bottom: 24px;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 4px 16px var(--shadow-medium);
        }

        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-body {
            padding: 24px;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px var(--shadow-light);
            border: 1px solid #e4e7ed;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px var(--shadow-medium);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-icon.blue {
            background: var(--primary-steel-blue);
        }

        .stat-icon.green {
            background: var(--success-color);
        }

        .stat-icon.orange {
            background: var(--warning-color);
        }

        .stat-icon.red {
            background: var(--danger-color);
        }

        .stat-icon.purple {
            background: #722ED1;
        }

        .stat-icon.warning {
            background: var(--warning-color);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 12px;
        }

        .stat-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 8px;
        }

        .stat-secondary {
            font-size: 12px;
            color: var(--text-muted);
        }

        .stat-trend {
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .stat-trend.up {
            color: var(--success-color);
        }

        .stat-trend.up::before {
            content: '↗';
            font-size: 14px;
        }

        .stat-trend.down {
            color: var(--danger-color);
        }

        .stat-trend.down::before {
            content: '↘';
            font-size: 14px;
        }

        /* 状态指示器 */
        .stat-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
        }

        .stat-status.online {
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        .stat-status.offline {
            background: var(--danger-color);
        }

        /* 实时指示器 */
        .stat-live-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 1.5s infinite;
        }

        /* 徽章 */
        .stat-badge {
            background: var(--danger-color);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
            min-width: 16px;
            text-align: center;
        }

        /* 告警级别 */
        .stat-alert-level {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .stat-alert-level.high {
            background: var(--danger-color);
            animation: pulse 1s infinite;
        }

        .stat-alert-level.medium {
            background: var(--warning-color);
        }

        .stat-alert-level.low {
            background: var(--info-color);
        }

        /* 增长指示器 */
        .stat-growth-indicator {
            background: var(--success-color);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        /* 优先级徽章 */
        .stat-priority-badge {
            background: var(--danger-color);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
            min-width: 16px;
            text-align: center;
            animation: pulse 2s infinite;
        }

        /* 脉搏动画 */
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.1);
            }
        }


        /* 表格卡片 */
        .table-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px var(--shadow-light);
            border: 1px solid #e4e7ed;
            overflow: hidden;
            margin-bottom: 32px;
        }

        .table-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #fafbfc;
        }

        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .table-actions {
            display: flex;
            gap: 8px;
        }

        .refresh-btn {
            padding: 6px 12px;
            border: 1px solid #dcdfe6;
            background: white;
            color: var(--text-secondary);
            font-size: 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            border-color: var(--primary-steel-blue);
            color: var(--primary-steel-blue);
        }

        .table-body {
            max-height: 400px;
            overflow-y: auto;
        }

        /* 表格样式 */
        .table-container {
            background: white;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e4e7ed;
        }

        .table th {
            background: var(--secondary-light-blue-gray);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 13px;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .table td {
            color: var(--text-secondary);
            font-size: 13px;
        }

        .table tr:hover {
            background: rgba(65, 90, 119, 0.05);
        }

        .table tr.row-offline {
            opacity: 0.6;
        }

        /* 门店名称样式 */
        .shop-name {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .shop-name .name {
            font-weight: 500;
        }

        .shop-name .rank {
            background: var(--warning-color);
            color: white;
            font-size: 10px;
            padding: 2px 4px;
            border-radius: 4px;
            font-weight: 600;
        }

        /* 状态标识增强 */
        .status-tag {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-tag.online {
            background: rgba(103, 194, 58, 0.1);
            color: var(--success-color);
        }

        .status-tag.offline {
            background: rgba(245, 108, 108, 0.1);
            color: var(--danger-color);
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: currentColor;
        }

        /* 营业额单元格 */
        .revenue-cell {
            min-width: 120px;
        }

        .revenue-cell .amount {
            font-weight: 600;
            color: var(--text-primary);
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #f0f2f5;
            border-radius: 2px;
            margin-top: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--success-color), var(--primary-steel-blue));
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        /* 利润单元格 */
        .profit-cell .profit-amount {
            font-weight: 500;
        }

        .profit-cell .profit-rate {
            color: var(--text-muted);
            font-size: 11px;
            display: block;
            margin-top: 2px;
        }

        /* 库存状态 */
        .stock-status {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .stock-status.normal {
            background: rgba(103, 194, 58, 0.1);
            color: var(--success-color);
        }

        .stock-status.warning {
            background: rgba(230, 162, 60, 0.1);
            color: var(--warning-color);
        }

        .stock-status.low {
            background: rgba(245, 108, 108, 0.1);
            color: var(--danger-color);
        }

        /* 更新时间 */
        .update-time {
            color: var(--text-muted);
            font-size: 11px;
        }

        /* 操作按钮 */
        .action-buttons {
            display: flex;
            gap: 4px;
        }

        .action-btn {
            padding: 4px 8px;
            border: 1px solid #dcdfe6;
            background: white;
            color: var(--text-secondary);
            font-size: 11px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn.view:hover {
            border-color: var(--primary-steel-blue);
            color: var(--primary-steel-blue);
        }

        .action-btn.control:hover {
            border-color: var(--success-color);
            color: var(--success-color);
        }


        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-tag.online {
            background: rgba(103, 194, 58, 0.1);
            color: var(--success-color);
        }

        .status-tag.offline {
            background: rgba(245, 108, 108, 0.1);
            color: var(--danger-color);
        }

        .status-tag.warning {
            background: rgba(230, 162, 60, 0.1);
            color: var(--warning-color);
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-steel-blue);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-business-blue);
        }

        .btn-secondary {
            background: var(--secondary-elegant-blue);
            color: var(--text-secondary);
        }

        .btn-secondary:hover {
            background: var(--secondary-steel-blue);
            color: white;
        }

        /* 图表网格布局 */
        .charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }

        /* 图表卡片 */
        .chart-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px var(--shadow-light);
            border: 1px solid #e4e7ed;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .chart-card:hover {
            box-shadow: 0 4px 16px var(--shadow-medium);
        }

        .chart-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #fafbfc;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .chart-period {
            color: var(--text-light);
            font-size: 12px;
        }

        .chart-body {
            padding: 24px;
        }

        /* 时间选择器 */
        .time-selector {
            display: flex;
            gap: 4px;
            background: #f5f7fa;
            padding: 4px;
            border-radius: 8px;
        }

        .time-btn {
            padding: 6px 12px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            font-size: 12px;
            font-weight: 500;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .time-btn:hover {
            background: rgba(64, 158, 255, 0.1);
            color: var(--primary-steel-blue);
        }

        .time-btn.active {
            background: var(--primary-steel-blue);
            color: white;
        }

        /* 排序按钮 */
        .chart-sort {
            display: flex;
            gap: 8px;
        }

        .sort-btn {
            padding: 6px 12px;
            border: 1px solid #dcdfe6;
            background: white;
            color: var(--text-secondary);
            font-size: 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sort-btn:hover {
            border-color: var(--primary-steel-blue);
            color: var(--primary-steel-blue);
        }

        /* 图表容器 */
        .chart-container {
            height: 320px;
            position: relative;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -260px;
                transition: left 0.3s ease;
                z-index: 1000;
            }

            .sidebar.mobile-open {
                left: 0;
            }

            .main-content {
                margin-left: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .page-container {
                padding: 16px;
            }

            .auth-card {
                margin: 16px;
                padding: 32px 24px;
            }
        }

        /* 隐藏类 */
        .hidden {
            display: none !important;
        }

        /* 加载动画 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid var(--secondary-elegant-blue);
            border-top: 3px solid var(--primary-steel-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 门店管理相关样式 */
        .card-actions {
            display: flex;
            gap: 10px;
        }
        
        .row-inactive {
            opacity: 0.6;
            background-color: #f8f9fa;
        }
        
        .shop-name {
            display: flex;
            flex-direction: column;
        }
        
        .shop-name .name {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .shop-name .remark {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
        }
        
        .address-info {
            display: flex;
            flex-direction: column;
        }
        
        .address-info .address {
            margin-bottom: 2px;
        }
        
        .address-info .coordinates {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .status-tag.active {
            background-color: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .status-tag.inactive {
            background-color: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .business-hours .closed {
            color: var(--text-secondary);
            font-style: italic;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background-color: #138496;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-dialog {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .modal-lg {
            max-width: 800px;
        }
        
        .modal-xl {
            max-width: 1000px;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .modal-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-secondary);
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-close:hover {
            color: var(--text-primary);
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* 表单样式 */
        .shop-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            margin-bottom: 6px;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .required {
            color: #dc3545;
        }
        
        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-steel-blue);
            box-shadow: 0 0 0 2px rgba(52, 144, 220, 0.1);
        }
        
        .address-input-group {
            display: flex;
            gap: 8px;
        }
        
        .address-input-group .form-control {
            flex: 1;
        }

        /* 营业时间配置 */
        .business-hours-config {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 16px;
            background-color: #f8f9fa;
        }
        
        .day-config {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px;
            background: white;
            border-radius: 4px;
        }
        
        .day-config:last-child {
            margin-bottom: 0;
        }
        
        .day-label {
            min-width: 60px;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .time-inputs {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }
        
        .time-input {
            width: 100px;
        }
        
        .time-separator {
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            color: var(--text-secondary);
            cursor: pointer;
        }

        /* 门店详情 */
        .shop-detail {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .detail-section {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 16px;
        }
        
        .detail-section:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }
        
        .section-title {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .detail-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 8px;
        }
        
        .detail-row:last-child {
            margin-bottom: 0;
        }
        
        .detail-item {
            display: flex;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .detail-item .label {
            min-width: 80px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-right: 8px;
        }
        
        .detail-item .value {
            color: var(--text-primary);
        }

        /* 营业时间显示 */
        .business-hours-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 8px;
        }
        
        .hours-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        
        .day-name {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .hours-time {
            color: var(--text-secondary);
            font-size: 14px;
        }
        
        .hours-closed {
            color: #dc3545;
            font-style: italic;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .detail-row {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .card-actions {
                flex-direction: column;
            }
            
            .table-container {
                overflow-x: auto;
            }
            
            .business-hours-display {
                grid-template-columns: 1fr;
            }
        }

        /* Tab表单优化样式 */
        .form-tabs {
            width: 100%;
        }
        
        .tab-nav {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 24px;
        }
        
        .tab-item {
            background: none;
            border: none;
            padding: 12px 24px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tab-item:hover {
            color: var(--primary-steel-blue);
            background-color: rgba(52, 144, 220, 0.05);
        }
        
        .tab-item.active {
            color: var(--primary-steel-blue);
            border-bottom-color: var(--primary-steel-blue);
            background-color: rgba(52, 144, 220, 0.1);
        }
        
        .tab-icon {
            font-size: 16px;
        }
        
        .tab-content {
            animation: fadeInUp 0.3s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 增强表单样式 */
        .shop-form.enhanced {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        
        .shop-form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid var(--primary-steel-blue);
        }
        
        .shop-form-section .section-header {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .shop-form-section .section-header::before {
            content: '';
            width: 4px;
            height: 16px;
            background-color: var(--primary-steel-blue);
            border-radius: 2px;
        }
        
        .shop-form-section .section-desc {
            margin: 0 0 16px 0;
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
        }
        
        .form-grid.coordinates {
            grid-template-columns: 1fr 1fr;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }

        /* 增强的营业时间配置 */
        .business-hours-config.enhanced {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        
        .day-config.enhanced {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            margin-bottom: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .day-config.enhanced:hover {
            border-color: var(--primary-steel-blue);
            box-shadow: 0 2px 8px rgba(52, 144, 220, 0.1);
        }
        
        .day-config.enhanced:last-child {
            margin-bottom: 0;
        }
        
        .day-info {
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 120px;
        }
        
        .day-label {
            font-weight: 600;
            color: var(--text-primary);
            min-width: 50px;
        }
        
        .day-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            background-color: #28a745;
            color: white;
        }
        
        .day-status.closed {
            background-color: #dc3545;
        }
        
        .time-controls {
            display: flex;
            align-items: center;
            gap: 16px;
            flex: 1;
        }
        
        .time-inputs {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .time-inputs.disabled {
            opacity: 0.5;
        }
        
        .time-input {
            width: 120px;
        }
        
        .time-separator {
            color: var(--text-secondary);
            font-weight: 500;
            margin: 0 4px;
        }

        /* 切换开关样式 */
        .toggle-switch {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }
        
        .toggle-switch input[type="checkbox"] {
            display: none;
        }
        
        .toggle-slider {
            position: relative;
            width: 44px;
            height: 24px;
            background-color: #ccc;
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        
        .toggle-slider::before {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: white;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .toggle-switch input[type="checkbox"]:checked + .toggle-slider {
            background-color: var(--primary-steel-blue);
        }
        
        .toggle-switch input[type="checkbox"]:checked + .toggle-slider::before {
            transform: translateX(20px);
        }
        
        .toggle-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* 增强的模态框底部 */
        .modal-footer.enhanced {
            display: flex;
            flex-direction: column;
            gap: 16px;
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background-color: #f8f9fa;
        }
        
        .footer-info {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .form-tips {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--text-secondary);
            background-color: #e3f2fd;
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid #bbdefb;
        }
        
        .footer-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 地址输入组增强 */
        .address-input-group .btn {
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .modal-xl {
                max-width: 95%;
                margin: 10px;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-grid.coordinates {
                grid-template-columns: 1fr;
            }
            
            .day-config.enhanced {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }
            
            .day-info {
                justify-content: space-between;
            }
            
            .time-controls {
                justify-content: space-between;
            }
            
            .footer-actions {
                flex-direction: column;
            }
            
            .tab-nav {
                justify-content: center;
            }
            
            .tab-item {
                flex: 1;
                justify-content: center;
                padding: 12px 16px;
            }
        }

        /* 角色管理样式 */
        .role-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        /* 权限管理样式 */
        .permissions-container {
            max-height: 400px;
            overflow-y: auto;
            padding: 16px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: #f8f9fa;
        }

        .permission-group {
            margin-bottom: 24px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .permission-group:last-child {
            margin-bottom: 0;
        }

        .group-header {
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }

        .group-header .checkbox-label {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .permission-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 8px;
        }

        .permission-item {
            padding: 8px 12px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .permission-item:hover {
            background-color: #e9ecef;
            border-color: var(--primary-steel-blue);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .checkbox-label input[type="checkbox"] {
            margin: 0;
            cursor: pointer;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .permission-items {
                grid-template-columns: 1fr;
            }
        }

        /* 管理员管理专属样式 */
        .admin-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .admin-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary-steel-blue), var(--primary-ocean-blue));
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
        }

        .admin-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .role-tag {
            background: rgba(64, 158, 255, 0.1);
            color: var(--primary-steel-blue);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid rgba(64, 158, 255, 0.2);
        }

        .status-tag.active {
            background: rgba(103, 194, 58, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(103, 194, 58, 0.2);
        }

        .status-tag.inactive {
            background: rgba(245, 108, 108, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(245, 108, 108, 0.2);
        }

        .login-time {
            color: var(--text-secondary);
            font-size: 12px;
        }

        .never-login {
            color: var(--text-tertiary);
            font-style: italic;
            font-size: 12px;
        }

        .row-inactive {
            background: rgba(0, 0, 0, 0.02);
            opacity: 0.7;
        }

        .btn-disabled {
            opacity: 0.5;
            cursor: not-allowed !important;
            pointer-events: none;
        }

        .form-hint {
            display: block;
            margin-top: 4px;
            font-size: 12px;
            color: var(--text-tertiary);
        }

        .radio-group {
            display: flex;
            gap: 16px;
            margin-top: 8px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .radio-item input[type="radio"] {
            margin: 0;
            cursor: pointer;
        }

        .radio-label {
            color: var(--text-secondary);
            cursor: pointer;
        }

        .admin-form .form-group {
            margin-bottom: 20px;
        }

        /* 管理员相关的响应式调整 */
        @media (max-width: 768px) {
            .admin-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }
            
            .radio-group {
                flex-direction: column;
                gap: 8px;
            }
        }

    </style>
</head>
<body>
    <div id="app">
        <!-- 登录页面 -->
        <div v-if="!isLoggedIn" class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <!-- <div class="auth-logo">九</div> -->
                    <h1 class="auth-title">{{ currentAuthTab === 'login' ? '企业登录' : '企业注册' }}</h1>
                    <!-- <p class="auth-subtitle">统一管理 · 数据透明 · 高效运营</p> -->
                </div>


                <!-- 表单区域 -->
                <div class="auth-form">
                    <!-- 登录表单 -->
                    <div class="form-section" :class="{ active: currentAuthTab === 'login' }">
                        <form @submit.prevent="handleLogin">
                            <div class="form-group">
                                <label class="form-label">企业账号</label>
                                <input 
                                    type="text" 
                                    class="form-input"
                                    :class="{ error: loginErrors.username }"
                                    v-model="loginForm.username" 
                                    placeholder="请输入企业账号/手机号"
                                    @blur="validateLoginUsername"
                                >
                                <div class="error-message" :class="{ show: loginErrors.username }">
                                    {{ loginErrors.username }}
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">登录密码</label>
                                <div class="password-container">
                                    <input 
                                        :type="showLoginPassword ? 'text' : 'password'"
                                        class="form-input"
                                        :class="{ error: loginErrors.password }"
                                        v-model="loginForm.password" 
                                        placeholder="请输入登录密码"
                                        @blur="validateLoginPassword"
                                    >
                                    <button 
                                        type="button" 
                                        class="password-toggle" 
                                        @click="showLoginPassword = !showLoginPassword"
                                    >
                                        {{ showLoginPassword ? '👁️' : '👁️‍🗨️' }}
                                    </button>
                                </div>
                                <div class="error-message" :class="{ show: loginErrors.password }">
                                    {{ loginErrors.password }}
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">图形验证码</label>
                                <div class="captcha-container">
                                    <input 
                                        type="text" 
                                        class="form-input captcha-input"
                                        :class="{ error: loginErrors.captcha }"
                                        v-model="loginForm.captcha"
                                        placeholder="请输入验证码"
                                        @blur="validateCaptcha"
                                    >
                                    <button type="button" class="captcha-btn" @click="refreshCaptcha">
                                        {{ captchaText }}
                                    </button>
                                </div>
                                <div class="error-message" :class="{ show: loginErrors.captcha }">
                                    {{ loginErrors.captcha }}
                                </div>
                            </div>

                            <div class="remember-forgot">
                                <label class="remember-me">
                                    <input type="checkbox" v-model="loginForm.remember">
                                    记住登录状态
                                </label>
                                <a href="#" class="forgot-password" @click.prevent="handleForgotPassword">
                                    忘记密码？
                                </a>
                            </div>

                            <button type="submit" class="submit-btn" :class="{ loading: loginLoading }">
                                <span class="loading-spinner" v-if="loginLoading"></span>
                                {{ loginLoading ? '登录中...' : '立即登录' }}
                            </button>
                        </form>
                        
                        <!-- 切换到注册页面 -->
                        <div class="auth-switch">
                            <span class="auth-switch-text">还没有账号？</span>
                            <a class="auth-switch-link" @click="switchAuthTab('register')">立即注册</a>
                        </div>
                    </div>

                    <!-- 注册表单 -->
                    <div class="form-section" :class="{ active: currentAuthTab === 'register' }">
                        <form @submit.prevent="handleRegister">
                            <div class="form-group">
                                <label class="form-label">企业名称</label>
                                <input 
                                    type="text" 
                                    class="form-input"
                                    :class="{ error: registerErrors.companyName }"
                                    v-model="registerForm.companyName"
                                    placeholder="请输入企业全称"
                                    @blur="validateCompanyName"
                                >
                                <div class="error-message" :class="{ show: registerErrors.companyName }">
                                    {{ registerErrors.companyName }}
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">手机号码</label>
                                <input 
                                    type="tel" 
                                    class="form-input"
                                    :class="{ error: registerErrors.phone }"
                                    v-model="registerForm.phone"
                                    placeholder="请输入手机号码"
                                    @blur="validatePhone"
                                >
                                <div class="error-message" :class="{ show: registerErrors.phone }">
                                    {{ registerErrors.phone }}
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">短信验证码</label>
                                <div class="captcha-container">
                                    <input 
                                        type="text" 
                                        class="form-input captcha-input"
                                        :class="{ error: registerErrors.smsCode }"
                                        v-model="registerForm.smsCode"
                                        placeholder="请输入验证码"
                                        @blur="validateSmsCode"
                                    >
                                    <button 
                                        type="button" 
                                        class="captcha-btn" 
                                        :disabled="smsCountdown > 0"
                                        @click="sendSmsCode"
                                    >
                                        {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
                                    </button>
                                </div>
                                <div class="error-message" :class="{ show: registerErrors.smsCode }">
                                    {{ registerErrors.smsCode }}
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">设置密码</label>
                                <div class="password-container">
                                    <input 
                                        :type="showRegisterPassword ? 'text' : 'password'"
                                        class="form-input"
                                        :class="{ error: registerErrors.password }"
                                        v-model="registerForm.password"
                                        placeholder="请设置登录密码"
                                        @input="checkPasswordStrength"
                                        @blur="validateRegisterPassword"
                                    >
                                    <button 
                                        type="button" 
                                        class="password-toggle" 
                                        @click="showRegisterPassword = !showRegisterPassword"
                                    >
                                        {{ showRegisterPassword ? '👁️' : '👁️‍🗨️' }}
                                    </button>
                                </div>
                                
                                <div class="error-message" :class="{ show: registerErrors.password }">
                                    {{ registerErrors.password }}
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">确认密码</label>
                                <div class="password-container">
                                    <input 
                                        :type="showConfirmPassword ? 'text' : 'password'"
                                        class="form-input"
                                        :class="{ error: registerErrors.confirmPassword }"
                                        v-model="registerForm.confirmPassword"
                                        placeholder="请再次输入密码"
                                        @blur="validateConfirmPassword"
                                    >
                                    <button 
                                        type="button" 
                                        class="password-toggle" 
                                        @click="showConfirmPassword = !showConfirmPassword"
                                    >
                                        {{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}
                                    </button>
                                </div>
                                <div class="error-message" :class="{ show: registerErrors.confirmPassword }">
                                    {{ registerErrors.confirmPassword }}
                                </div>
                            </div>

                            <div class="agreement">
                                <input 
                                    type="checkbox" 
                                    v-model="registerForm.agreement"
                                    @change="validateAgreement"
                                >
                                我已阅读并同意
                                <a href="#" @click.prevent="showAgreement('terms')">《用户服务协议》</a>
                                和
                                <a href="#" @click.prevent="showAgreement('privacy')">《隐私保护政策》</a>
                            </div>
                            <div class="error-message" :class="{ show: registerErrors.agreement }">
                                {{ registerErrors.agreement }}
                            </div>

                            <button type="submit" class="submit-btn" :class="{ loading: registerLoading }">
                                <span class="loading-spinner" v-if="registerLoading"></span>
                                {{ registerLoading ? '注册中...' : '立即注册' }}
                            </button>
                        </form>
                        
                        <!-- 切换到登录页面 -->
                        <div class="auth-switch">
                            <span class="auth-switch-text">已有账号？</span>
                            <a class="auth-switch-link" @click="switchAuthTab('login')">立即登录</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主系统界面 -->
        <div v-else class="app-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
                <div class="logo-area">
                    <div class="logo">JR</div>
                    <div class="logo-text">企业管理系统</div>
                </div>

                <nav class="nav-menu">
                    <div class="nav-item">
                        <a class="nav-link" :class="{ active: currentPage === 'dashboard' }" @click="navigateTo('dashboard')">
                            <div class="nav-icon">📊</div>
                            <span class="nav-text">数据大屏</span>
                        </a>
                    </div>

                    <div class="nav-item">
                        <a class="nav-link" :class="{ active: currentPage === 'shops' }" @click="navigateTo('shops')">
                            <div class="nav-icon">🏪</div>
                            <span class="nav-text">门店管理</span>
                            <!-- <span class="nav-badge" v-if="shopStats.offline > 0">{{ shopStats.offline }}</span> -->
                        </a>
                    </div>


                    <div class="nav-item">
                        <a class="nav-link" :class="{ active: currentPage.startsWith('permissions') }" @click="togglePermissionsMenu">
                            <div class="nav-icon">🔐</div>
                            <span class="nav-text">权限管理</span>
                        </a>
                        <div v-show="showPermissionsMenu" class="nav-submenu">
                            <div class="nav-item">
                                <a class="nav-link nav-sub-link" :class="{ active: currentPage === 'permissions-admin' }" @click="navigateTo('permissions-admin')">
                                    <div class="nav-icon">👤</div>
                                    <span class="nav-text">管理员管理</span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a class="nav-link nav-sub-link" :class="{ active: currentPage === 'permissions-role' }" @click="navigateTo('permissions-role')">
                                    <div class="nav-icon">🏷️</div>
                                    <span class="nav-text">角色管理</span>
                                </a>
                            </div>
                        </div>
                    </div>

                </nav>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 顶部栏 -->
                <header class="header">
                    <div class="header-left">
                        <button class="sidebar-toggle" @click="toggleSidebar">
                            <span>☰</span>
                        </button>
                        <div class="breadcrumb">
                            <span>{{ getCurrentPageTitle() }}</span>
                        </div>
                    </div>

                    <div class="header-right">
                        <button class="header-notification">
                            <span>🔔</span>
                            <div class="notification-badge" v-if="notifications.unread > 0"></div>
                        </button>

                        <div class="user-info" @click="showUserMenu = !showUserMenu">
                            <div class="user-avatar">管</div>
                            <span class="user-name">{{ currentUser.name }}</span>
                        </div>
                    </div>
                </header>

                <!-- 内容区域 -->
                <main class="content-area">
                    <!-- 数据大屏页面 -->
                    <div v-if="currentPage === 'dashboard'" class="page-container">
                        <div class="page-header">
                            <h1 class="page-title">数据大屏</h1>
                            <p class="page-description">实时监控所有门店的关键经营数据</p>
                        </div>

                        <!-- 核心指标卡片区域 -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon blue">🏪</div>
                                    <div class="stat-status online"></div>
                                </div>
                                <div class="stat-value">{{ dashboardData.totalShops }}</div>
                                <div class="stat-label">门店总数</div>
                                <div class="stat-info">
                                    <span class="stat-secondary">在线: {{ dashboardData.onlineShops }}</span>
                                    <div class="stat-trend up">+{{ dashboardData.newShopsThisMonth }} 本月新增</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon green">💰</div>
                                    <div class="stat-live-indicator"></div>
                                </div>
                                <div class="stat-value">¥{{ formatNumber(dashboardData.todayRevenue) }}</div>
                                <div class="stat-label">今日营业额</div>
                                <div class="stat-info">
                                    <span class="stat-secondary">目标: ¥{{ formatNumber(dashboardData.revenueTarget) }}</span>
                                    <div class="stat-trend up">+{{ dashboardData.revenueGrowth }}% 较昨日</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon orange">📦</div>
                                    <div class="stat-badge">{{ dashboardData.cancelledOrders }}</div>
                                </div>
                                <div class="stat-value">{{ dashboardData.todayOrders }}</div>
                                <div class="stat-label">今日订单量</div>
                                <div class="stat-info">
                                    <span class="stat-secondary">成功: {{ dashboardData.successOrders }}</span>
                                    <div class="stat-trend up">+{{ dashboardData.orderGrowth }}% 较昨日</div>
                                </div>
                            </div>

                            <!-- <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon red">🚨</div>
                                    <div class="stat-alert-level high" v-if="dashboardData.stockAlerts > 10"></div>
                                </div>
                                <div class="stat-value">{{ dashboardData.stockAlerts }}</div>
                                <div class="stat-label">库存预警</div>
                                <div class="stat-info">
                                    <span class="stat-secondary">低库存商品</span>
                                    <div class="stat-trend down">-{{ dashboardData.stockImprovement }} 较上周</div>
                                </div>
                            </div> -->

                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon purple">👥</div>
                                    <div class="stat-growth-indicator">+{{ dashboardData.memberGrowthRate }}%</div>
                                </div>
                                <div class="stat-value">{{ formatNumber(dashboardData.activeMembers) }}</div>
                                <div class="stat-label">活跃会员</div>
                                <div class="stat-info">
                                    <span class="stat-secondary">本月活跃</span>
                                    <div class="stat-trend up">+{{ dashboardData.newMembers }} 新增会员</div>
                                </div>
                            </div>

                            <!-- <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon warning">⚠️</div>
                                    <div class="stat-priority-badge" v-if="dashboardData.highPriorityAlerts > 0">{{ dashboardData.highPriorityAlerts }}</div>
                                </div>
                                <div class="stat-value">{{ dashboardData.systemAlerts }}</div>
                                <div class="stat-label">系统告警</div>
                                <div class="stat-info">
                                    <span class="stat-secondary">待处理事件</span>
                                    <div class="stat-trend down">-{{ dashboardData.resolvedAlerts }} 已处理</div>
                                </div>
                            </div> -->
                        </div>

                        <!-- 图表分析区域 -->
                        <div class="charts-grid">
                            <!-- 营业额趋势图 -->
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h3 class="chart-title">营业额趋势</h3>
                                    <div class="time-selector">
                                        <button class="time-btn" :class="{ active: chartTimeRange === 'today' }" @click="switchTimeRange('today')">今日</button>
                                        <button class="time-btn" :class="{ active: chartTimeRange === 'week' }" @click="switchTimeRange('week')">本周</button>
                                        <button class="time-btn" :class="{ active: chartTimeRange === 'month' }" @click="switchTimeRange('month')">本月</button>
                                        <button class="time-btn" :class="{ active: chartTimeRange === 'year' }" @click="switchTimeRange('year')">本年</button>
                                    </div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-container" id="revenueChart"></div>
                                </div>
                            </div>

                            <!-- 门店业绩对比 -->
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h3 class="chart-title">门店业绩对比</h3>
                                    <div class="chart-sort">
                                        <button class="sort-btn" @click="sortShopsByRevenue">按营业额排序</button>
                                    </div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-container" id="shopCompareChart"></div>
                                </div>
                            </div>

                            <!-- 支付方式分布 -->
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h3 class="chart-title">支付方式分布</h3>
                                    <div class="chart-period">今日数据</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-container" id="paymentChart"></div>
                                </div>
                            </div>

                            <!-- 热销商品排行 -->
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h3 class="chart-title">热销商品排行</h3>
                                    <div class="chart-period">本周数据</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-container" id="productRankChart"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 门店状态表格 -->
                        <div class="table-card">
                            <div class="table-header">
                                <h3 class="table-title">门店实时状态</h3>
                                <div class="table-actions">
                                    <button class="refresh-btn" @click="refreshShopData">刷新</button>
                                </div>
                            </div>
                            <div class="table-body">
                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>门店名称</th>
                                                <th>运营状态</th>
                                                <th>今日营业额</th>
                                                <th>订单数</th>
                                                <th>客单价</th>
                                                <th>利润</th>
                                                <th>库存状态</th>
                                                <th>最后更新</th>
                                                <!-- <th>操作</th> -->
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="shop in shopList" :key="shop.id" :class="{ 'row-offline': shop.status === 'offline' }">
                                                <td>
                                                    <div class="shop-name">
                                                        <span class="name">{{ shop.name }}</span>
                                                        <span class="rank" v-if="getShopRank(shop.todayRevenue) <= 3">#{{ getShopRank(shop.todayRevenue) }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="status-tag" :class="shop.status">
                                                        <span class="status-dot"></span>
                                                        {{ shop.status === 'online' ? '营业中' : '已打烊' }}
                                                    </span>
                                                </td>
                                                <td class="revenue-cell">
                                                    <span class="amount">¥{{ formatNumber(shop.todayRevenue) }}</span>
                                                    <div class="progress-bar">
                                                        <div class="progress-fill" :style="{ width: (shop.todayRevenue / 40000 * 100) + '%' }"></div>
                                                    </div>
                                                </td>
                                                <td>{{ shop.todayOrders }}</td>
                                                <td>¥{{ shop.avgPrice }}</td>
                                                <td class="profit-cell">
                                                    <span class="profit-amount">¥{{ formatNumber(shop.profit) }}</span>
                                                    <span class="profit-rate">({{ ((shop.profit / shop.todayRevenue) * 100).toFixed(1) }}%)</span>
                                                </td>
                                                <td>
                                                    <span class="stock-status" :class="shop.stockStatus">
                                                        {{ getStockStatusText(shop.stockStatus) }}
                                                    </span>
                                                </td>
                                                <td class="update-time">{{ shop.lastUpdate }}</td>
                                                <!-- <td>
                                                    <div class="action-buttons">
                                                        <button class="action-btn view" @click="viewShopDetail(shop.id)">详情</button>
                                                        <button class="action-btn control" v-if="shop.status === 'online'" @click="controlShop(shop.id)">控制</button>
                                                    </div>
                                                </td> -->
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 门店管理页面 -->
                    <div v-else-if="currentPage === 'shops'" class="page-container">
                        <div class="page-header">
                            <h1 class="page-title">门店管理</h1>
                            <p class="page-description">管理所有门店的基础信息、账号权限和运营状态</p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <!-- <h3 class="card-title">门店列表</h3> -->
                                <div class="card-actions">
                                    <button class="btn btn-primary" @click="showAddShopModal = true">
                                        <i class="icon">➕</i> 新增门店
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th width="60">ID</th>
                                                <th width="120">门店名称</th>
                                                <th width="100">负责人</th>
                                                <th width="120">登录手机</th>
                                                <th width="200">门店地址</th>
                                                <th width="120">服务电话</th>
                                                <th width="100">营业状态</th>
                                                <th width="120">营业时间</th>
                                                <th width="100">创建时间</th>
                                                <th width="200">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="shop in shopList" :key="shop.id" :class="{ 'row-inactive': shop.status === 0 }">
                                                <td>{{ shop.id }}</td>
                                                <td>
                                                    <div class="shop-name">
                                                        <span class="name">{{ shop.shop_name }}</span>
                                                        <span class="remark" v-if="shop.remark">{{ shop.remark }}</span>
                                                    </div>
                                                </td>
                                                <td>{{ shop.manager_name }}</td>
                                                <td>{{ shop.mobile }}</td>
                                                <td>
                                                    <div class="address-info">
                                                        <span class="address">{{ shop.address }}</span>
                                                        <span class="coordinates" v-if="shop.latitude && shop.longitude">
                                                            ({{ shop.latitude }}, {{ shop.longitude }})
                                                        </span>
                                                    </div>
                                                </td>
                                                <td>{{ shop.service_phone }}</td>
                                                <td>
                                                    <span class="status-tag" :class="{ 'active': shop.status === 1, 'inactive': shop.status === 0 }">
                                                        {{ shop.status === 1 ? '营业中' : '暂停营业' }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="business-hours">
                                                        <span v-if="getTodayBusinessHours(shop.business_hours)">
                                                            {{ getTodayBusinessHours(shop.business_hours) }}
                                                        </span>
                                                        <span v-else class="closed">今日休业</span>
                                                    </div>
                                                </td>
                                                <td>{{ formatDate(shop.created_at) }}</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn btn-sm btn-primary" @click="editShop(shop)" title="编辑门店">
                                                            <i class="icon">✏️</i> 编辑
                                                        </button>
                                                        <button class="btn btn-sm btn-info" @click="viewShopDetail(shop)" title="查看详情">
                                                            <i class="icon">👁️</i> 详情
                                                        </button>
                                                        <button class="btn btn-sm btn-warning" @click="toggleShopStatus(shop)" 
                                                                :title="shop.status === 1 ? '暂停营业' : '恢复营业'">
                                                            <i class="icon">{{ shop.status === 1 ? '⏸️' : '▶️' }}</i>
                                                            {{ shop.status === 1 ? '暂停' : '启用' }}
                                                        </button>
                                                        <button class="btn btn-sm btn-danger" @click="deleteShop(shop)" title="删除门店">
                                                            <i class="icon">🗑️</i> 删除
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 门店新增模态框 -->
                        <div v-if="showAddShopModal" class="modal-overlay" @click.self="showAddShopModal = false">
                            <div class="modal-dialog modal-xl">
                                <div class="modal-header">
                                    <h3 class="modal-title">新增门店</h3>
                                    <button class="modal-close" @click="showAddShopModal = false">×</button>
                                </div>
                                <div class="modal-body">
                                    <!-- Tab导航 -->
                                    <div class="form-tabs">
                                        <div class="tab-nav">
                                            <button class="tab-item" :class="{ active: activeFormTab === 'basic' }" @click="activeFormTab = 'basic'">
                                                <i class="tab-icon">🏪</i>
                                                基本信息
                                            </button>
                                            <button class="tab-item" :class="{ active: activeFormTab === 'hours' }" @click="activeFormTab = 'hours'">
                                                <i class="tab-icon">🕐</i>
                                                营业时间
                                            </button>
                                        </div>
                                        
                                        <!-- 基本信息Tab -->
                                        <div v-show="activeFormTab === 'basic'" class="tab-content">
                                            <form class="shop-form enhanced">
                                                <div class="shop-form-section">
                                                    <h4 class="section-header">门店基本信息</h4>
                                                    <div class="form-grid">
                                                        <div class="form-group">
                                                            <label class="form-label">门店名称 <span class="required">*</span></label>
                                                            <input type="text" class="form-control" v-model="shopForm.shop_name" placeholder="请输入门店名称">
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="form-label">门店负责人 <span class="required">*</span></label>
                                                            <input type="text" class="form-control" v-model="shopForm.manager_name" placeholder="请输入负责人姓名">
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="form-label">营业状态</label>
                                                            <select class="form-control" v-model="shopForm.status">
                                                                <option value="1">营业中</option>
                                                                <option value="0">暂停营业</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="shop-form-section">
                                                    <h4 class="section-header">联系方式</h4>
                                                    <div class="form-grid">
                                                        <div class="form-group">
                                                            <label class="form-label">登录手机 <span class="required">*</span></label>
                                                            <input type="tel" class="form-control" v-model="shopForm.mobile" placeholder="请输入登录手机号">
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="form-label">登录密码</label>
                                                            <input type="password" class="form-control" v-model="shopForm.password" placeholder="请输入登录密码">
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="form-label">服务电话</label>
                                                            <input type="tel" class="form-control" v-model="shopForm.service_phone" placeholder="请输入服务电话">
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="shop-form-section">
                                                    <h4 class="section-header">门店位置</h4>
                                                    <div class="form-group full-width">
                                                        <label class="form-label">门店地址 <span class="required">*</span></label>
                                                        <div class="address-input-group">
                                                            <input type="text" class="form-control" v-model="shopForm.address" placeholder="请输入详细地址">
                                                            <button type="button" class="btn btn-secondary" @click="selectLocation">
                                                                <i class="icon">📍</i> 选择位置
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="form-grid coordinates">
                                                        <div class="form-group">
                                                            <label class="form-label">经度坐标</label>
                                                            <input type="text" class="form-control" v-model="shopForm.longitude" placeholder="longitude">
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="form-label">纬度坐标</label>
                                                            <input type="text" class="form-control" v-model="shopForm.latitude" placeholder="latitude">
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="shop-form-section">
                                                    <h4 class="section-header">备注信息</h4>
                                                    <div class="form-group full-width">
                                                        <label class="form-label">备注说明</label>
                                                        <textarea class="form-control" v-model="shopForm.remark" rows="3" placeholder="请输入备注说明"></textarea>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                        
                                        <!-- 营业时间Tab -->
                                        <div v-show="activeFormTab === 'hours'" class="tab-content">
                                            <div class="hours-form">
                                                <div class="shop-form-section">
                                                    <h4 class="section-header">营业时间设置</h4>
                                                    <p class="section-desc">设置门店每天的营业时间，可以单独设置某天为休息日</p>
                                                    <div class="business-hours-config enhanced">
                                                        <div v-for="(dayInfo, day) in shopForm.business_hours" :key="day" class="day-config enhanced">
                                                            <div class="day-info">
                                                                <div class="day-label">{{ getDayLabel(day) }}</div>
                                                                <div class="day-status" :class="{ closed: dayInfo.closed }">
                                                                    {{ dayInfo.closed ? '休息' : '营业' }}
                                                                </div>
                                                            </div>
                                                            <div class="time-controls">
                                                                <div class="time-inputs" :class="{ disabled: dayInfo.closed }">
                                                                    <input type="time" class="form-control time-input" v-model="dayInfo.open" 
                                                                           :disabled="dayInfo.closed" placeholder="开始时间">
                                                                    <span class="time-separator">至</span>
                                                                    <input type="time" class="form-control time-input" v-model="dayInfo.close" 
                                                                           :disabled="dayInfo.closed" placeholder="结束时间">
                                                                </div>
                                                                <label class="toggle-switch">
                                                                    <input type="checkbox" v-model="dayInfo.closed">
                                                                    <span class="toggle-slider"></span>
                                                                    <span class="toggle-label">休息</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer enhanced">
                                    <div class="footer-info">
                                        <span class="form-tips">
                                            <i class="icon">💡</i>
                                            {{ activeFormTab === 'basic' ? '请填写门店基本信息，带*为必填项' : '请设置门店营业时间，可灵活调整每日营业时间' }}
                                        </span>
                                    </div>
                                    <div class="footer-actions">
                                        <button class="btn btn-secondary" @click="showAddShopModal = false">取消</button>
                                        <button v-if="activeFormTab === 'basic'" class="btn btn-primary" @click="activeFormTab = 'hours'">
                                            下一步：营业时间 <i class="icon">→</i>
                                        </button>
                                        <button v-if="activeFormTab === 'hours'" class="btn btn-secondary" @click="activeFormTab = 'basic'">
                                            <i class="icon">←</i> 返回基本信息
                                        </button>
                                        <button class="btn btn-primary" @click="saveShop">
                                            <i class="icon">💾</i> 保存门店
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 门店编辑模态框 -->
                        <div v-if="showEditShopModal" class="modal-overlay" @click.self="showEditShopModal = false">
                            <div class="modal-dialog modal-xl">
                                <div class="modal-header">
                                    <h3 class="modal-title">编辑门店</h3>
                                    <button class="modal-close" @click="showEditShopModal = false">×</button>
                                </div>
                                <div class="modal-body">
                                    <!-- Tab导航 -->
                                    <div class="form-tabs">
                                        <div class="tab-nav">
                                            <button class="tab-item" :class="{ active: activeFormTab === 'basic' }" @click="activeFormTab = 'basic'">
                                                <i class="tab-icon">🏪</i>
                                                基本信息
                                            </button>
                                            <button class="tab-item" :class="{ active: activeFormTab === 'hours' }" @click="activeFormTab = 'hours'">
                                                <i class="tab-icon">🕐</i>
                                                营业时间
                                            </button>
                                        </div>
                                        
                                        <!-- 基本信息Tab -->
                                        <div v-show="activeFormTab === 'basic'" class="tab-content">
                                            <form class="shop-form enhanced">
                                                <div class="shop-form-section">
                                                    <h4 class="section-header">门店基本信息</h4>
                                                    <div class="form-grid">
                                                        <div class="form-group">
                                                            <label class="form-label">门店名称 <span class="required">*</span></label>
                                                            <input type="text" class="form-control" v-model="shopForm.shop_name" placeholder="请输入门店名称">
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="form-label">门店负责人 <span class="required">*</span></label>
                                                            <input type="text" class="form-control" v-model="shopForm.manager_name" placeholder="请输入负责人姓名">
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="form-label">营业状态</label>
                                                            <select class="form-control" v-model="shopForm.status">
                                                                <option value="1">营业中</option>
                                                                <option value="0">暂停营业</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="shop-form-section">
                                                    <h4 class="section-header">联系方式</h4>
                                                    <div class="form-grid">
                                                        <div class="form-group">
                                                            <label class="form-label">登录手机 <span class="required">*</span></label>
                                                            <input type="tel" class="form-control" v-model="shopForm.mobile" placeholder="请输入登录手机号">
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="form-label">登录密码</label>
                                                            <input type="password" class="form-control" v-model="shopForm.password" placeholder="不修改请留空">
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="form-label">服务电话</label>
                                                            <input type="tel" class="form-control" v-model="shopForm.service_phone" placeholder="请输入服务电话">
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="shop-form-section">
                                                    <h4 class="section-header">门店位置</h4>
                                                    <div class="form-group full-width">
                                                        <label class="form-label">门店地址 <span class="required">*</span></label>
                                                        <div class="address-input-group">
                                                            <input type="text" class="form-control" v-model="shopForm.address" placeholder="请输入详细地址">
                                                            <button type="button" class="btn btn-secondary" @click="selectLocation">
                                                                <i class="icon">📍</i> 选择位置
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="form-grid coordinates">
                                                        <div class="form-group">
                                                            <label class="form-label">经度坐标</label>
                                                            <input type="text" class="form-control" v-model="shopForm.longitude" placeholder="longitude">
                                                        </div>
                                                        <div class="form-group">
                                                            <label class="form-label">纬度坐标</label>
                                                            <input type="text" class="form-control" v-model="shopForm.latitude" placeholder="latitude">
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="shop-form-section">
                                                    <h4 class="section-header">备注信息</h4>
                                                    <div class="form-group full-width">
                                                        <label class="form-label">备注说明</label>
                                                        <textarea class="form-control" v-model="shopForm.remark" rows="3" placeholder="请输入备注说明"></textarea>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                        
                                        <!-- 营业时间Tab -->
                                        <div v-show="activeFormTab === 'hours'" class="tab-content">
                                            <div class="hours-form">
                                                <div class="shop-form-section">
                                                    <h4 class="section-header">营业时间设置</h4>
                                                    <p class="section-desc">设置门店每天的营业时间，可以单独设置某天为休息日</p>
                                                    <div class="business-hours-config enhanced">
                                                        <div v-for="(dayInfo, day) in shopForm.business_hours" :key="day" class="day-config enhanced">
                                                            <div class="day-info">
                                                                <div class="day-label">{{ getDayLabel(day) }}</div>
                                                                <div class="day-status" :class="{ closed: dayInfo.closed }">
                                                                    {{ dayInfo.closed ? '休息' : '营业' }}
                                                                </div>
                                                            </div>
                                                            <div class="time-controls">
                                                                <div class="time-inputs" :class="{ disabled: dayInfo.closed }">
                                                                    <input type="time" class="form-control time-input" v-model="dayInfo.open" 
                                                                           :disabled="dayInfo.closed" placeholder="开始时间">
                                                                    <span class="time-separator">至</span>
                                                                    <input type="time" class="form-control time-input" v-model="dayInfo.close" 
                                                                           :disabled="dayInfo.closed" placeholder="结束时间">
                                                                </div>
                                                                <label class="toggle-switch">
                                                                    <input type="checkbox" v-model="dayInfo.closed">
                                                                    <span class="toggle-slider"></span>
                                                                    <span class="toggle-label">休息</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer enhanced">
                                    <div class="footer-info">
                                        <span class="form-tips">
                                            <i class="icon">💡</i>
                                            {{ activeFormTab === 'basic' ? '请修改门店基本信息，带*为必填项' : '请调整门店营业时间，可灵活设置每日营业时间' }}
                                        </span>
                                    </div>
                                    <div class="footer-actions">
                                        <button class="btn btn-secondary" @click="showEditShopModal = false">取消</button>
                                        <button v-if="activeFormTab === 'basic'" class="btn btn-primary" @click="activeFormTab = 'hours'">
                                            下一步：营业时间 <i class="icon">→</i>
                                        </button>
                                        <button v-if="activeFormTab === 'hours'" class="btn btn-secondary" @click="activeFormTab = 'basic'">
                                            <i class="icon">←</i> 返回基本信息
                                        </button>
                                        <button class="btn btn-primary" @click="saveShop">
                                            <i class="icon">💾</i> 保存修改
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 门店详情模态框 -->
                        <div v-if="showShopDetail" class="modal-overlay" @click.self="showShopDetail = false">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-header">
                                    <h3 class="modal-title">门店详情</h3>
                                    <button class="modal-close" @click="showShopDetail = false">×</button>
                                </div>
                                <div class="modal-body">
                                    <div v-if="currentDetailShop" class="shop-detail">
                                        <div class="detail-section">
                                            <h4 class="section-title">基本信息</h4>
                                            <div class="detail-row">
                                                <div class="detail-item">
                                                    <span class="label">门店ID:</span>
                                                    <span class="value">{{ currentDetailShop.id }}</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="label">门店名称:</span>
                                                    <span class="value">{{ currentDetailShop.shop_name }}</span>
                                                </div>
                                            </div>
                                            <div class="detail-row">
                                                <div class="detail-item">
                                                    <span class="label">门店负责人:</span>
                                                    <span class="value">{{ currentDetailShop.manager_name || '-' }}</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="label">营业状态:</span>
                                                    <span class="value status-tag" :class="{ 'active': currentDetailShop.status === 1, 'inactive': currentDetailShop.status === 0 }">
                                                        {{ currentDetailShop.status === 1 ? '营业中' : '暂停营业' }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detail-section">
                                            <h4 class="section-title">联系信息</h4>
                                            <div class="detail-row">
                                                <div class="detail-item">
                                                    <span class="label">登录手机:</span>
                                                    <span class="value">{{ currentDetailShop.mobile }}</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="label">服务电话:</span>
                                                    <span class="value">{{ currentDetailShop.service_phone || '-' }}</span>
                                                </div>
                                            </div>
                                            <div class="detail-row">
                                                <div class="detail-item full-width">
                                                    <span class="label">门店地址:</span>
                                                    <span class="value">{{ currentDetailShop.address }}</span>
                                                </div>
                                            </div>
                                            <div class="detail-row">
                                                <div class="detail-item">
                                                    <span class="label">经度:</span>
                                                    <span class="value">{{ currentDetailShop.longitude || '-' }}</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="label">纬度:</span>
                                                    <span class="value">{{ currentDetailShop.latitude || '-' }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detail-section">
                                            <h4 class="section-title">营业时间</h4>
                                            <div class="business-hours-display">
                                                <div v-for="(dayInfo, day) in currentDetailShop.business_hours" :key="day" class="hours-row">
                                                    <span class="day-name">{{ getDayLabel(day) }}</span>
                                                    <span class="hours-time" v-if="!dayInfo.closed">
                                                        {{ dayInfo.open }} - {{ dayInfo.close }}
                                                    </span>
                                                    <span class="hours-closed" v-else>休息</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detail-section" v-if="currentDetailShop.remark">
                                            <h4 class="section-title">备注说明</h4>
                                            <div class="detail-row">
                                                <div class="detail-item full-width">
                                                    <span class="value">{{ currentDetailShop.remark }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detail-section">
                                            <h4 class="section-title">时间信息</h4>
                                            <div class="detail-row">
                                                <div class="detail-item">
                                                    <span class="label">创建时间:</span>
                                                    <span class="value">{{ currentDetailShop.created_at }}</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="label">更新时间:</span>
                                                    <span class="value">{{ currentDetailShop.updated_at }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button class="btn btn-secondary" @click="showShopDetail = false">关闭</button>
                                    <button class="btn btn-primary" @click="editShop(currentDetailShop); showShopDetail = false">编辑</button>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- 角色管理页面 -->
                    <div v-else-if="currentPage === 'permissions-role'" class="page-container">
                        <div class="page-header">
                            <h1 class="page-title">角色管理</h1>
                            <p class="page-description">管理系统角色信息，设置角色权限</p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <!-- <h3 class="card-title">角色列表</h3> -->
                                <div class="card-actions">
                                    <button class="btn btn-primary" @click="showAddRoleModal = true">
                                        <i class="icon">➕</i> 新增角色
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th width="80">ID</th>
                                                <th width="200">角色名称</th>
                                                <th width="200">添加时间</th>
                                                <th width="200">更新时间</th>
                                                <th width="300">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="role in roleList" :key="role.id">
                                                <td>{{ role.id }}</td>
                                                <td>{{ role.role_name }}</td>
                                                <td>{{ formatDate(role.created_at) }}</td>
                                                <td>{{ formatDate(role.updated_at) }}</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn btn-sm btn-warning" @click="managePermissions(role)" title="权限管理">
                                                            <i class="icon">🔐</i> 权限
                                                        </button>
                                                        <button class="btn btn-sm btn-primary" @click="editRole(role)" title="编辑角色">
                                                            <i class="icon">✏️</i> 编辑
                                                        </button>
                                                        <button class="btn btn-sm btn-danger" @click="deleteRole(role)" title="删除角色">
                                                            <i class="icon">🗑️</i> 删除
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 角色新增模态框 -->
                        <div v-if="showAddRoleModal" class="modal-overlay" @click.self="showAddRoleModal = false">
                            <div class="modal-dialog">
                                <div class="modal-header">
                                    <h3 class="modal-title">新增角色</h3>
                                    <button class="modal-close" @click="showAddRoleModal = false">×</button>
                                </div>
                                <div class="modal-body">
                                    <form class="role-form">
                                        <div class="form-group">
                                            <label class="form-label">角色名称 <span class="required">*</span></label>
                                            <input type="text" class="form-control" v-model="roleForm.role_name" placeholder="请输入角色名称">
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button class="btn btn-secondary" @click="showAddRoleModal = false">取消</button>
                                    <button class="btn btn-primary" @click="saveRole">
                                        <i class="icon">💾</i> 保存角色
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 角色编辑模态框 -->
                        <div v-if="showEditRoleModal" class="modal-overlay" @click.self="showEditRoleModal = false">
                            <div class="modal-dialog">
                                <div class="modal-header">
                                    <h3 class="modal-title">编辑角色</h3>
                                    <button class="modal-close" @click="showEditRoleModal = false">×</button>
                                </div>
                                <div class="modal-body">
                                    <form class="role-form">
                                        <div class="form-group">
                                            <label class="form-label">角色名称 <span class="required">*</span></label>
                                            <input type="text" class="form-control" v-model="roleForm.role_name" placeholder="请输入角色名称">
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button class="btn btn-secondary" @click="showEditRoleModal = false">取消</button>
                                    <button class="btn btn-primary" @click="saveRole">
                                        <i class="icon">💾</i> 保存修改
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 权限管理模态框 -->
                        <div v-if="showPermissionsModal" class="modal-overlay" @click.self="showPermissionsModal = false">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-header">
                                    <h3 class="modal-title">权限管理 - {{ currentRole?.role_name }}</h3>
                                    <button class="modal-close" @click="showPermissionsModal = false">×</button>
                                </div>
                                <div class="modal-body">
                                    <div class="permissions-container">
                                        <div class="permission-group" v-for="group in permissionGroups" :key="group.name">
                                            <div class="group-header">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" 
                                                           :checked="isGroupAllSelected(group)" 
                                                           @change="toggleGroupPermissions(group, $event.target.checked)">
                                                    <strong>{{ group.name }}</strong>
                                                </label>
                                            </div>
                                            <div class="permission-items">
                                                <label class="checkbox-label permission-item" v-for="permission in group.permissions" :key="permission.id">
                                                    <input type="checkbox" 
                                                           v-model="selectedPermissions" 
                                                           :value="permission.id">
                                                    {{ permission.name }}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button class="btn btn-secondary" @click="showPermissionsModal = false">取消</button>
                                    <button class="btn btn-primary" @click="savePermissions">
                                        <i class="icon">💾</i> 保存权限
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 管理员管理页面 -->
                    <div v-else-if="currentPage === 'permissions-admin'" class="page-container">
                        <div class="page-header">
                            <h1 class="page-title">管理员管理</h1>
                            <p class="page-description">管理系统管理员账户信息和权限设置</p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <!-- <h3 class="card-title">管理员列表</h3> -->
                                <div class="card-actions">
                                    <button class="btn btn-primary" @click="showAddAdminModal = true">
                                        <i class="icon">➕</i> 新增管理员
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th width="60">ID</th>
                                                <th width="120">昵称</th>
                                                <th width="120">登录账号</th>
                                                <th width="100">所属角色</th>
                                                <th width="80">状态</th>
                                                <th width="150">最后登录时间</th>
                                                <th width="120">添加时间</th>
                                                <th width="200">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="admin in adminList" :key="admin.id" :class="{ 'row-inactive': admin.status === 0 }">
                                                <td>{{ admin.id }}</td>
                                                <td>
                                                    <div class="admin-info">
                                                        <div class="admin-avatar">{{ admin.nickname.charAt(0) }}</div>
                                                        <span class="admin-name">{{ admin.nickname }}</span>
                                                    </div>
                                                </td>
                                                <td>{{ admin.username }}</td>
                                                <td>
                                                    <span class="role-tag">{{ getRoleName(admin.role_id) }}</span>
                                                </td>
                                                <td>
                                                    <span class="status-tag" :class="{ 'active': admin.status === 1, 'inactive': admin.status === 0 }">
                                                        {{ admin.status === 1 ? '正常' : '禁用' }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span v-if="admin.login_time" class="login-time">{{ formatDate(admin.login_time) }}</span>
                                                    <span v-else class="never-login">从未登录</span>
                                                </td>
                                                <td>{{ formatDate(admin.created_at) }}</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn btn-sm btn-primary" @click="editAdmin(admin)" title="编辑管理员">
                                                            <i class="icon">✏️</i> 编辑
                                                        </button>
                                                        <button class="btn btn-sm btn-warning" @click="toggleAdminStatus(admin)" 
                                                                :title="admin.status === 1 ? '禁用管理员' : '启用管理员'">
                                                            <i class="icon">{{ admin.status === 1 ? '⏸️' : '▶️' }}</i>
                                                            {{ admin.status === 1 ? '禁用' : '启用' }}
                                                        </button>
                                                        <button class="btn btn-sm btn-danger" @click="deleteAdmin(admin)" 
                                                                :disabled="admin.is_origin === 1" title="删除管理员"
                                                                :class="{ 'btn-disabled': admin.is_origin === 1 }">
                                                            <i class="icon">🗑️</i> 删除
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 管理员新增模态框 -->
                    <div v-show="currentPage === 'permissions-admin'">
                        <div v-if="showAddAdminModal" class="modal-overlay" @click.self="showAddAdminModal = false">
                            <div class="modal-dialog">
                                <div class="modal-header">
                                    <h3 class="modal-title">新增管理员</h3>
                                    <button class="modal-close" @click="showAddAdminModal = false">×</button>
                                </div>
                                <div class="modal-body">
                                    <form class="admin-form">
                                        <div class="form-group">
                                            <label class="form-label">昵称 <span class="required">*</span></label>
                                            <input type="text" class="form-control" v-model="adminForm.nickname" placeholder="请输入管理员昵称">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">登录账号 <span class="required">*</span></label>
                                            <input type="text" class="form-control" v-model="adminForm.username" placeholder="请输入登录账号">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">登录密码 <span class="required">*</span></label>
                                            <input type="password" class="form-control" v-model="adminForm.password" placeholder="请输入登录密码">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">确认密码 <span class="required">*</span></label>
                                            <input type="password" class="form-control" v-model="adminForm.password_confirm" placeholder="请再次输入密码">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">所属角色 <span class="required">*</span></label>
                                            <select class="form-control" v-model="adminForm.role_id">
                                                <option value="">请选择角色</option>
                                                <option v-for="role in roleList" :key="role.id" :value="role.id">{{ role.role_name }}</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">头像URL</label>
                                            <input type="url" class="form-control" v-model="adminForm.avatar" placeholder="请输入头像链接(可选)">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">状态</label>
                                            <div class="radio-group">
                                                <label class="radio-item">
                                                    <input type="radio" name="adminStatus" :value="1" v-model="adminForm.status">
                                                    <span class="radio-label">正常</span>
                                                </label>
                                                <label class="radio-item">
                                                    <input type="radio" name="adminStatus" :value="0" v-model="adminForm.status">
                                                    <span class="radio-label">禁用</span>
                                                </label>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button class="btn btn-secondary" @click="showAddAdminModal = false">取消</button>
                                    <button class="btn btn-primary" @click="saveAdmin">
                                        <i class="icon">💾</i> 保存管理员
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 管理员编辑模态框 -->
                        <div v-if="showEditAdminModal" class="modal-overlay" @click.self="showEditAdminModal = false">
                            <div class="modal-dialog">
                                <div class="modal-header">
                                    <h3 class="modal-title">编辑管理员</h3>
                                    <button class="modal-close" @click="showEditAdminModal = false">×</button>
                                </div>
                                <div class="modal-body">
                                    <form class="admin-form">
                                        <div class="form-group">
                                            <label class="form-label">昵称 <span class="required">*</span></label>
                                            <input type="text" class="form-control" v-model="adminForm.nickname" placeholder="请输入管理员昵称">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">登录账号 <span class="required">*</span></label>
                                            <input type="text" class="form-control" v-model="adminForm.username" placeholder="请输入登录账号">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">登录密码</label>
                                            <input type="password" class="form-control" v-model="adminForm.password" placeholder="留空则不修改密码">
                                            <small class="form-hint">留空则不修改原密码</small>
                                        </div>
                                        <div class="form-group" v-if="adminForm.password">
                                            <label class="form-label">确认密码 <span class="required">*</span></label>
                                            <input type="password" class="form-control" v-model="adminForm.password_confirm" placeholder="请再次输入密码">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">所属角色 <span class="required">*</span></label>
                                            <select class="form-control" v-model="adminForm.role_id">
                                                <option value="">请选择角色</option>
                                                <option v-for="role in roleList" :key="role.id" :value="role.id">{{ role.role_name }}</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">头像URL</label>
                                            <input type="url" class="form-control" v-model="adminForm.avatar" placeholder="请输入头像链接(可选)">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">状态</label>
                                            <div class="radio-group">
                                                <label class="radio-item">
                                                    <input type="radio" name="editAdminStatus" :value="1" v-model="adminForm.status">
                                                    <span class="radio-label">正常</span>
                                                </label>
                                                <label class="radio-item">
                                                    <input type="radio" name="editAdminStatus" :value="0" v-model="adminForm.status">
                                                    <span class="radio-label">禁用</span>
                                                </label>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button class="btn btn-secondary" @click="showEditAdminModal = false">取消</button>
                                    <button class="btn btn-primary" @click="saveAdmin">
                                        <i class="icon">💾</i> 保存修改
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 其他页面占位 -->
                    <div v-else class="page-container">
                        <div class="page-header">
                            <h1 class="page-title">{{ getCurrentPageTitle() }}</h1>
                            <p class="page-description">功能开发中...</p>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <div class="loading">
                                    <div class="loading-spinner"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive, computed, onMounted, nextTick } = Vue;

        createApp({
            setup() {
                // 认证状态
                const isLoggedIn = ref(false);
                const currentAuthTab = ref('login');
                const loginLoading = ref(false);
                const registerLoading = ref(false);
                
                // 登录表单数据
                const loginForm = reactive({
                    username: '',
                    password: '',
                    captcha: '',
                    remember: false
                });

                // 注册表单数据
                const registerForm = reactive({
                    companyName: '',
                    contactName: '',
                    phone: '',
                    smsCode: '',
                    password: '',
                    confirmPassword: '',
                    agreement: false
                });

                // 表单验证错误信息
                const loginErrors = reactive({
                    username: '',
                    password: '',
                    captcha: ''
                });

                const registerErrors = reactive({
                    companyName: '',
                    contactName: '',
                    phone: '',
                    smsCode: '',
                    password: '',
                    confirmPassword: '',
                    agreement: ''
                });

                // 界面状态
                const showLoginPassword = ref(false);
                const showRegisterPassword = ref(false);
                const showConfirmPassword = ref(false);
                const smsCountdown = ref(0);
                const captchaText = ref('ABCD');

                // 密码强度
                const passwordStrength = reactive({
                    level: '',
                    text: '弱'
                });

                // 用户信息
                const currentUser = reactive({
                    name: '企业管理员',
                    role: 'admin'
                });

                // 界面状态
                const sidebarCollapsed = ref(false);
                const currentPage = ref('dashboard');
                const showUserMenu = ref(false);
                const chartTimeRange = ref('month');
                const showPermissionsMenu = ref(false);

                // 通知
                const notifications = reactive({
                    unread: 3
                });

                // 门店统计
                const shopStats = reactive({
                    total: 8,
                    online: 6,
                    offline: 2
                });

                // 仪表盘数据
                const dashboardData = reactive({
                    totalShops: 8,
                    onlineShops: 6,
                    newShopsThisMonth: 2,
                    todayRevenue: 125680,
                    revenueTarget: 150000,
                    revenueGrowth: 12.5,
                    todayOrders: 342,
                    successOrders: 320,
                    cancelledOrders: 22,
                    orderGrowth: 8.3,
                    stockAlerts: 15,
                    stockImprovement: 3,
                    activeMembers: 12680,
                    memberGrowthRate: 15.2,
                    newMembers: 186,
                    systemAlerts: 5,
                    highPriorityAlerts: 2,
                    resolvedAlerts: 8
                });


                // 图表数据
                const paymentData = reactive({
                    wechat: 45.2,
                    alipay: 32.8,
                    cash: 15.6,
                    card: 6.4
                });

                const hotProducts = ref([
                    { name: '招牌炒饭', sales: 486, revenue: 24300 },
                    { name: '宫保鸡丁', sales: 372, revenue: 18600 },
                    { name: '麻婆豆腐', sales: 295, revenue: 11800 },
                    { name: '糖醋里脊', sales: 258, revenue: 15480 },
                    { name: '鱼香肉丝', sales: 234, revenue: 14040 },
                    { name: '回锅肉', sales: 187, revenue: 11220 },
                    { name: '青椒肉丝', sales: 156, revenue: 7800 },
                    { name: '土豆丝', sales: 134, revenue: 5360 }
                ]);


                // 门店列表
                const shopList = ref([
                    {
                        id: 1,
                        shop_name: '旗舰店',
                        manager_name: '张经理',
                        mobile: '***********',
                        password: '******',
                        address: '北京市朝阳区建国门外大街1号',
                        business_hours: {
                            monday: { open: '09:00', close: '22:00', closed: false },
                            tuesday: { open: '09:00', close: '22:00', closed: false },
                            wednesday: { open: '09:00', close: '22:00', closed: false },
                            thursday: { open: '09:00', close: '22:00', closed: false },
                            friday: { open: '09:00', close: '22:00', closed: false },
                            saturday: { open: '09:00', close: '23:00', closed: false },
                            sunday: { open: '10:00', close: '21:00', closed: false }
                        },
                        service_phone: '010-12345678',
                        latitude: '39.906908',
                        longitude: '116.397128',
                        status: 1,
                        remark: '旗舰门店，位于CBD核心区域',
                        company_id: 1,
                        created_at: '2024-01-15 10:00:00',
                        updated_at: '2024-01-15 10:00:00',
                        // 运营数据字段（用于统计显示）
                        todayRevenue: 35600,
                        todayOrders: 89,
                        avgPrice: 400,
                        profit: 12460
                    },
                    {
                        id: 2,
                        shop_name: '中关村店',
                        manager_name: '李经理',
                        mobile: '***********',
                        password: '******',
                        address: '北京市海淀区中关村大街59号',
                        business_hours: {
                            monday: { open: '08:30', close: '21:30', closed: false },
                            tuesday: { open: '08:30', close: '21:30', closed: false },
                            wednesday: { open: '08:30', close: '21:30', closed: false },
                            thursday: { open: '08:30', close: '21:30', closed: false },
                            friday: { open: '08:30', close: '22:00', closed: false },
                            saturday: { open: '08:30', close: '22:30', closed: false },
                            sunday: { open: '09:00', close: '21:00', closed: false }
                        },
                        service_phone: '010-87654321',
                        latitude: '39.983780',
                        longitude: '116.318622',
                        status: 1,
                        remark: '科技园区店，主要服务周边白领',
                        company_id: 1,
                        created_at: '2024-02-20 14:30:00',
                        updated_at: '2024-02-20 14:30:00',
                        // 运营数据字段（用于统计显示）
                        todayRevenue: 28900,
                        todayOrders: 76,
                        avgPrice: 380,
                        profit: 9815
                    },
                    {
                        id: 3,
                        shop_name: '王府井店',
                        manager_name: '王经理',
                        mobile: '***********',
                        password: '******',
                        address: '北京市东城区王府井大街255号',
                        business_hours: {
                            monday: { open: '10:00', close: '22:00', closed: false },
                            tuesday: { open: '10:00', close: '22:00', closed: false },
                            wednesday: { open: '10:00', close: '22:00', closed: false },
                            thursday: { open: '10:00', close: '22:00', closed: false },
                            friday: { open: '10:00', close: '23:00', closed: false },
                            saturday: { open: '09:30', close: '23:30', closed: false },
                            sunday: { open: '09:30', close: '22:30', closed: false }
                        },
                        service_phone: '010-11223344',
                        latitude: '39.909989',
                        longitude: '116.417570',
                        status: 1,
                        remark: '商业街旗舰店，客流量大',
                        company_id: 1,
                        created_at: '2024-03-10 09:15:00',
                        updated_at: '2024-03-10 09:15:00',
                        // 运营数据字段（用于统计显示）
                        todayRevenue: 24580,
                        todayOrders: 62,
                        avgPrice: 396,
                        profit: 8603
                    },
                    {
                        id: 4,
                        shop_name: '三里屯店',
                        manager_name: '赵经理',
                        mobile: '***********',
                        password: '******',
                        address: '北京市朝阳区三里屯路19号',
                        business_hours: {
                            monday: { open: '11:00', close: '02:00', closed: false },
                            tuesday: { open: '11:00', close: '02:00', closed: false },
                            wednesday: { open: '11:00', close: '02:00', closed: false },
                            thursday: { open: '11:00', close: '02:00', closed: false },
                            friday: { open: '11:00', close: '03:00', closed: false },
                            saturday: { open: '11:00', close: '03:00', closed: false },
                            sunday: { open: '11:00', close: '24:00', closed: false }
                        },
                        service_phone: '010-55667788',
                        latitude: '39.936580',
                        longitude: '116.447850',
                        status: 1,
                        remark: '夜场营业，主打夜宵服务',
                        company_id: 1,
                        created_at: '2024-04-05 16:20:00',
                        updated_at: '2024-04-05 16:20:00',
                        // 运营数据字段（用于统计显示）
                        todayRevenue: 19240,
                        todayOrders: 51,
                        avgPrice: 377,
                        profit: 6734
                    },
                    {
                        id: 5,
                        shop_name: '望京店',
                        manager_name: '陈经理',
                        mobile: '***********',
                        password: '******',
                        address: '北京市朝阳区望京SOHO T1座',
                        business_hours: {
                            monday: { open: '09:00', close: '21:00', closed: true },
                            tuesday: { open: '09:00', close: '21:00', closed: true },
                            wednesday: { open: '09:00', close: '21:00', closed: true },
                            thursday: { open: '09:00', close: '21:00', closed: true },
                            friday: { open: '09:00', close: '21:00', closed: true },
                            saturday: { open: '09:00', close: '21:00', closed: true },
                            sunday: { open: '09:00', close: '21:00', closed: true }
                        },
                        service_phone: '010-99887766',
                        latitude: '39.998210',
                        longitude: '116.470310',
                        status: 0,
                        remark: '装修升级中，暂停营业',
                        company_id: 1,
                        created_at: '2024-05-12 11:45:00',
                        updated_at: '2024-05-12 11:45:00',
                        // 运营数据字段（用于统计显示）
                        todayRevenue: 0,
                        todayOrders: 0,
                        avgPrice: 0,
                        profit: 0
                    }
                ]);

                // 时间范围切换
                const switchTimeRange = (range) => {
                    chartTimeRange.value = range;
                    // 重新初始化图表
                    nextTick(() => {
                        initRevenueChart();
                    });
                };

                // 门店排序
                const sortShopsByRevenue = () => {
                    if (typeof shopList !== 'undefined' && shopList && shopList.value) {
                        shopList.value.sort((a, b) => b.todayRevenue - a.todayRevenue);
                    }
                    nextTick(() => {
                        initShopCompareChart();
                    });
                };

                // 门店管理相关响应式变量
                const showAddShopModal = ref(false);
                const showEditShopModal = ref(false);
                const showShopDetail = ref(false);
                const currentEditShop = ref(null);
                const currentDetailShop = ref(null);

                // 门店表单数据
                const shopForm = ref({
                    id: null,
                    shop_name: '',
                    manager_name: '',
                    mobile: '',
                    password: '',
                    address: '',
                    business_hours: {
                        monday: { open: '09:00', close: '22:00', closed: false },
                        tuesday: { open: '09:00', close: '22:00', closed: false },
                        wednesday: { open: '09:00', close: '22:00', closed: false },  
                        thursday: { open: '09:00', close: '22:00', closed: false },
                        friday: { open: '09:00', close: '22:00', closed: false },
                        saturday: { open: '09:00', close: '22:00', closed: false },
                        sunday: { open: '09:00', close: '22:00', closed: false }
                    },
                    service_phone: '',
                    latitude: '',
                    longitude: '',
                    status: 1,
                    remark: '',
                    company_id: 1
                });

                // 表单Tab状态
                const activeFormTab = ref('basic'); // 'basic' | 'hours'

                // 角色管理相关变量
                const showAddRoleModal = ref(false);
                const showEditRoleModal = ref(false);
                const showPermissionsModal = ref(false);
                const currentRole = ref(null);

                // 角色表单数据
                const roleForm = ref({
                    id: null,
                    role_name: ''
                });

                // 角色列表数据
                const roleList = ref([
                    {
                        id: 1,
                        role_name: '超级管理员',
                        created_at: '2024-01-15 10:00:00',
                        updated_at: '2024-01-15 10:00:00'
                    },
                    {
                        id: 2,
                        role_name: '店长',
                        created_at: '2024-02-10 14:30:00',
                        updated_at: '2024-02-10 14:30:00'
                    },
                    {
                        id: 3,
                        role_name: '收银员',
                        created_at: '2024-03-05 09:15:00',
                        updated_at: '2024-03-05 09:15:00'
                    },
                    {
                        id: 4,
                        role_name: '服务员',
                        created_at: '2024-03-20 16:20:00',
                        updated_at: '2024-03-20 16:20:00'
                    }
                ]);

                // 管理员模态框状态
                const showAddAdminModal = ref(false);
                const showEditAdminModal = ref(false);

                // 管理员表单数据
                const adminForm = reactive({
                    id: null,
                    nickname: '',
                    username: '',
                    password: '',
                    password_confirm: '',
                    avatar: '',
                    role_id: '',
                    status: 1
                });

                // 管理员列表数据
                const adminList = ref([
                    {
                        id: 1,
                        nickname: '系统管理员',
                        username: 'admin',
                        avatar: '',
                        role_id: 1,
                        status: 1,
                        login_ip: '*************',
                        login_time: '2024-07-30 09:30:00',
                        created_at: '2024-01-01 00:00:00',
                        updated_at: '2024-07-30 09:30:00',
                        is_origin: 1
                    },
                    {
                        id: 2,
                        nickname: '店长小李',
                        username: 'manager_li',
                        avatar: '',
                        role_id: 2,
                        status: 1,
                        login_ip: '*************',
                        login_time: '2024-07-29 18:45:00',
                        created_at: '2024-01-15 10:00:00',
                        updated_at: '2024-07-29 18:45:00',
                        is_origin: 0
                    },
                    {
                        id: 3,
                        nickname: '收银员小王',
                        username: 'cashier_wang',
                        avatar: '',
                        role_id: 3,
                        status: 1,
                        login_ip: '*************',
                        login_time: '2024-07-30 08:15:00',
                        created_at: '2024-03-05 14:30:00',
                        updated_at: '2024-07-30 08:15:00',
                        is_origin: 0
                    },
                    {
                        id: 4,
                        nickname: '服务员小张',
                        username: 'server_zhang',
                        avatar: '',
                        role_id: 4,
                        status: 0,
                        login_ip: '*************',
                        login_time: '2024-07-28 22:30:00',
                        created_at: '2024-03-20 16:20:00',
                        updated_at: '2024-07-28 22:30:00',
                        is_origin: 0
                    }
                ]);

                // 权限组数据
                const permissionGroups = ref([
                    {
                        name: '门店管理',
                        permissions: [
                            { id: 'shop_view', name: '查看门店' },
                            { id: 'shop_add', name: '新增门店' },
                            { id: 'shop_edit', name: '编辑门店' },
                            { id: 'shop_delete', name: '删除门店' }
                        ]
                    },
                    {
                        name: '用户管理',
                        permissions: [
                            { id: 'user_view', name: '查看用户' },
                            { id: 'user_add', name: '新增用户' },
                            { id: 'user_edit', name: '编辑用户' },
                            { id: 'user_delete', name: '删除用户' }
                        ]
                    },
                    {
                        name: '权限管理',
                        permissions: [
                            { id: 'role_view', name: '查看角色' },
                            { id: 'role_add', name: '新增角色' },
                            { id: 'role_edit', name: '编辑角色' },
                            { id: 'role_delete', name: '删除角色' },
                            { id: 'permission_manage', name: '权限管理' }
                        ]
                    },
                    {
                        name: '系统管理',
                        permissions: [
                            { id: 'system_config', name: '系统配置' },
                            { id: 'log_view', name: '查看日志' },
                            { id: 'backup_manage', name: '备份管理' }
                        ]
                    }
                ]);

                // 选中的权限
                const selectedPermissions = ref([]);

                const editShop = (shop) => {
                    currentEditShop.value = { ...shop };
                    shopForm.value = { ...shop };
                    showEditShopModal.value = true;
                };

                const viewShopDetail = (shop) => {
                    currentDetailShop.value = shop;
                    showShopDetail.value = true;
                };

                const toggleShopStatus = (shop) => {
                    const newStatus = shop.status === 1 ? 0 : 1;
                    const action = newStatus === 1 ? '启用' : '暂停';
                    
                    if (confirm(`确定要${action}门店"${shop.shop_name}"吗？`)) {
                        shop.status = newStatus;
                        // 这里应该调用API更新状态
                        console.log(`${action}门店:`, shop.shop_name);
                    }
                };

                const deleteShop = (shop) => {
                    if (confirm(`确定要删除门店"${shop.shop_name}"吗？此操作不可恢复！`)) {
                        const index = shopList.value.findIndex(s => s.id === shop.id);
                        if (index > -1) {
                            shopList.value.splice(index, 1);
                            // 这里应该调用API删除门店
                            console.log('删除门店:', shop.shop_name);
                        }
                    }
                };

                const saveShop = () => {
                    // 验证表单
                    if (!shopForm.value.shop_name.trim()) {
                        alert('请输入门店名称');
                        return;
                    }
                    if (!shopForm.value.manager_name.trim()) {
                        alert('请输入门店负责人');
                        return;
                    }
                    if (!shopForm.value.mobile.trim()) {
                        alert('请输入登录手机号');
                        return;
                    }
                    if (!shopForm.value.address.trim()) {
                        alert('请输入门店地址');
                        return;
                    }

                    if (shopForm.value.id) {
                        // 编辑模式
                        const index = shopList.value.findIndex(s => s.id === shopForm.value.id);
                        if (index > -1) {
                            shopList.value[index] = { 
                                ...shopForm.value,
                                updated_at: new Date().toISOString().substring(0, 19)
                            };
                        }
                        showEditShopModal.value = false;
                    } else {
                        // 新增模式
                        const newShop = {
                            ...shopForm.value,
                            id: Math.max(...shopList.value.map(s => s.id)) + 1,
                            created_at: new Date().toISOString().substring(0, 19),
                            updated_at: new Date().toISOString().substring(0, 19)
                        };
                        shopList.value.push(newShop);
                        showAddShopModal.value = false;
                    }

                    // 重置表单
                    resetShopForm();
                };

                const resetShopForm = () => {
                    shopForm.value = {
                        id: null,
                        shop_name: '',
                        manager_name: '',
                        mobile: '',
                        password: '',
                        address: '',
                        business_hours: {
                            monday: { open: '09:00', close: '22:00', closed: false },
                            tuesday: { open: '09:00', close: '22:00', closed: false },
                            wednesday: { open: '09:00', close: '22:00', closed: false },
                            thursday: { open: '09:00', close: '22:00', closed: false },
                            friday: { open: '09:00', close: '22:00', closed: false },
                            saturday: { open: '09:00', close: '22:00', closed: false },
                            sunday: { open: '09:00', close: '22:00', closed: false }
                        },
                        service_phone: '',
                        latitude: '',
                        longitude: '',
                        status: 1,
                        remark: '',
                        company_id: 1
                    };
                    activeFormTab.value = 'basic';
                };

                // 获取今日营业时间
                const getTodayBusinessHours = (businessHours) => {
                    if (!businessHours) return '';
                    
                    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
                    const today = days[new Date().getDay()];
                    const todayHours = businessHours[today];
                    
                    if (!todayHours || todayHours.closed) {
                        return '';
                    }
                    
                    return `${todayHours.open}-${todayHours.close}`;
                };

                // 格式化日期
                const formatDate = (dateString) => {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    return date.toLocaleDateString();
                };

                // 获取星期中文标签
                const getDayLabel = (day) => {
                    const dayLabels = {
                        monday: '周一',
                        tuesday: '周二',
                        wednesday: '周三',
                        thursday: '周四',
                        friday: '周五',
                        saturday: '周六',
                        sunday: '周日'
                    };
                    return dayLabels[day] || day;
                };

                // 选择地理位置
                const selectLocation = () => {
                    // 这里可以集成地图选择组件
                    alert('地图选择功能待实现，可以集成百度地图或高德地图API');
                };

                // 角色管理方法
                const togglePermissionsMenu = () => {
                    showPermissionsMenu.value = !showPermissionsMenu.value;
                };

                const editRole = (role) => {
                    currentRole.value = { ...role };
                    roleForm.value = { ...role };
                    showEditRoleModal.value = true;
                };

                const deleteRole = (role) => {
                    if (confirm(`确定要删除角色"${role.role_name}"吗？此操作不可恢复！`)) {
                        const index = roleList.value.findIndex(r => r.id === role.id);
                        if (index > -1) {
                            roleList.value.splice(index, 1);
                            console.log('删除角色:', role.role_name);
                        }
                    }
                };

                const saveRole = () => {
                    // 验证表单
                    if (!roleForm.value.role_name.trim()) {
                        alert('请输入角色名称');
                        return;
                    }

                    if (roleForm.value.id) {
                        // 编辑模式
                        const index = roleList.value.findIndex(r => r.id === roleForm.value.id);
                        if (index > -1) {
                            roleList.value[index] = { 
                                ...roleForm.value,
                                updated_at: new Date().toISOString().substring(0, 19).replace('T', ' ')
                            };
                        }
                        showEditRoleModal.value = false;
                    } else {
                        // 新增模式
                        const newRole = {
                            ...roleForm.value,
                            id: Math.max(...roleList.value.map(r => r.id)) + 1,
                            created_at: new Date().toISOString().substring(0, 19).replace('T', ' '),
                            updated_at: new Date().toISOString().substring(0, 19).replace('T', ' ')
                        };
                        roleList.value.push(newRole);
                        showAddRoleModal.value = false;
                    }

                    // 重置表单
                    resetRoleForm();
                };

                const resetRoleForm = () => {
                    roleForm.value = {
                        id: null,
                        role_name: ''
                    };
                };

                const managePermissions = (role) => {
                    currentRole.value = role;
                    // 这里可以从服务器加载该角色的权限
                    selectedPermissions.value = ['shop_view', 'user_view', 'role_view']; // 示例权限
                    showPermissionsModal.value = true;
                };

                const isGroupAllSelected = (group) => {
                    return group.permissions.every(permission => 
                        selectedPermissions.value.includes(permission.id)
                    );
                };

                const toggleGroupPermissions = (group, checked) => {
                    if (checked) {
                        // 添加该组所有权限
                        group.permissions.forEach(permission => {
                            if (!selectedPermissions.value.includes(permission.id)) {
                                selectedPermissions.value.push(permission.id);
                            }
                        });
                    } else {
                        // 移除该组所有权限
                        group.permissions.forEach(permission => {
                            const index = selectedPermissions.value.indexOf(permission.id);
                            if (index > -1) {
                                selectedPermissions.value.splice(index, 1);
                            }
                        });
                    }
                };

                const savePermissions = () => {
                    console.log('保存权限:', currentRole.value.role_name, selectedPermissions.value);
                    showPermissionsModal.value = false;
                    alert(`已为角色"${currentRole.value.role_name}"保存权限设置`);
                };

                // 管理员管理方法
                const refreshAdminList = () => {
                    console.log('刷新管理员列表');
                    // 这里可以从服务器重新加载管理员数据
                };

                const editAdmin = (admin) => {
                    adminForm.id = admin.id;
                    adminForm.nickname = admin.nickname;
                    adminForm.username = admin.username;
                    adminForm.password = '';
                    adminForm.password_confirm = '';
                    adminForm.avatar = admin.avatar || '';
                    adminForm.role_id = admin.role_id;
                    adminForm.status = admin.status;
                    showEditAdminModal.value = true;
                };

                const deleteAdmin = (admin) => {
                    if (admin.is_origin === 1) {
                        alert('系统原始管理员不能删除！');
                        return;
                    }
                    if (confirm(`确定要删除管理员"${admin.nickname}"吗？此操作不可恢复！`)) {
                        const index = adminList.value.findIndex(a => a.id === admin.id);
                        if (index > -1) {
                            adminList.value.splice(index, 1);
                            console.log('删除管理员:', admin.nickname);
                            alert('管理员删除成功！');
                        }
                    }
                };

                const toggleAdminStatus = (admin) => {
                    if (admin.is_origin === 1) {
                        alert('系统原始管理员状态不能修改！');
                        return;
                    }
                    const newStatus = admin.status === 1 ? 0 : 1;
                    const statusText = newStatus === 1 ? '启用' : '禁用';
                    if (confirm(`确定要${statusText}管理员"${admin.nickname}"吗？`)) {
                        admin.status = newStatus;
                        admin.updated_at = new Date().toISOString().substring(0, 19).replace('T', ' ');
                        console.log(`${statusText}管理员:`, admin.nickname);
                        alert(`管理员${statusText}成功！`);
                    }
                };

                const saveAdmin = () => {
                    // 表单验证
                    if (!adminForm.nickname.trim()) {
                        alert('请输入管理员昵称');
                        return;
                    }
                    if (!adminForm.username.trim()) {
                        alert('请输入登录账号');
                        return;
                    }
                    if (!adminForm.role_id) {
                        alert('请选择所属角色');
                        return;
                    }

                    // 新增模式需要验证密码
                    if (!adminForm.id) {
                        if (!adminForm.password) {
                            alert('请输入登录密码');
                            return;
                        }
                        if (adminForm.password !== adminForm.password_confirm) {
                            alert('两次输入的密码不一致');
                            return;
                        }
                        if (adminForm.password.length < 6) {
                            alert('密码长度至少6位');
                            return;
                        }
                    } else {
                        // 编辑模式，如果输入了密码需要验证确认密码
                        if (adminForm.password && adminForm.password !== adminForm.password_confirm) {
                            alert('两次输入的密码不一致');
                            return;
                        }
                        if (adminForm.password && adminForm.password.length < 6) {
                            alert('密码长度至少6位');
                            return;
                        }
                    }

                    // 检查用户名是否重复
                    const existingAdmin = adminList.value.find(admin => 
                        admin.username === adminForm.username && admin.id !== adminForm.id
                    );
                    if (existingAdmin) {
                        alert('登录账号已存在，请更换');
                        return;
                    }

                    if (adminForm.id) {
                        // 编辑模式
                        const index = adminList.value.findIndex(a => a.id === adminForm.id);
                        if (index > -1) {
                            adminList.value[index] = { 
                                ...adminList.value[index],
                                nickname: adminForm.nickname,
                                username: adminForm.username,
                                avatar: adminForm.avatar,
                                role_id: adminForm.role_id,
                                status: adminForm.status,
                                updated_at: new Date().toISOString().substring(0, 19).replace('T', ' ')
                            };
                            // 如果修改了密码，这里应该重新设置密码
                            if (adminForm.password) {
                                console.log('更新密码:', adminForm.password);
                            }
                        }
                        showEditAdminModal.value = false;
                        alert('管理员信息修改成功！');
                    } else {
                        // 新增模式
                        const newAdmin = {
                            id: Math.max(...adminList.value.map(a => a.id)) + 1,
                            nickname: adminForm.nickname,
                            username: adminForm.username,
                            avatar: adminForm.avatar,
                            role_id: adminForm.role_id,
                            status: adminForm.status,
                            login_ip: '',
                            login_time: null,
                            created_at: new Date().toISOString().substring(0, 19).replace('T', ' '),
                            updated_at: new Date().toISOString().substring(0, 19).replace('T', ' '),
                            is_origin: 0
                        };
                        adminList.value.push(newAdmin);
                        showAddAdminModal.value = false;
                        alert('管理员添加成功！');
                        console.log('新增管理员:', newAdmin);
                    }

                    // 重置表单
                    resetAdminForm();
                };

                const resetAdminForm = () => {
                    adminForm.id = null;
                    adminForm.nickname = '';
                    adminForm.username = '';
                    adminForm.password = '';
                    adminForm.password_confirm = '';
                    adminForm.avatar = '';
                    adminForm.role_id = '';
                    adminForm.status = 1;
                };

                const getRoleName = (roleId) => {
                    const role = roleList.value.find(r => r.id === roleId);
                    return role ? role.role_name : '未知角色';
                };

                // 切换认证标签页
                const switchAuthTab = (tab) => {
                    currentAuthTab.value = tab;
                    // 清除表单错误
                    Object.keys(loginErrors).forEach(key => loginErrors[key] = '');
                    Object.keys(registerErrors).forEach(key => registerErrors[key] = '');
                };

                // 表单验证函数
                const validateLoginUsername = () => {
                    if (!loginForm.username.trim()) {
                        loginErrors.username = '请输入企业账号';
                        return false;
                    }
                    if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]{2,20}$|^1[3-9]\d{9}$/.test(loginForm.username)) {
                        loginErrors.username = '请输入有效的账号或手机号';
                        return false;
                    }
                    loginErrors.username = '';
                    return true;
                };

                const validateLoginPassword = () => {
                    if (!loginForm.password) {
                        loginErrors.password = '请输入登录密码';
                        return false;
                    }
                    if (loginForm.password.length < 6) {
                        loginErrors.password = '密码长度不能少于6位';
                        return false;
                    }
                    loginErrors.password = '';
                    return true;
                };

                const validateCaptcha = () => {
                    if (!loginForm.captcha) {
                        loginErrors.captcha = '请输入验证码';
                        return false;
                    }
                    if (loginForm.captcha.toLowerCase() !== captchaText.value.toLowerCase()) {
                        loginErrors.captcha = '验证码错误';
                        return false;
                    }
                    loginErrors.captcha = '';
                    return true;
                };

                const validateCompanyName = () => {
                    if (!registerForm.companyName.trim()) {
                        registerErrors.companyName = '请输入企业名称';
                        return false;
                    }
                    if (registerForm.companyName.length < 2) {
                        registerErrors.companyName = '企业名称至少2个字符';
                        return false;
                    }
                    registerErrors.companyName = '';
                    return true;
                };

                const validateContactName = () => {
                    if (!registerForm.contactName.trim()) {
                        registerErrors.contactName = '请输入联系人姓名';
                        return false;
                    }
                    if (!/^[\u4e00-\u9fa5a-zA-Z\s]{2,10}$/.test(registerForm.contactName)) {
                        registerErrors.contactName = '请输入有效的姓名';
                        return false;
                    }
                    registerErrors.contactName = '';
                    return true;
                };

                const validatePhone = () => {
                    if (!registerForm.phone) {
                        registerErrors.phone = '请输入手机号码';
                        return false;
                    }
                    if (!/^1[3-9]\d{9}$/.test(registerForm.phone)) {
                        registerErrors.phone = '请输入有效的手机号码';
                        return false;
                    }
                    registerErrors.phone = '';
                    return true;
                };

                const validateSmsCode = () => {
                    if (!registerForm.smsCode) {
                        registerErrors.smsCode = '请输入短信验证码';
                        return false;
                    }
                    if (!/^\d{6}$/.test(registerForm.smsCode)) {
                        registerErrors.smsCode = '请输入6位数字验证码';
                        return false;
                    }
                    registerErrors.smsCode = '';
                    return true;
                };

                const validateRegisterPassword = () => {
                    if (!registerForm.password) {
                        registerErrors.password = '请输入密码';
                        return false;
                    }
                    if (registerForm.password.length < 8) {
                        registerErrors.password = '密码长度不能少于8位';
                        return false;
                    }
                    if (!/^(?=.*[a-zA-Z])(?=.*\d)/.test(registerForm.password)) {
                        registerErrors.password = '密码必须包含字母和数字';
                        return false;
                    }
                    registerErrors.password = '';
                    return true;
                };

                const validateConfirmPassword = () => {
                    if (!registerForm.confirmPassword) {
                        registerErrors.confirmPassword = '请确认密码';
                        return false;
                    }
                    if (registerForm.confirmPassword !== registerForm.password) {
                        registerErrors.confirmPassword = '两次输入的密码不一致';
                        return false;
                    }
                    registerErrors.confirmPassword = '';
                    return true;
                };

                const validateAgreement = () => {
                    if (!registerForm.agreement) {
                        registerErrors.agreement = '请同意用户协议和隐私政策';
                        return false;
                    }
                    registerErrors.agreement = '';
                    return true;
                };

                // 检查密码强度
                const checkPasswordStrength = () => {
                    const password = registerForm.password;
                    let score = 0;
                    
                    if (password.length >= 8) score++;
                    if (/[a-z]/.test(password)) score++;
                    if (/[A-Z]/.test(password)) score++;
                    if (/\d/.test(password)) score++;
                    if (/[^a-zA-Z\d]/.test(password)) score++;
                    
                    if (score <= 2) {
                        passwordStrength.level = 'weak';
                        passwordStrength.text = '弱';
                    } else if (score <= 3) {
                        passwordStrength.level = 'medium';
                        passwordStrength.text = '中';
                    } else {
                        passwordStrength.level = 'strong';
                        passwordStrength.text = '强';
                    }
                };

                // 刷新验证码
                const refreshCaptcha = () => {
                    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                    let result = '';
                    for (let i = 0; i < 4; i++) {
                        result += chars.charAt(Math.floor(Math.random() * chars.length));
                    }
                    captchaText.value = result;
                };

                // 发送短信验证码
                const sendSmsCode = () => {
                    if (!validatePhone()) return;
                    
                    smsCountdown.value = 60;
                    const timer = setInterval(() => {
                        smsCountdown.value--;
                        if (smsCountdown.value <= 0) {
                            clearInterval(timer);
                        }
                    }, 1000);
                    
                    // 这里应该调用实际的短信发送API
                    console.log('发送验证码到:', registerForm.phone);
                };

                // 处理登录
                const handleLogin = async () => {
                    // 验证所有字段
                    const isUsernameValid = validateLoginUsername();
                    const isPasswordValid = validateLoginPassword();
                    const isCaptchaValid = validateCaptcha();
                    
                    if (!isUsernameValid || !isPasswordValid || !isCaptchaValid) {
                        return;
                    }
                    
                    loginLoading.value = true;
                    
                    try {
                        // 模拟API调用
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        // 这里应该调用实际的登录API
                        console.log('登录信息:', loginForm);
                        
                        // 登录成功后的处理
                        isLoggedIn.value = true;
                        // 初始化图表
                        nextTick(() => {
                            initCharts();
                        });
                        
                    } catch (error) {
                        console.error('登录失败:', error);
                        alert('登录失败，请重试');
                    } finally {
                        loginLoading.value = false;
                    }
                };

                // 处理注册
                const handleRegister = async () => {
                    // 验证所有字段
                    const validations = [
                        validateCompanyName(),
                        validateContactName(),
                        validatePhone(),
                        validateSmsCode(),
                        validateRegisterPassword(),
                        validateConfirmPassword(),
                        validateAgreement()
                    ];
                    
                    if (!validations.every(v => v)) {
                        return;
                    }
                    
                    registerLoading.value = true;
                    
                    try {
                        // 模拟API调用
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        // 这里应该调用实际的注册API
                        console.log('注册信息:', registerForm);
                        
                        // 注册成功后的处理
                        alert('注册成功！请登录');
                        currentAuthTab.value = 'login';
                        
                    } catch (error) {
                        console.error('注册失败:', error);
                        alert('注册失败，请重试');
                    } finally {
                        registerLoading.value = false;
                    }
                };

                // 忘记密码
                const handleForgotPassword = () => {
                    alert('请联系管理员重置密码');
                };

                // 显示协议
                const showAgreement = (type) => {
                    const title = type === 'terms' ? '用户服务协议' : '隐私保护政策';
                    alert(`显示 ${title}`);
                };

                const toggleSidebar = () => {
                    sidebarCollapsed.value = !sidebarCollapsed.value;
                };

                const navigateTo = (page) => {
                    currentPage.value = page;
                    // 页面切换后初始化对应图表
                    nextTick(() => {
                        if (page === 'dashboard') {
                            initRevenueChart();
                        }
                    });
                };

                const getCurrentPageTitle = () => {
                    const pageMap = {
                        dashboard: '数据大屏',
                        shops: '门店管理',
                        permissions: '权限管理',
                        'permissions-admin': '管理员管理',
                        'permissions-role': '角色管理'
                    };
                    return pageMap[currentPage.value] || '未知页面';
                };

                const formatNumber = (num) => {
                    if (num === undefined || num === null || isNaN(num)) {
                        return '0';
                    }
                    return Number(num).toLocaleString('zh-CN');
                };

                // 获取门店排名
                const getShopRank = (revenue) => {
                    // 使用默认数据如果shopList不存在
                    const defaultData = [
                        { shop_name: '旗舰店', todayRevenue: 125680 },
                        { shop_name: '商场店', todayRevenue: 98420 },
                        { shop_name: '社区店', todayRevenue: 78650 },
                        { shop_name: '写字楼店', todayRevenue: 65430 },
                        { shop_name: '学校店', todayRevenue: 45280 }
                    ];
                    
                    const data = (typeof shopList !== 'undefined' && shopList && shopList.value) ? shopList.value : defaultData;
                    const sorted = [...data].sort((a, b) => b.todayRevenue - a.todayRevenue);
                    return sorted.findIndex(shop => shop.todayRevenue === revenue) + 1;
                };

                // 获取库存状态文本
                const getStockStatusText = (status) => {
                    const statusMap = {
                        normal: '正常',
                        warning: '预警', 
                        low: '不足'
                    };
                    return statusMap[status] || '未知';
                };

                // 刷新门店数据
                const refreshShopData = () => {
                    console.log('刷新门店数据');
                    // 模拟数据刷新
                };


                // 控制门店
                const controlShop = (shopId) => {
                    console.log('控制门店:', shopId);
                };


                // 图表初始化
                const initCharts = () => {
                    initRevenueChart();
                    initPaymentChart();
                    initShopCompareChart();
                    initProductRankChart();
                };

                const initRevenueChart = () => {
                    const chartDom = document.getElementById('revenueChart');
                    if (!chartDom) return;
                    
                    const myChart = echarts.init(chartDom);
                    
                    // 根据时间范围获取不同数据
                    const getChartData = () => {
                        switch (chartTimeRange.value) {
                            case 'today':
                                return {
                                    xData: ['8:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'],
                                    revenue: [1200, 2800, 4500, 3200, 2100, 5600, 7200, 3800],
                                    orders: [12, 28, 45, 32, 21, 56, 72, 38]
                                };
                            case 'week':
                                return {
                                    xData: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                                    revenue: [18000, 22000, 19000, 25000, 28000, 35000, 32000],
                                    orders: [180, 220, 190, 250, 280, 350, 320]
                                };
                            case 'year':
                                return {
                                    xData: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                                    revenue: [280000, 320000, 290000, 350000, 380000, 420000, 390000, 410000, 440000, 460000, 480000, 500000],
                                    orders: [2800, 3200, 2900, 3500, 3800, 4200, 3900, 4100, 4400, 4600, 4800, 5000]
                                };
                            default: // month
                                return {
                                    xData: ['1日', '5日', '10日', '15日', '20日', '25日', '30日'],
                                    revenue: [28000, 32000, 29000, 35000, 38000, 42000, 39000],
                                    orders: [280, 320, 290, 350, 380, 420, 390]
                                };
                        }
                    };
                    
                    const chartData = getChartData();
                    
                    const option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'cross'
                            }
                        },
                        legend: {
                            data: ['营业额', '利润率'],
                            bottom: 0
                        },
                        grid: {
                            top: 20,
                            left: 60,
                            right: 60,
                            bottom: 50
                        },
                        xAxis: {
                            type: 'category',
                            data: chartData.xData,
                            axisLabel: {
                                color: '#666'
                            }
                        },
                        yAxis: [
                            {
                                type: 'value',
                                name: '营业额(元)',
                                position: 'left',
                                axisLabel: {
                                    color: '#666',
                                    formatter: '{value}'
                                }
                            },
                            {
                                type: 'value',
                                name: '利润率(%)',
                                position: 'right',
                                axisLabel: {
                                    color: '#666',
                                    formatter: '{value}%'
                                }
                            }
                        ],
                        series: [
                            {
                                name: '营业额',
                                type: 'bar',
                                data: chartData.revenue,
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        { offset: 0, color: '#415A77' },
                                        { offset: 1, color: '#778DA9' }
                                    ])
                                },
                                barWidth: '60%'
                            },
                            {
                                name: '利润率',
                                type: 'line',
                                yAxisIndex: 1,
                                data: [35.2, 38.5, 33.8, 40.2, 42.1, 45.6, 41.3, 43.8, 46.2, 48.1, 49.5, 51.2].slice(0, chartData.xData.length),
                                itemStyle: {
                                    color: '#E8B86D'
                                },
                                lineStyle: {
                                    width: 3
                                },
                                symbol: 'circle',
                                symbolSize: 6
                            }
                        ]
                    };
                    myChart.setOption(option);
                };

                const initPaymentChart = () => {
                    const chartDom = document.getElementById('paymentChart');
                    if (!chartDom) return;
                    
                    const myChart = echarts.init(chartDom);
                    const option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c}% ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            right: 20,
                            top: 'center'
                        },
                        series: [
                            {
                                name: '支付方式',
                                type: 'pie',
                                radius: ['40%', '70%'],
                                center: ['40%', '50%'],
                                avoidLabelOverlap: false,
                                itemStyle: {
                                    borderRadius: 5,
                                    borderColor: '#fff',
                                    borderWidth: 2
                                },
                                label: {
                                    show: false,
                                    position: 'center'
                                },
                                emphasis: {
                                    label: {
                                        show: true,
                                        fontSize: '18',
                                        fontWeight: 'bold'
                                    }
                                },
                                labelLine: {
                                    show: false
                                },
                                data: [
                                    { value: paymentData.wechat, name: '微信支付', itemStyle: { color: '#07C160' } },
                                    { value: paymentData.alipay, name: '支付宝', itemStyle: { color: '#1677FF' } },
                                    { value: paymentData.cash, name: '现金', itemStyle: { color: '#FA8C16' } },
                                    { value: paymentData.card, name: '银行卡', itemStyle: { color: '#722ED1' } }
                                ]
                            }
                        ]
                    };
                    myChart.setOption(option);
                };

                const initShopCompareChart = () => {
                    const chartDom = document.getElementById('shopCompareChart');
                    if (!chartDom) return;
                    
                    const myChart = echarts.init(chartDom);
                    
                    // 定义默认门店数据
                    const defaultShopData = [
                        { shop_name: '旗舰店', todayRevenue: 125680 },
                        { shop_name: '商场店', todayRevenue: 98420 },
                        { shop_name: '社区店', todayRevenue: 78650 },
                        { shop_name: '写字楼店', todayRevenue: 65430 },
                        { shop_name: '学校店', todayRevenue: 45280 }
                    ];
                    
                    // 使用shopList数据或默认数据
                    let shopData = defaultShopData;
                    if (typeof shopList !== 'undefined' && shopList && shopList.value && shopList.value.length > 0) {
                        shopData = shopList.value;
                    }
                    
                    const shopNames = shopData.map(shop => shop.shop_name || shop.name || '未知门店');
                    const shopRevenues = shopData.map(shop => shop.todayRevenue || 0);
                    
                    const option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        grid: {
                            top: 20,
                            left: 60,
                            right: 30,
                            bottom: 60
                        },
                        xAxis: {
                            type: 'category',
                            data: shopNames,
                            axisLabel: {
                                rotate: 30,
                                color: '#666'
                            }
                        },
                        yAxis: {
                            type: 'value',
                            name: '营业额(元)',
                            axisLabel: {
                                color: '#666'
                            }
                        },
                        series: [
                            {
                                name: '今日营业额',
                                type: 'bar',
                                data: shopRevenues,
                                itemStyle: {
                                    color: function(params) {
                                        const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399'];
                                        return colors[params.dataIndex % colors.length];
                                    },
                                    borderRadius: [4, 4, 0, 0]
                                },
                                label: {
                                    show: true,
                                    position: 'top',
                                    formatter: '{c}',
                                    color: '#666'
                                },
                                barWidth: '50%'
                            }
                        ]
                    };
                    myChart.setOption(option);
                };

                const initProductRankChart = () => {
                    const chartDom = document.getElementById('productRankChart');
                    if (!chartDom) return;
                    
                    const myChart = echarts.init(chartDom);
                    const productNames = hotProducts.value.map(product => product.name);
                    const productSales = hotProducts.value.map(product => product.sales);
                    
                    const option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                            formatter: function(params) {
                                const dataIndex = params[0].dataIndex;
                                const product = hotProducts.value[dataIndex];
                                return `${product.name}<br/>销量: ${product.sales}份<br/>销售额: ¥${product.revenue}`;
                            }
                        },
                        grid: {
                            top: 20,
                            left: 80,
                            right: 30,
                            bottom: 20
                        },
                        xAxis: {
                            type: 'value',
                            axisLabel: {
                                color: '#666'
                            }
                        },
                        yAxis: {
                            type: 'category',
                            data: productNames,
                            axisLabel: {
                                color: '#666'
                            }
                        },
                        series: [
                            {
                                name: '销量',
                                type: 'bar',
                                data: productSales,
                                itemStyle: {
                                    color: function(params) {
                                        if (params.dataIndex < 3) {
                                            return ['#FFD700', '#C0C0C0', '#CD7F32'][params.dataIndex];
                                        }
                                        return '#409EFF';
                                    },
                                    borderRadius: [0, 4, 4, 0]
                                },
                                label: {
                                    show: true,
                                    position: 'right',
                                    formatter: '{c}份',
                                    color: '#666'
                                },
                                barHeight: 20
                            }
                        ]
                    };
                    myChart.setOption(option);
                };


                // 初始化
                onMounted(() => {
                    refreshCaptcha();
                });

                return {
                    isLoggedIn,
                    currentAuthTab,
                    loginLoading,
                    registerLoading,
                    loginForm,
                    registerForm,
                    loginErrors,
                    registerErrors,
                    showLoginPassword,
                    showRegisterPassword,
                    showConfirmPassword,
                    smsCountdown,
                    captchaText,
                    passwordStrength,
                    currentUser,
                    sidebarCollapsed,
                    currentPage,
                    showUserMenu,
                    showPermissionsMenu,
                    notifications,
                    shopStats,
                    dashboardData,
                    shopList,
                    // 门店管理相关
                    showAddShopModal,
                    showEditShopModal,
                    showShopDetail,
                    currentEditShop,
                    currentDetailShop,
                    shopForm,
                    activeFormTab,
                    editShop,
                    viewShopDetail,
                    toggleShopStatus,
                    deleteShop,
                    saveShop,
                    resetShopForm,
                    getTodayBusinessHours,
                    formatDate,
                    getDayLabel,
                    selectLocation,
                    // 角色管理相关
                    showAddRoleModal,
                    showEditRoleModal,
                    showPermissionsModal,
                    currentRole,
                    roleForm,
                    roleList,
                    permissionGroups,
                    selectedPermissions,
                    togglePermissionsMenu,
                    editRole,
                    deleteRole,
                    saveRole,
                    resetRoleForm,
                    managePermissions,
                    isGroupAllSelected,
                    toggleGroupPermissions,
                    savePermissions,
                    // 管理员管理相关
                    showAddAdminModal,
                    showEditAdminModal,
                    adminForm,
                    adminList,
                    refreshAdminList,
                    editAdmin,
                    deleteAdmin,
                    toggleAdminStatus,
                    saveAdmin,
                    resetAdminForm,
                    getRoleName,
                    // 其他方法
                    switchAuthTab,
                    validateLoginUsername,
                    validateLoginPassword,
                    validateCaptcha,
                    validateCompanyName,
                    validateContactName,
                    validatePhone,
                    validateSmsCode,
                    validateRegisterPassword,
                    validateConfirmPassword,
                    validateAgreement,
                    checkPasswordStrength,
                    refreshCaptcha,
                    sendSmsCode,
                    handleLogin,
                    handleRegister,
                    handleForgotPassword,
                    showAgreement,
                    toggleSidebar,
                    navigateTo,
                    getCurrentPageTitle,
                    formatNumber,
                    chartTimeRange,
                    switchTimeRange,
                    sortShopsByRevenue,
                    paymentData,
                    hotProducts,
                    getShopRank,
                    getStockStatusText,
                    refreshShopData,
                    controlShop
                };
            }
        }).mount('#app');
    </script>
</body>
</html>