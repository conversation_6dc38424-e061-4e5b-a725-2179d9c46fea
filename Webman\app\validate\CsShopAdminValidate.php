<?php

namespace app\validate;

class CsShopAdminValidate extends BaseValidate
{
    protected $rule = [
        'shop_id|门店ID' => [
            'require',
            'number',
        ],
        'username|用户名' => [
            'require',
            'alphaNum',
            'min' => 3,
            'max' => 20,
            'unique' => 'app\model\CsShopAdmin,username',
        ],
        'password|密码' => [
            'require',
            'min' => 6,
            'max' => 20,
        ],
        'mobile|手机号码' => [
            'require',
            'mobile',
            'unique' => 'app\model\CsShopAdmin,mobile',
        ],
        'nickname|昵称' => [
            'require',
            'chsAlphaNum',
            'min' => 2,
            'max' => 20,
        ],
        'status|状态' => [
            'require',
            'in' => '0,1',
        ],
    ];

    protected $message = [
        'shop_id.require' => '门店ID不能为空',
        'shop_id.number' => '门店ID必须是数字',
        'username.require' => '用户名不能为空',
        'username.alphaNum' => '用户名只能包含字母和数字',
        'username.min' => '用户名长度不能少于3位',
        'username.max' => '用户名长度不能超过20位',
        'username.unique' => '用户名已存在',
        'password.require' => '密码不能为空',
        'password.min' => '密码长度不能少于6位',
        'password.max' => '密码长度不能超过20位',
        'mobile.require' => '手机号码不能为空',
        'mobile.mobile' => '手机号码格式不正确',
        'mobile.unique' => '手机号码已存在',
        'nickname.require' => '昵称不能为空',
        'nickname.chsAlphaNum' => '昵称只能包含中文、字母和数字',
        'nickname.min' => '昵称长度不能少于2位',
        'nickname.max' => '昵称长度不能超过20位',
        'status.require' => '状态不能为空',
        'status.in' => '状态值不正确',
    ];

    protected $scene = [
        'create' => ['shop_id', 'username', 'password', 'mobile', 'nickname', 'status'],
        'update' => ['shop_id', 'username', 'mobile', 'nickname', 'status'],
        'reset_password' => ['password'],
    ];
}