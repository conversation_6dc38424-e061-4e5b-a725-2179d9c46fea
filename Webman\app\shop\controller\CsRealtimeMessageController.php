<?php

namespace app\shop\controller;

use app\controller\ShopBaseController;
use app\model\CsRealtimeMessage;
use support\Request;
use support\Response;
use Webman\Log;
use Carbon\Carbon;

/**
 * 实时消息控制器
 */
class CsRealtimeMessageController extends ShopBaseController
{
    protected $modelName = 'CsRealtimeMessage';

    /**
     * 获取消息列表
     */
    public function index(Request $request): Response
    {
        try {
            $params = [
                'shop_id' => $request->shop_id,
                'page' => $request->get('page', 1),
                'page_size' => $request->get('page_size', 15),
                'sort_field' => $request->get('sort_field', 'created_at'),
                'sort' => $request->get('sort', 'desc'),
                // 搜索条件
                'title' => $request->get('title', ''),
                'content' => $request->get('content', ''),
                'message_type' => $request->get('message_type', ''),
                'is_read' => $request->get('is_read', ''), // 0-未读 1-已读
                'created_at' => $request->get('created_at', []) // 时间范围
            ];

            $result = $this->getMessageList($params);
            return success($result);
        } catch (\Exception $e) {
            Log::error('获取实时消息列表失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 获取消息列表（内部方法）
     */
    private function getMessageList(array $params): array
    {
        $query = CsRealtimeMessage::where('shop_id', $params['shop_id']);

        // 搜索条件
        if (!empty($params['title'])) {
            $query->where('title', 'like', '%' . $params['title'] . '%');
        }

        if (!empty($params['content'])) {
            $query->where('content', 'like', '%' . $params['content'] . '%');
        }

        if (!empty($params['message_type'])) {
            $query->where('message_type', $params['message_type']);
        }

        if ($params['is_read'] !== '') {
            $query->where('is_read', $params['is_read']);
        }

        // 时间范围
        if (!empty($params['created_at']) && is_array($params['created_at']) && count($params['created_at']) == 2) {
            $query->whereBetween('created_at', $params['created_at']);
        }

        // 排序
        $sortField = $params['sort_field'];
        $sortOrder = $params['sort'];
        
        if ($sortField === 'priority') {
            // 按优先级排序
            $query->orderByPriority();
            if ($sortOrder === 'asc') {
                $query->orderBy('created_at', 'asc');
            } else {
                $query->orderBy('created_at', 'desc');
            }
        } else {
            $query->orderBy($sortField, $sortOrder);
        }

        // 分页
        $total = $query->count();
        $itemList = $query->offset(($params['page'] - 1) * $params['page_size'])
            ->limit($params['page_size'])
            ->get();

        // 添加计算属性
        $itemList->each(function ($item) {
            $item->append([
                'type_text', 'read_status_text', 'type_class', 'read_status_class',
                'priority', 'icon', 'is_urgent', 'time_ago',
                'table_id', 'order_id', 'booking_id', 'user_id'
            ]);
        });

        return [
            'itemList' => $itemList,
            'total' => $total,
            'current_page' => $params['page'],
            'per_page' => $params['page_size']
        ];
    }

    /**
     * 获取消息详情
     */
    public function detail(Request $request): Response
    {
        try {
            $id = $request->get('id');
            if (empty($id)) {
                return fail('消息ID不能为空');
            }

            $message = CsRealtimeMessage::where('id', $id)
                ->where('shop_id', $request->shop_id)
                ->first();

            if (!$message) {
                return fail('消息不存在');
            }

            // 添加计算属性
            $message->append([
                'type_text', 'read_status_text', 'type_class', 'read_status_class',
                'priority', 'icon', 'is_urgent', 'time_ago',
                'table_id', 'order_id', 'booking_id', 'user_id'
            ]);

            return success($message);
        } catch (\Exception $e) {
            Log::error('获取实时消息详情失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 标记消息已读
     */
    public function markRead(Request $request): Response
    {
        try {
            $id = $request->post('id');
            if (empty($id)) {
                return fail('消息ID不能为空');
            }

            $message = CsRealtimeMessage::where('id', $id)
                ->where('shop_id', $request->shop_id)
                ->first();

            if (!$message) {
                return fail('消息不存在');
            }

            if ($message->markAsRead()) {
                // 获取最新的未读数量
                $unreadCount = CsRealtimeMessage::getUnreadCount($request->shop_id);
                
                return success([
                    'message' => '标记成功',
                    'unread_count' => $unreadCount
                ]);
            } else {
                return fail('标记失败');
            }
        } catch (\Exception $e) {
            Log::error('标记消息已读失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 批量标记已读
     */
    public function batchMarkRead(Request $request): Response
    {
        try {
            $ids = $request->post('ids', []);
            if (empty($ids) || !is_array($ids)) {
                return fail('请选择要标记的消息');
            }

            $updatedCount = CsRealtimeMessage::markBatchAsRead($ids, $request->shop_id);
            
            // 获取最新的未读数量
            $unreadCount = CsRealtimeMessage::getUnreadCount($request->shop_id);
            
            return success([
                'message' => "成功标记 {$updatedCount} 条消息为已读",
                'updated_count' => $updatedCount,
                'unread_count' => $unreadCount
            ]);
        } catch (\Exception $e) {
            Log::error('批量标记消息已读失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 全部标记为已读
     */
    public function markAllRead(Request $request): Response
    {
        try {
            $updatedCount = CsRealtimeMessage::markAllAsRead($request->shop_id);
            
            // 获取最新的未读数量（应该为0）
            $unreadCount = CsRealtimeMessage::getUnreadCount($request->shop_id);
            
            return success([
                'message' => "成功标记 {$updatedCount} 条消息为已读",
                'updated_count' => $updatedCount,
                'unread_count' => $unreadCount
            ]);
        } catch (\Exception $e) {
            Log::error('全部标记已读失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 获取未读消息数量
     */
    public function unreadCount(Request $request): Response
    {
        try {
            $count = CsRealtimeMessage::getUnreadCount($request->shop_id);
            return success($count);
        } catch (\Exception $e) {
            Log::error('获取未读消息数量失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 获取最新消息
     */
    public function latest(Request $request): Response
    {
        try {
            $limit = $request->get('limit', 5);
            
            $messages = CsRealtimeMessage::where('shop_id', $request->shop_id)
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();

            // 添加计算属性
            $messages->each(function ($item) {
                $item->append([
                    'type_text', 'read_status_text', 'type_class', 'read_status_class',
                    'priority', 'icon', 'is_urgent', 'time_ago',
                    'table_id', 'order_id', 'booking_id', 'user_id'
                ]);
            });

            return success($messages);
        } catch (\Exception $e) {
            Log::error('获取最新消息失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 获取消息统计
     */
    public function statistics(Request $request): Response
    {
        try {
            $type = $request->get('type', 'today'); // today, week, month
            
            switch ($type) {
                case 'today':
                    $statistics = CsRealtimeMessage::getTodayStatistics($request->shop_id);
                    break;
                case 'week':
                    $statistics = $this->getWeekStatistics($request->shop_id);
                    break;
                case 'month':
                    $statistics = $this->getMonthStatistics($request->shop_id);
                    break;
                default:
                    $statistics = CsRealtimeMessage::getTodayStatistics($request->shop_id);
            }

            return success($statistics);
        } catch (\Exception $e) {
            Log::error('获取消息统计失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 获取本周统计
     */
    private function getWeekStatistics(int $shopId): array
    {
        $startOfWeek = Carbon::now()->startOfWeek();
        $endOfWeek = Carbon::now()->endOfWeek();
        
        $messages = CsRealtimeMessage::where('shop_id', $shopId)
            ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
            ->select('message_type', 'is_read', 'created_at')
            ->get();

        $statistics = [
            'total' => $messages->count(),
            'unread' => $messages->where('is_read', CsRealtimeMessage::STATUS_UNREAD)->count(),
            'by_type' => [],
            'by_day' => []
        ];

        // 按类型统计
        foreach (CsRealtimeMessage::getTypeOptions() as $type => $name) {
            $typeMessages = $messages->where('message_type', $type);
            $statistics['by_type'][$type] = [
                'name' => $name,
                'total' => $typeMessages->count(),
                'unread' => $typeMessages->where('is_read', CsRealtimeMessage::STATUS_UNREAD)->count()
            ];
        }

        // 按天统计
        for ($i = 0; $i < 7; $i++) {
            $date = $startOfWeek->copy()->addDays($i);
            $dayMessages = $messages->filter(function ($message) use ($date) {
                return $message->created_at->format('Y-m-d') === $date->format('Y-m-d');
            });
            
            $statistics['by_day'][] = [
                'date' => $date->format('Y-m-d'),
                'weekday' => $date->format('D'),
                'total' => $dayMessages->count(),
                'unread' => $dayMessages->where('is_read', CsRealtimeMessage::STATUS_UNREAD)->count()
            ];
        }

        return $statistics;
    }

    /**
     * 获取本月统计
     */
    private function getMonthStatistics(int $shopId): array
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();
        
        $messages = CsRealtimeMessage::where('shop_id', $shopId)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->select('message_type', 'is_read', 'created_at')
            ->get();

        $statistics = [
            'total' => $messages->count(),
            'unread' => $messages->where('is_read', CsRealtimeMessage::STATUS_UNREAD)->count(),
            'by_type' => [],
            'by_week' => []
        ];

        // 按类型统计
        foreach (CsRealtimeMessage::getTypeOptions() as $type => $name) {
            $typeMessages = $messages->where('message_type', $type);
            $statistics['by_type'][$type] = [
                'name' => $name,
                'total' => $typeMessages->count(),
                'unread' => $typeMessages->where('is_read', CsRealtimeMessage::STATUS_UNREAD)->count()
            ];
        }

        // 按周统计
        $currentWeekStart = $startOfMonth->copy();
        $weekNum = 1;
        
        while ($currentWeekStart->lte($endOfMonth)) {
            $weekEnd = $currentWeekStart->copy()->endOfWeek();
            if ($weekEnd->gt($endOfMonth)) {
                $weekEnd = $endOfMonth->copy();
            }
            
            $weekMessages = $messages->filter(function ($message) use ($currentWeekStart, $weekEnd) {
                return $message->created_at->between($currentWeekStart, $weekEnd);
            });
            
            $statistics['by_week'][] = [
                'week' => $weekNum,
                'start_date' => $currentWeekStart->format('Y-m-d'),
                'end_date' => $weekEnd->format('Y-m-d'),
                'total' => $weekMessages->count(),
                'unread' => $weekMessages->where('is_read', CsRealtimeMessage::STATUS_UNREAD)->count()
            ];
            
            $currentWeekStart->addWeek();
            $weekNum++;
        }

        return $statistics;
    }

    /**
     * 创建消息（内部接口，用于WebSocket接收到消息时调用）
     */
    public function create(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证必要字段
            if (empty($data['shop_id']) || empty($data['message_type']) || 
                empty($data['title']) || empty($data['content'])) {
                return fail('缺少必要参数');
            }

            $message = CsRealtimeMessage::createMessage($data);
            
            if ($message) {
                return success($message, '消息创建成功');
            } else {
                return fail('消息创建失败');
            }
        } catch (\Exception $e) {
            Log::error('创建实时消息失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 删除消息
     */
    public function delete(Request $request): Response
    {
        try {
            $id = $request->post('id');
            if (empty($id)) {
                return fail('消息ID不能为空');
            }

            $message = CsRealtimeMessage::where('id', $id)
                ->where('shop_id', $request->shop_id)
                ->first();

            if (!$message) {
                return fail('消息不存在');
            }

            if ($message->delete()) {
                // 获取最新的未读数量
                $unreadCount = CsRealtimeMessage::getUnreadCount($request->shop_id);
                
                return success([
                    'message' => '删除成功',
                    'unread_count' => $unreadCount
                ]);
            } else {
                return fail('删除失败');
            }
        } catch (\Exception $e) {
            Log::error('删除实时消息失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 批量删除消息
     */
    public function batchDelete(Request $request): Response
    {
        try {
            $ids = $request->post('ids', []);
            if (empty($ids) || !is_array($ids)) {
                return fail('请选择要删除的消息');
            }

            $deletedCount = CsRealtimeMessage::where('shop_id', $request->shop_id)
                ->whereIn('id', $ids)
                ->delete();
            
            // 获取最新的未读数量
            $unreadCount = CsRealtimeMessage::getUnreadCount($request->shop_id);
            
            return success([
                'message' => "成功删除 {$deletedCount} 条消息",
                'deleted_count' => $deletedCount,
                'unread_count' => $unreadCount
            ]);
        } catch (\Exception $e) {
            Log::error('批量删除实时消息失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 清理过期消息
     */
    public function cleanup(Request $request): Response
    {
        try {
            $retentionDays = $request->post('retention_days', 30);
            
            $deletedCount = CsRealtimeMessage::cleanupExpiredMessages($request->shop_id, $retentionDays);
            
            return success([
                'message' => "成功清理 {$deletedCount} 条过期消息",
                'deleted_count' => $deletedCount
            ]);
        } catch (\Exception $e) {
            Log::error('清理过期消息失败: ' . $e->getMessage());
            return fail($e->getMessage());
        }
    }

    /**
     * 获取消息类型选项
     */
    public function getTypeOptions(Request $request): Response
    {
        try {
            return success(CsRealtimeMessage::getTypeOptions());
        } catch (\Exception $e) {
            return fail($e->getMessage());
        }
    }

    /**
     * 获取已读状态选项
     */
    public function getReadStatusOptions(Request $request): Response
    {
        try {
            return success(CsRealtimeMessage::getReadStatusOptions());
        } catch (\Exception $e) {
            return fail($e->getMessage());
        }
    }
}