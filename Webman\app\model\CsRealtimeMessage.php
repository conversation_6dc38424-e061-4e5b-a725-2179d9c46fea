<?php

namespace app\model;

use Carbon\Carbon;

/**
 * realtime_messages 实时消息表
 * @property integer $id 主键ID
 * @property integer $shop_id 店铺ID
 * @property string $message_type 消息类型
 * @property string $title 消息标题
 * @property string $content 消息内容
 * @property integer $is_read 是否已读
 * @property mixed $extra_data 扩展数据
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class CsRealtimeMessage extends ShopBaseModel
{
    protected $table = 'cs_realtime_messages';

    /**
     * 可批量赋值的属性
     * @var array
     */
    protected $fillable = [
        'shop_id', 'message_type', 'title', 'content', 'is_read', 'extra_data'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $casts = [
        'shop_id' => 'integer',
        'is_read' => 'integer',
        'extra_data' => 'json',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 验证字段
     * @var array
     */
    public static array $validateFields = [
        'id', 'shop_id', 'message_type', 'title', 'content', 'is_read', 'extra_data'
    ];

    /**
     * 搜索字段配置
     * @var array
     */
    public static array $searchFields = [
        'title' => 'LIKE',
        'content' => 'LIKE',
        'message_type' => '=',
        'is_read' => '=',
        'created_at' => 'BETWEEN',
    ];

    // 消息类型常量
    const TYPE_CALL_SERVICE = 'call_service';
    const TYPE_SUBMIT_ORDER = 'submit_order';
    const TYPE_BOOK_TABLE = 'book_table';
    const TYPE_CANCEL_BOOK = 'cancel_book';
    const TYPE_SERVICE_RESPONSE = 'service_response';
    const TYPE_ORDER_STATUS_UPDATE = 'order_status_update';
    const TYPE_BOOKING_STATUS_UPDATE = 'booking_status_update';

    // 已读状态常量
    const STATUS_UNREAD = 0;
    const STATUS_READ = 1;

    /**
     * 获取消息类型选项
     */
    public static function getTypeOptions(): array
    {
        return [
            self::TYPE_CALL_SERVICE => '呼叫服务',
            self::TYPE_SUBMIT_ORDER => '提交订单',
            self::TYPE_BOOK_TABLE => '预订桌台',
            self::TYPE_CANCEL_BOOK => '取消预订',
            /* self::TYPE_SERVICE_RESPONSE => '服务响应',
            self::TYPE_ORDER_STATUS_UPDATE => '订单状态更新',
            self::TYPE_BOOKING_STATUS_UPDATE => '预订状态更新', */
        ];
    }

    /**
     * 获取已读状态选项
     */
    public static function getReadStatusOptions(): array
    {
        return [
            self::STATUS_UNREAD => '未读',
            self::STATUS_READ => '已读',
        ];
    }

    /**
     * 获取消息类型文本
     */
    public function getTypeTextAttribute(): string
    {
        $options = self::getTypeOptions();
        return $options[$this->message_type] ?? '未知';
    }

    /**
     * 获取已读状态文本
     */
    public function getReadStatusTextAttribute(): string
    {
        $options = self::getReadStatusOptions();
        return $options[$this->is_read] ?? '未知';
    }

    /**
     * 获取消息类型样式类
     */
    public function getTypeClassAttribute(): string
    {
        return match($this->message_type) {
            self::TYPE_CALL_SERVICE => 'danger',
            self::TYPE_SUBMIT_ORDER => 'warning',
            self::TYPE_BOOK_TABLE => 'info',
            self::TYPE_CANCEL_BOOK => 'info',
            self::TYPE_SERVICE_RESPONSE => 'success',
            self::TYPE_ORDER_STATUS_UPDATE => 'primary',
            self::TYPE_BOOKING_STATUS_UPDATE => 'primary',
            default => 'info'
        };
    }

    /**
     * 获取已读状态样式类
     */
    public function getReadStatusClassAttribute(): string
    {
        return match($this->is_read) {
            self::STATUS_UNREAD => 'warning',
            self::STATUS_READ => 'success',
            default => 'info'
        };
    }

    /**
     * 获取消息优先级（用于排序和显示）
     */
    public function getPriorityAttribute(): int
    {
        return match($this->message_type) {
            self::TYPE_CALL_SERVICE => 4, // 最高优先级
            self::TYPE_SUBMIT_ORDER => 3,
            self::TYPE_SERVICE_RESPONSE => 3,
            self::TYPE_ORDER_STATUS_UPDATE => 2,
            self::TYPE_BOOK_TABLE => 2,
            self::TYPE_BOOKING_STATUS_UPDATE => 2,
            self::TYPE_CANCEL_BOOK => 1,
            default => 1
        };
    }

    /**
     * 获取消息图标
     */
    public function getIconAttribute(): string
    {
        return match($this->message_type) {
            self::TYPE_CALL_SERVICE => 'service',
            self::TYPE_SUBMIT_ORDER => 'shopping-cart',
            self::TYPE_BOOK_TABLE => 'calendar',
            self::TYPE_CANCEL_BOOK => 'close',
            self::TYPE_SERVICE_RESPONSE => 'check',
            self::TYPE_ORDER_STATUS_UPDATE => 'refresh',
            self::TYPE_BOOKING_STATUS_UPDATE => 'edit',
            default => 'message'
        };
    }

    /**
     * 判断是否为紧急消息
     */
    public function getIsUrgentAttribute(): bool
    {
        return $this->message_type === self::TYPE_CALL_SERVICE;
    }

    /**
     * 获取格式化的时间差
     */
    public function getTimeAgoAttribute(): string
    {
        if (!$this->created_at) {
            return '未知时间';
        }

        $now = time();
        $created = strtotime($this->created_at);
        $diff = $now - $created;

        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } elseif ($diff < 604800) {
            return floor($diff / 86400) . '天前';
        } else {
            return date('Y-m-d', $created);
        }
    }

    /**
     * 获取桌台ID（从扩展数据中）
     */
    public function getTableIdAttribute(): ?int
    {
        if (!$this->extra_data || !is_array($this->extra_data)) {
            return null;
        }
        
        $tableId = $this->extra_data['table_id'] ?? null;
        return $tableId !== null ? (int)$tableId : null;
    }

    /**
     * 获取订单ID（从扩展数据中）
     */
    public function getOrderIdAttribute(): ?int
    {
        if (!$this->extra_data || !is_array($this->extra_data)) {
            return null;
        }
        
        $orderId = $this->extra_data['order_id'] ?? null;
        return $orderId !== null ? (int)$orderId : null;
    }

    /**
     * 获取预订ID（从扩展数据中）
     */
    public function getBookingIdAttribute(): ?int
    {
        if (!$this->extra_data || !is_array($this->extra_data)) {
            return null;
        }
        
        $bookingId = $this->extra_data['booking_id'] ?? null;
        return $bookingId !== null ? (int)$bookingId : null;
    }

    /**
     * 获取用户ID（从扩展数据中）
     */
    public function getUserIdAttribute(): ?int
    {
        if (!$this->extra_data || !is_array($this->extra_data)) {
            return null;
        }
        
        $userId = $this->extra_data['user_id'] ?? null;
        return $userId !== null ? (int)$userId : null;
    }

    /**
     * Scope: 未读消息
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', self::STATUS_UNREAD);
    }

    /**
     * Scope: 已读消息
     */
    public function scopeRead($query)
    {
        return $query->where('is_read', self::STATUS_READ);
    }

    /**
     * Scope: 按优先级排序
     */
    public function scopeOrderByPriority($query)
    {
        return $query->orderByRaw("
            CASE message_type 
                WHEN '" . self::TYPE_CALL_SERVICE . "' THEN 4
                WHEN '" . self::TYPE_SUBMIT_ORDER . "' THEN 3
                WHEN '" . self::TYPE_SERVICE_RESPONSE . "' THEN 3
                WHEN '" . self::TYPE_ORDER_STATUS_UPDATE . "' THEN 2
                WHEN '" . self::TYPE_BOOK_TABLE . "' THEN 2
                WHEN '" . self::TYPE_BOOKING_STATUS_UPDATE . "' THEN 2
                WHEN '" . self::TYPE_CANCEL_BOOK . "' THEN 1
                ELSE 0
            END DESC
        ");
    }

    /**
     * Scope: 紧急消息
     */
    public function scopeUrgent($query)
    {
        return $query->where('message_type', self::TYPE_CALL_SERVICE);
    }

    /**
     * Scope: 今日消息
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * Scope: 指定日期范围
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 标记为已读
     */
    public function markAsRead(): bool
    {
        if ($this->is_read == self::STATUS_READ) {
            return true;
        }

        $this->is_read = self::STATUS_READ;
        return $this->save();
    }

    /**
     * 标记为未读
     */
    public function markAsUnread(): bool
    {
        if ($this->is_read == self::STATUS_UNREAD) {
            return true;
        }

        $this->is_read = self::STATUS_UNREAD;
        return $this->save();
    }

    /**
     * 批量标记为已读
     */
    public static function markBatchAsRead(array $ids, int $shopId): int
    {
        return self::where('shop_id', $shopId)
            ->whereIn('id', $ids)
            ->where('is_read', self::STATUS_UNREAD)
            ->update(['is_read' => self::STATUS_READ]);
    }

    /**
     * 全部标记为已读
     */
    public static function markAllAsRead(int $shopId): int
    {
        return self::where('shop_id', $shopId)
            ->where('is_read', self::STATUS_UNREAD)
            ->update(['is_read' => self::STATUS_READ]);
    }

    /**
     * 获取未读消息数量
     */
    public static function getUnreadCount(int $shopId): int
    {
        return self::where('shop_id', $shopId)
            ->where('is_read', self::STATUS_UNREAD)
            ->count();
    }

    /**
     * 获取今日消息统计
     */
    public static function getTodayStatistics(int $shopId): array
    {
        $today = today();
        
        $statistics = [
            'total' => 0,
            'unread' => 0,
            'by_type' => []
        ];

        // 总数和未读数
        $messages = self::where('shop_id', $shopId)
            ->whereDate('created_at', $today)
            ->select('message_type', 'is_read')
            ->get();

        $statistics['total'] = $messages->count();
        $statistics['unread'] = $messages->where('is_read', self::STATUS_UNREAD)->count();

        // 按类型统计
        foreach (self::getTypeOptions() as $type => $name) {
            $typeMessages = $messages->where('message_type', $type);
            $statistics['by_type'][$type] = [
                'name' => $name,
                'total' => $typeMessages->count(),
                'unread' => $typeMessages->where('is_read', self::STATUS_UNREAD)->count()
            ];
        }

        return $statistics;
    }

    /**
     * 清理过期消息（可配置保留天数）
     */
    public static function cleanupExpiredMessages(int $shopId, int $retentionDays = 30): int
    {
        $expiredDate = Carbon::now()->subDays($retentionDays);
        
        return self::where('shop_id', $shopId)
            ->where('created_at', '<', $expiredDate)
            ->delete();
    }

    /**
     * 创建实时消息
     */
    public static function createMessage(array $data): ?self
    {
        try {
            return self::create([
                'shop_id' => $data['shop_id'],
                'message_type' => $data['message_type'],
                'title' => $data['title'],
                'content' => $data['content'],
                'is_read' => $data['is_read'] ?? self::STATUS_UNREAD,
                'extra_data' => $data['extra_data'] ?? null,
            ]);
        } catch (\Exception $e) {
            \Webman\Log::error('创建实时消息失败: ' . $e->getMessage());
            return null;
        }
    }
}