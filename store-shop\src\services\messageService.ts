import { ElMessage, ElNotification } from 'element-plus'
import { useRealtimeStore } from '@/stores/realtime'
import { notificationManager, messageUtils } from '@/utils/websocket'
import {
  BusinessAction,
  RealtimeMessageType
} from '@/types/realtime'
import type {
  RealtimeMessage,
  ResponseMessage,
  CallServiceData,
  SubmitOrderData,
  BookTableData,
  CancelBookData,
  NotificationOptions
} from '@/types/realtime'

/**
 * 消息处理服务类
 */
export class MessageService {
  private realtimeStore = useRealtimeStore()
  
  /**
   * 处理WebSocket业务消息
   */
  async handleBusinessMessage(message: ResponseMessage): Promise<void> {
    try {
      if (!message.action || !message.data) {
        console.warn('收到无效的业务消息:', message)
        return
      }
      
      // 消息去重检查
      const messageKey = messageUtils.generateMessageKey(message)
      if (messageUtils.isDuplicateMessage(messageKey)) {
        console.log('忽略重复消息:', messageKey)
        return
      }
      
      messageUtils.addToCache(messageKey)
      
      // 创建实时消息对象
      const realtimeMessage = this.createRealtimeMessage(message)
      
      // 保存到store
      this.realtimeStore.messages.unshift(realtimeMessage)
      
      // 更新未读计数
      if (realtimeMessage.is_read === 0) {
        this.realtimeStore.unreadCount++
      }
      
      // 显示通知
      await this.showMessageNotification(realtimeMessage)
      
      // 保存到本地存储
      this.realtimeStore.saveMessagesToStorage()
      
      // 触发音效
      this.playMessageSound(realtimeMessage.message_type)
      
      console.log('处理业务消息成功:', realtimeMessage)
    } catch (error) {
      console.error('处理业务消息失败:', error)
      ElMessage.error('消息处理失败')
    }
  }
  
  /**
   * 创建实时消息对象
   */
  private createRealtimeMessage(responseMessage: ResponseMessage): RealtimeMessage {
    const { title, content } = this.formatMessage(responseMessage.action!, responseMessage.data)
    const shopId = responseMessage.data?.shop_id || 0
    
    return {
      shop_id: Number(shopId),
      message_type: this.getMessageType(responseMessage.action!),
      title,
      content,
      is_read: 0,
      extra_data: responseMessage.data || {},
      created_at: new Date().toISOString()
    }
  }
  
  /**
   * 格式化消息内容
   */
  private formatMessage(action: BusinessAction, data: any): { title: string; content: string } {
    switch (action) {
      case BusinessAction.CALL_SERVICE:
        return this.formatCallServiceMessage(data as CallServiceData)
        
      case BusinessAction.SUBMIT_ORDER:
        return this.formatSubmitOrderMessage(data as SubmitOrderData)
        
      case BusinessAction.BOOK_TABLE:
        return this.formatBookTableMessage(data as BookTableData)
        
      case BusinessAction.CANCEL_BOOK:
        return this.formatCancelBookMessage(data as CancelBookData)
        
      default:
        return {
          title: '系统通知',
          content: data.message || '收到新消息'
        }
    }
  }
  
  /**
   * 格式化呼叫服务消息
   */
  private formatCallServiceMessage(data: CallServiceData): { title: string; content: string } {
    const tableName = data.table_id ? `桌台${data.table_id}号` : '客户'
    return {
      title: `${tableName}呼叫服务`,
      content: data.message || '客户请求服务员协助，请及时响应'
    }
  }
  
  /**
   * 格式化订单提交消息
   */
  private formatSubmitOrderMessage(data: SubmitOrderData): { title: string; content: string } {
    const tableName = data.table_id ? `桌台${data.table_id}号` : '客户'
    const amount = data.total_amount ? `，金额：¥${data.total_amount}` : ''
    return {
      title: '新订单通知',
      content: `${tableName}提交了新订单${amount}`
    }
  }
  
  /**
   * 格式化桌台预订消息
   */
  private formatBookTableMessage(data: BookTableData): { title: string; content: string } {
    const tableName = data.table_id ? `桌台${data.table_id}号` : '客户'
    const time = data.booking_time ? `，预订时间：${this.formatBookingTime(data.booking_time)}` : ''
    const guests = data.guest_count ? `，就餐人数：${data.guest_count}人` : ''
    return {
      title: '桌台预订通知',
      content: `${tableName}有新的预订申请${time}${guests}`
    }
  }
  
  /**
   * 格式化取消预订消息
   */
  private formatCancelBookMessage(data: CancelBookData): { title: string; content: string } {
    const tableName = data.table_id ? `桌台${data.table_id}号` : '客户'
    const reason = data.reason ? `，取消原因：${data.reason}` : ''
    return {
      title: '取消预订通知',
      content: `${tableName}取消了预订${reason}`
    }
  }
  
  /**
   * 格式化预订时间
   */
  private formatBookingTime(timestamp: string): string {
    try {
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (error) {
      return timestamp
    }
  }
  
  /**
   * 获取消息类型
   */
  private getMessageType(action: BusinessAction): RealtimeMessageType {
    const typeMap: Record<BusinessAction, RealtimeMessageType> = {
      [BusinessAction.JOIN_GROUP]: RealtimeMessageType.CALL_SERVICE,
      [BusinessAction.CALL_SERVICE]: RealtimeMessageType.CALL_SERVICE,
      [BusinessAction.SUBMIT_ORDER]: RealtimeMessageType.SUBMIT_ORDER,
      [BusinessAction.BOOK_TABLE]: RealtimeMessageType.BOOK_TABLE,
      [BusinessAction.CANCEL_BOOK]: RealtimeMessageType.CANCEL_BOOK,
      [BusinessAction.SERVICE_RESPONSE]: RealtimeMessageType.CALL_SERVICE,
      [BusinessAction.ORDER_STATUS_UPDATE]: RealtimeMessageType.SUBMIT_ORDER,
      [BusinessAction.BOOKING_STATUS_UPDATE]: RealtimeMessageType.BOOK_TABLE
    }
    
    return typeMap[action] || RealtimeMessageType.CALL_SERVICE
  }
  
  /**
   * 显示消息通知
   */
  private async showMessageNotification(message: RealtimeMessage): Promise<void> {
    if (!this.realtimeStore.notificationEnabled) {
      return
    }
    
    const isUrgent = message.message_type === RealtimeMessageType.CALL_SERVICE
    
    // 浏览器通知
    if (await this.shouldShowBrowserNotification()) {
      const options: NotificationOptions = {
        title: message.title,
        body: message.content,
        icon: '/favicon.ico',
        sound: this.realtimeStore.soundEnabled,
        persistent: isUrgent,
        onClick: () => {
          // 可以在这里添加点击通知的处理逻辑
          this.handleNotificationClick(message)
        }
      }
      
      notificationManager.showNotification(options)
    }
    
    // Element Plus 通知
    ElNotification({
      title: message.title,
      message: message.content,
      type: this.getNotificationType(message.message_type),
      duration: isUrgent ? 10000 : 5000,
      position: 'top-right',
      showClose: true,
      onClick: () => {
        this.handleNotificationClick(message)
      }
    })
  }
  
  /**
   * 检查是否应该显示浏览器通知
   */
  private async shouldShowBrowserNotification(): Promise<boolean> {
    if (!('Notification' in window)) {
      return false
    }
    
    if (Notification.permission === 'granted') {
      return true
    }
    
    if (Notification.permission === 'default') {
      const permission = await notificationManager.requestPermission()
      return permission === 'granted'
    }
    
    return false
  }
  
  /**
   * 获取通知类型
   */
  private getNotificationType(messageType: RealtimeMessageType): 'success' | 'warning' | 'info' | 'error' {
    const typeMap: Record<RealtimeMessageType, 'success' | 'warning' | 'info' | 'error'> = {
      [RealtimeMessageType.CALL_SERVICE]: 'warning',
      [RealtimeMessageType.SUBMIT_ORDER]: 'success',
      [RealtimeMessageType.BOOK_TABLE]: 'success',
      [RealtimeMessageType.CANCEL_BOOK]: 'info'
    }
    
    return typeMap[messageType] || 'info'
  }
  
  /**
   * 播放消息提示音
   */
  private playMessageSound(messageType: RealtimeMessageType): void {
    if (!this.realtimeStore.soundEnabled) {
      return
    }
    
    const isUrgent = messageType === RealtimeMessageType.CALL_SERVICE
    notificationManager.playSound(isUrgent ? 'urgent' : 'default')
  }
  
  /**
   * 处理通知点击事件
   */
  private handleNotificationClick(message: RealtimeMessage): void {
    // 聚焦窗口
    if (window.focus) {
      window.focus()
    }
    
    // 标记消息为已读
    this.markMessageAsRead(message)
    
    // 可以在这里添加跳转到消息详情的逻辑
    console.log('点击消息通知:', message)
  }
  
  /**
   * 标记消息为已读
   */
  markMessageAsRead(message: RealtimeMessage): void {
    if (message.id && message.is_read === 0) {
      this.realtimeStore.markMessageRead(message.id)
    }
  }
  
  /**
   * 批量标记消息为已读
   */
  markMessagesAsRead(messageIds: number[]): void {
    this.realtimeStore.markMessagesRead(messageIds)
  }
  
  /**
   * 全部标记为已读
   */
  markAllAsRead(): void {
    this.realtimeStore.markAllRead()
  }
  
  /**
   * 删除消息
   */
  deleteMessage(messageId: number): void {
    this.realtimeStore.removeMessage(messageId)
    ElMessage.success('消息已删除')
  }
  
  /**
   * 清空所有消息
   */
  clearAllMessages(): void {
    this.realtimeStore.clearAllMessages()
    ElMessage.success('所有消息已清空')
  }
  
  /**
   * 创建测试消息（开发调试用）
   */
  createTestMessage(type: RealtimeMessageType = RealtimeMessageType.CALL_SERVICE): void {
    const testData = this.generateTestData(type)
    const testMessage: ResponseMessage = {
      type: 'business' as any,
      action: this.getActionByType(type),
      data: testData
    }
    
    this.handleBusinessMessage(testMessage)
  }
  
  /**
   * 生成测试数据
   */
  private generateTestData(type: RealtimeMessageType): any {
    const tableId = Math.floor(Math.random() * 20) + 1
    const userId = Math.floor(Math.random() * 1000) + 1
    const shopId = 1
    
    switch (type) {
      case RealtimeMessageType.CALL_SERVICE:
        return {
          shop_id: shopId,
          table_id: tableId,
          user_id: userId,
          message: '请求服务员协助',
          timestamp: new Date().toISOString()
        } as CallServiceData
        
      case RealtimeMessageType.SUBMIT_ORDER:
        return {
          shop_id: shopId,
          order_id: Math.floor(Math.random() * 10000) + 1,
          table_id: tableId,
          user_id: userId,
          total_amount: Math.floor(Math.random() * 200) + 50,
          timestamp: new Date().toISOString()
        } as SubmitOrderData
        
      case RealtimeMessageType.BOOK_TABLE:
        return {
          shop_id: shopId,
          booking_id: Math.floor(Math.random() * 1000) + 1,
          table_id: tableId,
          user_id: userId,
          booking_time: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
          guest_count: Math.floor(Math.random() * 8) + 1,
          timestamp: new Date().toISOString()
        } as BookTableData
        
      case RealtimeMessageType.CANCEL_BOOK:
        return {
          shop_id: shopId,
          booking_id: Math.floor(Math.random() * 1000) + 1,
          table_id: tableId,
          user_id: userId,
          reason: '临时有事',
          timestamp: new Date().toISOString()
        } as CancelBookData
    }
  }
  
  /**
   * 根据消息类型获取对应的动作
   */
  private getActionByType(type: RealtimeMessageType): BusinessAction {
    const actionMap: Record<RealtimeMessageType, BusinessAction> = {
      [RealtimeMessageType.CALL_SERVICE]: BusinessAction.CALL_SERVICE,
      [RealtimeMessageType.SUBMIT_ORDER]: BusinessAction.SUBMIT_ORDER,
      [RealtimeMessageType.BOOK_TABLE]: BusinessAction.BOOK_TABLE,
      [RealtimeMessageType.CANCEL_BOOK]: BusinessAction.CANCEL_BOOK
    }
    
    return actionMap[type]
  }
  
  /**
   * 获取消息统计信息
   */
  getMessageStatistics(): {
    total: number
    unread: number
    byType: Record<RealtimeMessageType, number>
    today: number
  } {
    const messages = this.realtimeStore.messages
    const today = new Date().toDateString()
    
    const statistics = {
      total: messages.length,
      unread: this.realtimeStore.unreadCount,
      byType: {
        [RealtimeMessageType.CALL_SERVICE]: 0,
        [RealtimeMessageType.SUBMIT_ORDER]: 0,
        [RealtimeMessageType.BOOK_TABLE]: 0,
        [RealtimeMessageType.CANCEL_BOOK]: 0
      },
      today: 0
    }
    
    messages.forEach(message => {
      // 按类型统计
      statistics.byType[message.message_type]++
      
      // 今日消息统计
      if (message.created_at && new Date(message.created_at).toDateString() === today) {
        statistics.today++
      }
    })
    
    return statistics
  }
  
  /**
   * 导出消息数据
   */
  exportMessages(): string {
    const messages = this.realtimeStore.messages
    const exportData = {
      exportTime: new Date().toISOString(),
      totalCount: messages.length,
      messages: messages.map(msg => ({
        ...msg,
        exportTime: undefined // 移除一些不必要的字段
      }))
    }
    
    return JSON.stringify(exportData, null, 2)
  }
  
  /**
   * 导入消息数据
   */
  importMessages(data: string): void {
    try {
      const importData = JSON.parse(data)
      if (importData.messages && Array.isArray(importData.messages)) {
        // 这里可以添加数据验证逻辑
        this.realtimeStore.messages = importData.messages
        this.realtimeStore.saveMessagesToStorage()
        ElMessage.success(`成功导入 ${importData.messages.length} 条消息`)
      } else {
        throw new Error('无效的数据格式')
      }
    } catch (error) {
      console.error('导入消息失败:', error)
      ElMessage.error('导入消息失败：数据格式错误')
    }
  }
}

// 创建全局实例
export const messageService = new MessageService()

// 默认导出
export default messageService