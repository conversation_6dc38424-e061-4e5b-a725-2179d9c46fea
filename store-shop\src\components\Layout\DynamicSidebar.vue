<template>
  <div class="app-sidebar">
    <el-menu
      ref="menuRef"
      class="sidebar-menu"
      :default-active="activeMenu"
      :collapse="isCollapse"
      :unique-opened="true"
      background-color="#f5f7f9"
      text-color="#333"
      active-text-color="var(--el-color-primary)"
    >
      <template v-if="isLoading">
        <div class="menu-loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载菜单中...</span>
        </div>
      </template>
      
      <template v-else>
        <sidebar-item 
          v-for="menu in accessibleMenus" 
          :key="menu.id"
          :menu="menu"
          :base-path="''"
          @navigate="handleNavigate"
        />
      </template>
    </el-menu>

    <!-- 客户服务悬浮区域 -->
    <div 
      class="customer-service-container"
      :class="{ 'is-dragging': isDragging }"
      :style="{
        left: containerPosition.x + 'px',
        bottom: containerPosition.y + 'px'
      }"
    >
      <!-- 客户服务触发按钮 -->
      <div
        class="customer-service-trigger"
        @mouseenter="!isDragging && handleMouseEnter()"
        @mouseleave="!isDragging && handleMouseLeave()"
        @mousedown="handleDragStart"
      >
        <div class="service-icon-wrapper">
          <el-icon class="service-icon"><Phone /></el-icon>
        </div>
        <span class="service-text">客户服务</span>
      </div>

      <!-- 客户服务悬浮菜单 -->
      <div
        class="customer-service-popup"
        :class="{ 'is-visible': showCustomerService }"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
      >
        <div class="service-item" @click="openExternalLink('https://docs.example.com')">
          <span>操作手册</span>
        </div>
        <!-- <div class="service-divider"></div>
        <div class="service-item" @click="navigateToFeedback">
          <span>意见反馈</span>
        </div> -->
        <div class="service-divider"></div>
        <div class="service-item phone-item">
          <span class="phone-label">售后客服：</span>
          <span class="phone-number">13340013637</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Loading, Phone } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import { useMenuStore } from '@/stores/menu'
import { usePermission } from '@/composables/usePermission'
import SidebarItem from './SidebarItem.vue'

const route = useRoute()
const router = useRouter()
const menuStore = useMenuStore()
const { accessibleMenus, hasPermission } = usePermission()

const isCollapse = ref(false)
const isLoading = ref(true)
const showCustomerService = ref(false)
const isRedirecting = ref(false)

// 菜单引用
const menuRef = ref()

// 拖动相关状态
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const containerPosition = ref({ x: 40, y: 20 }) // 初始位置

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  // 根据当前路由路径找到对应的菜单标识
  const currentPath = route.path
  
  // 添加防护，避免在菜单未加载或重定向过程中计算
  if (isLoading.value || isRedirecting.value || !menuStore.userMenus.length) {
    return ''
  }
  
  // 递归查找所有菜单项（包括子菜单），进行精确匹配
  const findExactMatch = (menus: any[]): any => {
    for (const menu of menus) {
      const menuPath = getMenuPath(menu)
      if (menuPath === currentPath) {
        return menu
      }
      
      if (menu.children && menu.children.length > 0) {
        const childMatch = findExactMatch(menu.children)
        if (childMatch) return childMatch
      }
    }
    return null
  }
  
  // 先尝试精确匹配（包括子菜单）
  const exactMatch = findExactMatch(menuStore.userMenus)
  if (exactMatch) {
    return exactMatch.menu_sign
  }
  
  // 尝试部分匹配（适用于嵌套路由）
  const partialMatch = findMenuByPath(menuStore.userMenus, currentPath)
  if (partialMatch) {
    return partialMatch.menu_sign
  }
  
  // 只有在确实需要时才获取默认菜单，但避免递归
  return ''
})

/**
 * 根据菜单获取路径
 */
const getMenuPath = (menu: any): string => {
  // 这里可以根据菜单标识映射到实际路径
  const pathMap: Record<string, string> = {
    table_business: '/table/business',
    retail: '/retail',
    reservation: '/reservation',
    member_users: '/member/users',
    member_level: '/member/user-level',
    member_recharge: '/member/recharge-package',
    member_points_get: '/member/points',
    member_points_use: '/member/deduction',
    member_balance: '/member/balance-log',
    member_points: '/member/points-log',
    order_table: '/order/table',
    order_retail: '/order/retail',
    finance_overview: '/finance/reports/business-overview',
    finance_monthly: '/finance/reports/monthly-report',
    finance_goods_rank: '/finance/reports/goods-ranking',
    finance_staff: '/finance/reports/staff-performance',
    finance_profit: '/finance/reports/profit-analysis',
    coupon: '/marketing/coupon',
    marketing_activity: '/marketing/activity',
    inventory_overview: '/inventory/index',
    inventory_check: '/inventory/check',
    inventory_log: '/inventory/log',
    message_center: '/message/message-center',
    table_category: '/table/classification',
    table_manage: '/table/index',
    table_config: '/settings/table',
    goods_category: '/goods/category',
    goods_manage: '/goods/index',
    goods_package: '/goods/set-meal',
    goods_material_category: '/goods/accessories-category',
    goods_material: '/goods/accessories-goods',
    goods_tag: '/goods/remark-label',
    goods_spec_category: '/goods/specification-classification',
    goods_spec: '/goods/specification',
    payment_method: '/settings/payment/method',
    table_billing: '/settings/billing/table',
    service_fee: '/settings/billing/service',
    print_template: '/settings/print/template',
    printer_config: '/settings/print/config',
    staff_admin: '/staff/list',
    staff_role: '/staff/role',
    customer_service_feedback: '/customer-service/feedback'
  }
  
  return pathMap[menu.menu_sign] || `/${menu.menu_sign.replace(/_/g, '/')}`
}

/**
 * 递归查找菜单 - 优先匹配最具体的子菜单
 */
const findMenuByPath = (menus: any[], targetPath: string): any => {
  let bestMatch = null
  let bestMatchLength = 0
  
  for (const menu of menus) {
    const menuPath = getMenuPath(menu)
    
    // 先检查子菜单是否有更精确的匹配
    if (menu.children && menu.children.length > 0) {
      const childMatch = findMenuByPath(menu.children, targetPath)
      if (childMatch) {
        return childMatch // 如果子菜单有匹配，直接返回子菜单的匹配结果
      }
    }
    
    // 检查当前菜单是否匹配，并且是否比之前的匹配更精确
    if (targetPath.startsWith(menuPath) && menuPath.length > bestMatchLength) {
      bestMatch = menu
      bestMatchLength = menuPath.length
    }
  }
  
  return bestMatch
}

/**
 * 获取默认激活菜单（非递归版本）
 */
const getDefaultActiveMenu = (): string => {
  // 避免在计算过程中触发递归，直接返回默认值
  return 'table_business'
}

/**
 * 预加载财务报表相关组件和资源
 */
const preloadFinanceReports = () => {
  // 预加载财务报表页面组件
  const financeComponents = [
    () => import('@/views/finance/reports/BusinessOverviewDetail.vue'),
    () => import('@/views/finance/reports/MonthlyReportDetail.vue'),
    () => import('@/views/finance/reports/GoodsRankingDetail.vue'),
    () => import('@/views/finance/reports/StaffPerformanceDetail.vue'),
    () => import('@/views/finance/reports/ProfitAnalysisDetail.vue'),
    () => import('@/views/finance/reports/CostAnalysisDetail.vue'),
    () => import('@/views/finance/reports/PaymentAnalysisDetail.vue')
  ]
  
  // 预加载财务相关组件
  const financeSubComponents = [
    () => import('@/components/finance/ReportPageLayout.vue'),
    () => import('@/components/finance/charts/SalesChart.vue'),
    () => import('@/components/finance/charts/PaymentAnalysisChart.vue')
  ]
  
  // 在空闲时间预加载
  requestIdleCallback(() => {
    financeComponents.forEach(loadComponent => {
      loadComponent().catch(() => {
        // 忽略预加载失败，不影响用户操作
      })
    })
    
    // 延迟一点加载子组件，避免一次性加载过多
    setTimeout(() => {
      financeSubComponents.forEach(loadComponent => {
        loadComponent().catch(() => {
          // 忽略预加载失败
        })  
      })
    }, 1000)
  })
}

/**
 * 处理导航
 */
const handleNavigate = (path: string) => {
  // 如果是首次访问财务报表相关页面，预加载其他报表页面
  if (path.startsWith('/finance/reports/')) {
    preloadFinanceReports()
  }
  
  router.push(path)
}

/**
 * 初始化菜单
 */
const initMenu = async () => {
  try {
    isLoading.value = true
    
    // 检查菜单配置状态
    await menuStore.fetchMenuConfigStatus()
    
    // 如果未初始化，则先初始化
    if (!menuStore.configStatus.is_initialized) {
      await menuStore.initMenuConfig()
    }
    
    // 获取用户菜单权限
    await menuStore.fetchUserMenuPermissions()
    
    // 使用 nextTick 确保状态更新完成后再设置权限
    await nextTick()
    
    // 设置权限到权限管理器
    const { setUserPermissions } = usePermission()
    setUserPermissions({
      menus: menuStore.userMenus,
      permissions: menuStore.userPermissions
    })
    
  } catch (error) {
    console.error('初始化菜单失败:', error)
    // 可以显示错误提示或回退到静态菜单
  } finally {
    // 确保在下一个 tick 后再设置加载完成，避免竞态条件
    await nextTick()
    isLoading.value = false
  }
}

// 组件挂载时初始化菜单
onMounted(() => {
  initMenu()
  restorePosition() // 恢复客户服务容器位置
})

// 监听路由变化，自动跳转到有权限的页面
watch(() => route.path, async (newPath) => {
  // 防止递归重定向
  if (isRedirecting.value || isLoading.value) {
    return
  }
  
  // 如果当前路径是根路径，跳转到第一个有权限的菜单
  if (newPath === '/' || newPath === '/login') {
    const firstMenu = findFirstAccessibleMenu()
    if (firstMenu) {
      const targetPath = getMenuPath(firstMenu)
      // 检查目标路径是否与当前路径不同
      if (targetPath !== newPath) {
        isRedirecting.value = true
        try {
          await router.push(targetPath)
          // 使用 nextTick 确保路由更新完成后再重置标志
          await nextTick()
        } catch (error) {
          console.error('路由跳转失败:', error)
        } finally {
          isRedirecting.value = false
        }
      }
    }
  }
}, { immediate: true })

// 监听activeMenu变化，手动更新菜单激活状态
watch(() => activeMenu.value, (newActiveMenu) => {
  if (newActiveMenu && menuRef.value) {
    // 使用nextTick确保DOM更新完成后再设置激活状态
    nextTick(() => {
      try {
        // 检查方法是否存在再调用
        if (typeof menuRef.value.setActiveIndex === 'function') {
          menuRef.value.setActiveIndex(newActiveMenu)
        } else {
          // Element Plus 新版本可能使用不同的方法或属性
          console.debug('setActiveIndex方法不存在，使用default-active属性更新')
        }
      } catch (error) {
        // 如果设置失败，可能是菜单还未完全渲染，忽略错误
        console.debug('Menu setActiveIndex failed:', error)
      }
    })
  }
}, { immediate: false, flush: 'post' })

/**
 * 查找第一个可访问的菜单
 */
const findFirstAccessibleMenu = () => {
  // 添加安全检查
  if (!menuStore.userMenus || menuStore.userMenus.length === 0) {
    return null
  }
  
  const findFirst = (menus: any[]): any => {
    for (const menu of menus) {
      if (hasPermission(menu.menu_sign)) {
        if (!menu.children || menu.children.length === 0) {
          return menu
        } else {
          const child = findFirst(menu.children)
          if (child) return child
        }
      }
    }
    return null
  }
  
  return findFirst(menuStore.userMenus)
}

/**
 * 切换客户服务菜单显示状态
 */
const toggleCustomerService = () => {
  showCustomerService.value = !showCustomerService.value
}

/**
 * 处理鼠标离开事件
 */
let hideTimer: NodeJS.Timeout | null = null

const handleMouseLeave = () => {
  // 清除之前的定时器
  if (hideTimer) {
    clearTimeout(hideTimer)
  }
  // 延迟隐藏，避免鼠标快速移动时闪烁
  hideTimer = setTimeout(() => {
    showCustomerService.value = false
  }, 200)
}

/**
 * 处理鼠标进入事件
 */
const handleMouseEnter = () => {
  // 清除隐藏定时器
  if (hideTimer) {
    clearTimeout(hideTimer)
  }
  showCustomerService.value = true
}

/**
 * 打开外部链接
 */
const openExternalLink = (url: string) => {
  window.open(url, '_blank')
}

/**
 * 导航到意见反馈页面
 */
/* const navigateToFeedback = () => {
  showCustomerService.value = false
  router.push('/customer-service/feedback')
} */

/**
 * 显示客服电话
 */
const showCustomerServicePhone = () => {
  showCustomerService.value = false
  ElMessageBox.alert('客服电话：13340013637', '客户服务', {
    confirmButtonText: '确定',
    type: 'info'
  })
}

/**
 * 拖动开始
 */
const handleDragStart = (event: MouseEvent) => {
  // 防止在拖动时触发客户服务菜单
  event.preventDefault()
  event.stopPropagation()
  
  isDragging.value = true
  showCustomerService.value = false // 拖动时隐藏弹出菜单
  
  const rect = (event.target as HTMLElement).closest('.customer-service-container')?.getBoundingClientRect()
  if (rect) {
    dragOffset.value = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    }
  }
  
  document.addEventListener('mousemove', handleDragMove)
  document.addEventListener('mouseup', handleDragEnd)
}

/**
 * 拖动中
 */
const handleDragMove = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const x = event.clientX - dragOffset.value.x
  const y = window.innerHeight - event.clientY + dragOffset.value.y - 80 // 计算距离底部的距离
  
  // 边界限制
  const maxX = window.innerWidth - 120 // 容器宽度 + 一些边距
  const maxY = window.innerHeight - 120 // 容器高度 + 一些边距
  
  containerPosition.value = {
    x: Math.max(10, Math.min(x, maxX)),
    y: Math.max(10, Math.min(y, maxY))
  }
}

/**
 * 拖动结束
 */
const handleDragEnd = () => {
  isDragging.value = false
  
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
  
  // 保存位置到 localStorage
  localStorage.setItem('customerServicePosition', JSON.stringify(containerPosition.value))
}

/**
 * 从 localStorage 恢复位置
 */
const restorePosition = () => {
  const savedPosition = localStorage.getItem('customerServicePosition')
  if (savedPosition) {
    try {
      containerPosition.value = JSON.parse(savedPosition)
    } catch (error) {
      console.warn('Failed to parse saved position:', error)
    }
  }
}
</script>

<style scoped>
.app-sidebar {
  height: 100%;
  background-color: #1f1f1f;
  overflow-y: auto;
  overflow-x: hidden;
  padding-top: 10px;
  padding-left: 10px;
}

.sidebar-menu {
  height: 100%;
  border-right: none;
  padding-bottom: 60px;
  background: #1f1f1f;
}

.menu-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #c2c2c2;
}

.menu-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.sidebar-menu :deep(.el-menu) {
  border-right: none;
}

/* 自定义子菜单样式 */
.sidebar-menu :deep(.el-sub-menu) {
  width: 100%;
  min-height: 60px;
  height: auto;
  line-height: 60px;
  float: left;
  border-bottom: 1px solid rgba(53, 58, 54, .5);
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  overflow: hidden;
}

.sidebar-menu :deep(.el-sub-menu .el-menu-item) {
  background: #353a36;
  color: #c2c2c2;
  padding-left: 40px !important;
}

.sidebar-menu :deep(.el-sub-menu .el-sub-menu__title) {
  height: 60px;
  line-height: 60px;
  padding-left: 10px !important;
}

.sidebar-menu :deep(.el-sub-menu .el-sub-menu) {
  min-height: 50px;
  line-height: 50px;
  padding-left: 0 !important;
  border-radius: 0;
  border-bottom: none;
}

.sidebar-menu :deep(.el-sub-menu .el-sub-menu .el-sub-menu__title) {
  height: 50px;
  line-height: 50px;
  padding-left: 30px !important;
  background: #353a36;
  color: #c2c2c2;
}

.sidebar-menu :deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  padding-left: 10px !important;
  color: #c2c2c2;
}

.sidebar-menu :deep(.el-menu) {
  background: #353a36;
}

/* 自定义菜单项选中样式，只为菜单项添加高亮，而不是子菜单 */
.sidebar-menu :deep(.el-menu-item.is-active) {
  background: linear-gradient(90deg, #419e5d, #1f482b);
  border-left: 0;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  color: #fff !important;
  font-weight: 500;
  min-height: 50px;
  height: auto;
  line-height: 50px;
  padding-left: 10px !important;
  background-size: cover;
}

.sidebar-menu :deep(.el-menu-item.is-active):hover {
  background: linear-gradient(90deg, #419e5d, #1f482b) !important;
  color: #fff !important;
}

/* 自定义子菜单项选中样式 */
.sidebar-menu :deep(.el-menu--inline .el-menu-item.is-active) {
  padding-left: 40px !important;
}

.sidebar-menu :deep(.el-menu--inline .el-menu--inline .el-menu-item.is-active) {
  padding-left: 40px !important;
}

/* 菜单图标颜色 */
.sidebar-menu :deep(.el-menu-item.is-active .el-icon) {
  color: #fff !important;
}

/* 覆盖Element Plus默认的父级菜单高亮行为 */
.sidebar-menu :deep(.el-sub-menu) > .el-sub-menu__title {
  color: #c2c2c2 !important;
}

.sidebar-menu :deep(.el-sub-menu) > .el-sub-menu__title .el-icon {
  color: #c2c2c2 !important;
}

/* 即使有.is-active类，也不要高亮父级菜单标题 */
.sidebar-menu :deep(.el-sub-menu.is-active) > .el-sub-menu__title {
  background: #353a36;
  color: #c2c2c2;
}

.sidebar-menu :deep(.el-sub-menu.is-active) > .el-sub-menu__title .el-icon {
  color: #c2c2c2 !important;
}

/* 展开状态下的样式，保持原来的颜色 */
.sidebar-menu :deep(.el-sub-menu.is-opened:not(.is-active)) > .el-sub-menu__title {
  background: #353a36;
  color: #c2c2c2;
}

.sidebar-menu :deep(.el-sub-menu.is-opened:not(.is-active)) > .el-sub-menu__title .el-icon {
  color: #c2c2c2 !important;
}

/* 父级菜单高亮 */
.sidebar-menu :deep(.el-sub-menu.is-opened) {
  border-left: none;
}

/* 悬停效果 */
.sidebar-menu :deep(.el-menu-item:hover),
.sidebar-menu :deep(.el-sub-menu__title:hover) {
  background: #2f3134 !important;
  color: #2c9a4d;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(100, 108, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 客户服务悬浮区域样式 */
.customer-service-container {
  position: fixed;
  z-index: 2000;
  transition: transform 0.2s ease;
}

.customer-service-container.is-dragging {
  transform: scale(1.05);
  z-index: 3000;
}

.customer-service-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  cursor: grab;
  transition: all 0.3s ease;
  user-select: none;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.customer-service-container.is-dragging .customer-service-trigger {
  cursor: grabbing;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

.customer-service-trigger:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.service-icon-wrapper {
  margin-bottom: 4px;
}

.service-icon {
  font-size: 24px;
  color: #2c9a4d;
}

.service-text {
  font-size: 12px;
  color: #c2c2c2;
  text-align: center;
  line-height: 1.2;
}

.customer-service-popup {
  position: absolute;
  bottom: -10px;
  left: 100px;
  width: 200px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateX(-10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.customer-service-popup.is-visible {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.service-item {
  padding: 12px 16px;
  cursor: pointer;
  color: #c2c2c2;
  transition: all 0.3s ease;
  font-size: 14px;
  text-align: center;
}

.service-item:hover {
  background: rgba(44, 154, 77, 0.2);
  color: #2c9a4d;
}

.service-item:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.service-item:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.service-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 16px;
}

.phone-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.phone-label {
  font-size: 12px;
  color: #999;
}

.phone-number {
  font-size: 14px;
  color: #2c9a4d;
  font-weight: 500;
}

@media (max-width: 1200px) {
  .app-sidebar {
    padding-top: 5px;
    padding-left: 5px;
  }
  
  .sidebar-menu {
    padding-bottom: 60px;
  }
  
  /* 自定义子菜单样式 */
  .sidebar-menu :deep(.el-sub-menu) {
    min-height: 50px;
    line-height: 50px;
  }
  
  .sidebar-menu :deep(.el-sub-menu .el-menu-item) {
    padding-left: 10px !important;
  }
  
  .sidebar-menu :deep(.el-sub-menu .el-sub-menu__title) {
    height: 50px;
    line-height: 50px;
    padding-left: 10px !important;
  }
  
  .sidebar-menu :deep(.el-sub-menu .el-sub-menu) {
    min-height: 50px;
    line-height: 50px;
    padding-left: 0 !important;
  }
  
  .sidebar-menu :deep(.el-sub-menu .el-sub-menu .el-sub-menu__title) {
    height: 50px;
    line-height: 50px;
    padding-left: 10px !important;
  }
  
  .sidebar-menu :deep(.el-menu-item) {
    height: 50px;
    line-height: 50px;
    padding-left: 10px !important;
    color: #c2c2c2;
  }
  
  /* 自定义菜单项选中样式，只为菜单项添加高亮，而不是子菜单 */
  .sidebar-menu :deep(.el-menu-item.is-active) {
    background: linear-gradient(90deg, #419e5d, #1f482b);
    border-left: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    color: #fff !important;
    font-weight: 500;
    min-height: 50px;
    height: auto;
    line-height: 50px;
    padding-left: 10px !important;
    background-size: cover;
  }
  
  .sidebar-menu :deep(.el-menu-item.is-active):hover {
    background: linear-gradient(90deg, #419e5d, #1f482b) !important;
    color: #fff !important;
  }
  
  /* 自定义子菜单项选中样式 */
  .sidebar-menu :deep(.el-menu--inline .el-menu-item.is-active) {
    padding-left: 10px !important;
  }
  
  .sidebar-menu :deep(.el-menu--inline .el-menu--inline .el-menu-item.is-active) {
    padding-left: 10px !important;
  }
  
  /* 菜单图标颜色 */
  .sidebar-menu :deep(.el-menu-item.is-active .el-icon) {
    color: #fff !important;
  }

  /* 小屏幕下的客户服务样式 */
  .customer-service-trigger {
    width: 70px;
    height: 70px;
  }

  .service-icon {
    font-size: 20px;
  }

  .service-text {
    font-size: 11px;
  }

  .customer-service-popup {
    width: 180px;
    left: 90px;
  }

  .service-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .phone-number {
    font-size: 13px;
  }
}
</style>