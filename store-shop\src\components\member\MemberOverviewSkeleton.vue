<template>
  <div class="member-overview-skeleton">
    <!-- 顶部头部骨架 -->
    <div class="header-skeleton">
      <div class="header-left-skeleton">
        <el-skeleton-item variant="text" class="title-skeleton" />
        <el-skeleton-item variant="text" class="subtitle-skeleton" />
      </div>
      <div class="header-right-skeleton">
        <el-skeleton-item variant="button" style="width: 200px; height: 32px;" />
        <el-skeleton-item variant="button" style="width: 80px; height: 32px; margin-left: 12px;" />
        <el-skeleton-item variant="button" style="width: 80px; height: 32px; margin-left: 12px;" />
      </div>
    </div>

    <!-- 核心指标卡片骨架 -->
    <div class="summary-cards-skeleton">
      <el-row :gutter="20">
        <el-col :span="6" v-for="i in 4" :key="i">
          <el-card class="summary-card-skeleton">
            <div class="card-content-skeleton">
              <el-skeleton-item variant="circle" class="card-icon-skeleton" />
              <div class="card-info-skeleton">
                <el-skeleton-item variant="text" class="card-title-skeleton" />
                <el-skeleton-item variant="text" class="card-value-skeleton" />
                <el-skeleton-item variant="text" class="card-trend-skeleton" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域骨架 -->
    <div class="charts-skeleton">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card-skeleton">
            <template #header>
              <div class="chart-header-skeleton">
                <el-skeleton-item variant="text" class="chart-title-skeleton" />
                <div class="chart-controls-skeleton">
                  <el-skeleton-item variant="button" style="width: 80px; height: 28px;" />
                  <el-skeleton-item variant="button" style="width: 80px; height: 28px; margin-left: 8px;" />
                </div>
              </div>
            </template>
            <el-skeleton-item variant="rect" class="chart-content-skeleton" />
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card-skeleton">
            <template #header>
              <div class="chart-header-skeleton">
                <el-skeleton-item variant="text" class="chart-title-skeleton" />
                <el-skeleton-item variant="circle" style="width: 16px; height: 16px;" />
              </div>
            </template>
            <el-skeleton-item variant="circle" class="pie-chart-skeleton" />
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 资产管理区域骨架 -->
    <div class="assets-skeleton">
      <el-row :gutter="20">
        <el-col :span="8" v-for="i in 3" :key="i">
          <el-card class="asset-card-skeleton">
            <template #header>
              <div class="asset-header-skeleton">
                <el-skeleton-item variant="circle" style="width: 16px; height: 16px;" />
                <el-skeleton-item variant="text" style="width: 80px; height: 16px;" />
              </div>
            </template>
            <div class="asset-content-skeleton">
              <div class="stat-item-skeleton" v-for="j in 3" :key="j">
                <el-skeleton-item variant="text" style="width: 80px; height: 14px;" />
                <el-skeleton-item variant="text" style="width: 60px; height: 16px;" />
              </div>
              <div class="progress-skeleton">
                <el-skeleton-item variant="text" style="width: 40px; height: 14px; margin-bottom: 8px;" />
                <el-skeleton-item variant="rect" style="width: 100%; height: 6px;" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 活跃度分析区域骨架 -->
    <div class="activity-skeleton">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card class="activity-card-skeleton">
            <template #header>
              <div class="activity-header-skeleton">
                <el-skeleton-item variant="text" class="activity-title-skeleton" />
                <el-skeleton-item variant="button" style="width: 100px; height: 24px;" />
              </div>
            </template>
            <div class="activity-layers-skeleton">
              <div class="layer-item-skeleton" v-for="i in 4" :key="i">
                <div class="layer-info-skeleton">
                  <el-skeleton-item variant="text" style="width: 80px; height: 16px; margin-bottom: 4px;" />
                  <el-skeleton-item variant="text" style="width: 120px; height: 12px;" />
                </div>
                <div class="layer-stats-skeleton">
                  <el-skeleton-item variant="text" style="width: 60px; height: 18px; margin-bottom: 2px;" />
                  <el-skeleton-item variant="text" style="width: 40px; height: 12px;" />
                </div>
                <el-skeleton-item variant="rect" class="layer-bar-skeleton" />
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="actions-card-skeleton">
            <template #header>
              <div class="actions-header-skeleton">
                <el-skeleton-item variant="circle" style="width: 16px; height: 16px;" />
                <el-skeleton-item variant="text" style="width: 60px; height: 16px;" />
              </div>
            </template>
            <div class="actions-content-skeleton">
              <el-skeleton-item 
                variant="button" 
                v-for="i in 5" 
                :key="i"
                style="width: 100%; height: 40px; margin-bottom: 12px;"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
// 骨架屏组件不需要任何逻辑
</script>

<style scoped lang="scss">
.member-overview-skeleton {
  padding: 20px;
  background-color: #f5f7f9;
  min-height: 100vh;

  .header-skeleton {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    .header-left-skeleton {
      .title-skeleton {
        width: 200px;
        height: 24px;
        margin-bottom: 8px;
      }

      .subtitle-skeleton {
        width: 300px;
        height: 14px;
      }
    }

    .header-right-skeleton {
      display: flex;
      align-items: center;
    }
  }

  .summary-cards-skeleton {
    margin-bottom: 20px;

    .summary-card-skeleton {
      height: 120px;

      .card-content-skeleton {
        display: flex;
        align-items: center;
        height: 100%;
        padding: 10px;

        .card-icon-skeleton {
          width: 60px;
          height: 60px;
          margin-right: 16px;
        }

        .card-info-skeleton {
          flex: 1;

          .card-title-skeleton {
            width: 80px;
            height: 14px;
            margin-bottom: 4px;
          }

          .card-value-skeleton {
            width: 120px;
            height: 24px;
            margin-bottom: 4px;
          }

          .card-trend-skeleton {
            width: 100px;
            height: 12px;
          }
        }
      }
    }
  }

  .charts-skeleton,
  .assets-skeleton,
  .activity-skeleton {
    margin-bottom: 20px;

    .chart-card-skeleton,
    .asset-card-skeleton,
    .activity-card-skeleton,
    .actions-card-skeleton {
      .chart-header-skeleton,
      .asset-header-skeleton,
      .activity-header-skeleton,
      .actions-header-skeleton {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .chart-title-skeleton,
        .activity-title-skeleton {
          width: 120px;
          height: 20px;
        }

        .chart-controls-skeleton {
          display: flex;
        }
      }

      .chart-content-skeleton {
        width: 100%;
        height: 300px;
      }

      .pie-chart-skeleton {
        width: 200px;
        height: 200px;
        margin: 50px auto;
      }
    }
  }

  .assets-skeleton {
    .asset-content-skeleton {
      padding: 10px 0;

      .stat-item-skeleton {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }

      .progress-skeleton {
        margin-top: 16px;
      }
    }
  }

  .activity-skeleton {
    .activity-layers-skeleton {
      .layer-item-skeleton {
        display: flex;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .layer-info-skeleton {
          flex: 1;
          margin-right: 20px;
        }

        .layer-stats-skeleton {
          margin-right: 20px;
          text-align: right;
        }

        .layer-bar-skeleton {
          width: 100px;
          height: 8px;
        }
      }
    }

    .actions-content-skeleton {
      padding: 10px 0;
    }
  }
}
</style>