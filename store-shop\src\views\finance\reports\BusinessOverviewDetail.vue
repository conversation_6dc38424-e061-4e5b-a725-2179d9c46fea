<template>
  <div class="business-overview-detail">
    <!-- 骨架屏 -->
    <ReportSkeleton v-if="loading.summary" />
    
    <!-- 实际内容 -->
    <ReportPageLayout
      v-else
      title="营业概况"
      subtitle="查看门店营业的整体概况和核心指标分析"
      icon="TrendCharts"
      :summary-data="summaryData"
      :default-date-range="[queryParams.start_date, queryParams.end_date] as [string, string]"
      @date-range-change="handleDateRangeChange"
      @export="handleExport"
      @refresh="handleRefresh"
    >
      <!-- 概览数据 -->
      <template #summary>
        <el-row :gutter="20">
          <el-col :span="6" v-for="(item, index) in summaryData" :key="index">
            <el-card class="summary-card" @click="focusChart(item.key)">
              <div class="summary-content">
                <div class="summary-icon" :style="{ background: item.color }">
                  <el-icon><component :is="item.icon" /></el-icon>
                </div>
                <div class="summary-info">
                  <div class="summary-title">{{ item.title }}</div>
                  <div class="summary-value">{{ item.value }}</div>
                  <div v-if="false && item.change" class="summary-trend" :class="item.trend">
                    <el-icon><component :is="getTrendIcon(item.trend)" /></el-icon>
                    {{ item.change }}
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </template>

      <!-- 主要内容 -->
      <template #content>
        <!-- 趋势图表区域 -->
        <div class="content-section">
          <el-row :gutter="20">
            <el-col :span="16">
              <el-card class="chart-card chart-card-fixed-height">
                <template #header>
                  <div class="chart-header">
                    <span>营业额趋势分析</span>
                    <div class="chart-controls">
                      <el-radio-group v-model="revenueChartType" size="small">
                        <el-radio-button value="line">折线图</el-radio-button>
                        <el-radio-button value="bar">柱状图</el-radio-button>
                        <el-radio-button value="area">面积图</el-radio-button>
                      </el-radio-group>
                    </div>
                  </div>
                </template>
                <div class="chart-container" v-loading="loading.revenue">
                  <SalesChart
                    :data="revenueChartData"
                    :chart-type="revenueChartType"
                    title="营业额趋势"
                    :show-footer="false"
                    :enable-data-zoom="false"
                    :enable-brush="false"
                    @click="handleChartClick"
                  />
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="chart-card chart-card-fixed-height">
                <template #header>
                  <div class="chart-header">
                    <span>支付方式分布</span>
                  </div>
                </template>
                <div class="chart-container" v-loading="loading.payment">
                  <PaymentAnalysisChart
                    :data="paymentChartData"
                    type="pie"
                  />
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 订单数据和客流分析 -->
        <div class="content-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="data-analysis-card">
                <template #header>
                  <span>订单数据分析</span>
                </template>
                <div class="analysis-content">
                  <div class="metric-row">
                    <div class="metric-item">
                      <div class="metric-label">总订单数</div>
                      <div class="metric-value primary">{{ formatNumber(businessData?.total_orders || 0) }}</div>
                    </div>
                    <div class="metric-item">
                      <div class="metric-label">平均客单价</div>
                      <div class="metric-value success">¥{{ formatNumber(businessData?.avg_order_value || 0) }}</div>
                    </div>
                  </div>
                  <div class="metric-row">
                    <div class="metric-item">
                      <div class="metric-label">完成订单率</div>
                      <div class="metric-value warning">{{ (businessData?.completion_rate || 0).toFixed(1) }}%</div>
                    </div>
                    <div class="metric-item">
                      <div class="metric-label">退单率</div>
                      <div class="metric-value danger">{{ (businessData?.refund_rate || 0).toFixed(1) }}%</div>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="data-analysis-card">
                <template #header>
                  <span>客流分析</span>
                </template>
                <div class="analysis-content">
                  <div class="metric-row">
                    <div class="metric-item">
                      <div class="metric-label">总客流量</div>
                      <div class="metric-value primary">{{ formatNumber(businessData?.total_customers || 0) }}</div>
                    </div>
                    <div class="metric-item">
                      <div class="metric-label">会员占比</div>
                      <div class="metric-value success">{{ (businessData?.member_rate || 0).toFixed(1) }}%</div>
                    </div>
                  </div>
                  <div class="metric-row">
                    <div class="metric-item">
                      <div class="metric-label">新客占比</div>
                      <div class="metric-value warning">{{ (businessData?.new_customer_rate || 0).toFixed(1) }}%</div>
                    </div>
                    <div class="metric-item">
                      <div class="metric-label">复购率</div>
                      <div class="metric-value info">{{ ((businessData?.customer_return_rate || 0) * 100).toFixed(1) }}%</div>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 时段分析 -->
        <div class="content-section">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-card class="chart-card">
                <template #header>
                  <div class="chart-header">
                    <span>时段营业分析</span>
                    <div class="chart-controls">
                      <!-- 数据维度控制 -->
                      <el-radio-group v-model="timeAnalysisType" size="default">
                        <el-radio-button value="revenue">营业额</el-radio-button>
                        <el-radio-button value="orders">订单数</el-radio-button>
                        <el-radio-button value="customers">客流量</el-radio-button>
                      </el-radio-group>
                    </div>
                  </div>
                </template>
                <div class="chart-type-controls">
                  <el-radio-group
                    v-model="timeAnalysisChartType"
                    size="small"
                  >
                    <el-radio-button value="line">折线图</el-radio-button>
                    <el-radio-button value="bar">柱状图</el-radio-button>
                    <el-radio-button value="area">面积图</el-radio-button>
                  </el-radio-group>
                </div>
                <div class="chart-container" v-loading="loading.timeAnalysis">
                  <SalesChart
                    :data="timeAnalysisData"
                    :chart-type="timeAnalysisChartType"
                    height="280px"
                    :enable-data-zoom="false"
                    :enable-brush="false"
                    :show-footer="false"
                    :show-header="false"
                    :show-internal-controls="false"
                    title=""
                  />
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </template>

      <!-- 表格操作按钮 -->
      <template #table-actions>
        <el-button size="small" @click="handleTableExport">
          <el-icon><Download /></el-icon>
          导出表格
        </el-button>
      </template>

      <!-- 详细数据表格 -->
      <template #table>
        <el-table
          :data="paginatedTableData"
          style="width: 100%" height="450px;"
          v-loading="loading.table"
          :default-sort="{ prop: 'date', order: 'descending' }"
          stripe
          border
        >
          <el-table-column prop="date" label="日期" min-width="120" align="center" sortable />
          <el-table-column prop="revenue" label="营业额" min-width="120" align="center" sortable>
            <template #default="{ row }">
              <span class="amount-text">¥{{ formatNumber(row.revenue) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="orders" label="订单数" min-width="100" align="center" sortable>
            <template #default="{ row }">
              <span class="number-text">{{ formatNumber(row.orders) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="customers" label="客流量" min-width="100" align="center" sortable>
            <template #default="{ row }">
              <span class="number-text">{{ formatNumber(row.customers) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="avgOrderAmount" label="客单价" min-width="120" align="center" sortable>
            <template #default="{ row }">
              <span class="amount-text">¥{{ formatNumber(row.avgOrderAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="completionRate" label="完成率" min-width="100" align="center" sortable>
            <template #default="{ row }">
              <el-tag :type="getCompletionRateType(row.completionRate)">
                {{ row.completionRate }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="refundRate" label="退单率" min-width="100" align="center" sortable>
            <template #default="{ row }">
              <el-tag :type="getRefundRateType(row.refundRate)">
                {{ row.refundRate }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="peakHours" label="高峰时段" min-width="120" align="center">
            <template #default="{ row }">
              <span class="time-text">{{ row.peakHours }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" fixed="right" align="center">
            <template #default="{ row }">
              <el-button type="primary" link size="small" @click="exportSingle(row)">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 表格分页 -->
        <div class="table-pagination">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="PAGINATION_CONFIG.PAGE_SIZE_OPTIONS"
            :total="pagination.total"
            :layout="PAGINATION_CONFIG.DEFAULT_LAYOUT"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </template>
    </ReportPageLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { TrendCharts, ArrowUp, ArrowDown, Minus, Download } from '@element-plus/icons-vue'
import { PAGINATION_CONFIG } from '@/utils/constants'
import ReportPageLayout from '@/components/finance/ReportPageLayout.vue'
import SalesChart from '@/components/finance/charts/SalesChart.vue'
import PaymentAnalysisChart from '@/components/finance/charts/PaymentAnalysisChart.vue'
import ReportSkeleton from '@/components/finance/ReportSkeleton.vue'
import financeService from '@/services/financeService'
import type {
  BusinessOverviewData,
  BusinessOverviewParams
} from '@/types/finance'

// 响应式数据
const loading = reactive({
  revenue: false,
  payment: false,
  timeAnalysis: false,
  table: false
})

const revenueChartType = ref<'line' | 'bar' | 'area'>('line')
const timeAnalysisType = ref<'revenue' | 'orders' | 'customers'>('revenue')
const timeAnalysisChartType = ref<'line' | 'bar' | 'area'>('bar')

const businessData = ref<BusinessOverviewData | null>(null)
const revenueChartData = ref<any>([])
const paymentChartData = ref<any>([])
const timeAnalysisData = ref<any>([])
const tableData = ref<any[]>([])

const pagination = reactive({
  currentPage: 1,
  pageSize: PAGINATION_CONFIG.DEFAULT_PAGE_SIZE as number,
  total: 0
})

// 计算属性 - 分页后的表格数据
const paginatedTableData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return tableData.value.slice(start, end)
})

// 查询参数
const queryParams = reactive<BusinessOverviewParams>({
  start_date: new Date().toISOString().split('T')[0],
  end_date: new Date().toISOString().split('T')[0]
})

// 计算属性 - 概览数据
const summaryData = computed(() => {
  const growthAnalysis = businessData.value?.growth_analysis

  return [
    {
      key: 'revenue',
      title: '总营业额',
      value: `¥${formatNumber(businessData.value?.total_revenue || 0)}`,
      icon: 'Money',
      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      trend: growthAnalysis?.revenue_growth_rate ? (growthAnalysis.revenue_growth_rate >= 0 ? 'up' as const : 'down' as const) : 'neutral' as const,
      change: growthAnalysis?.revenue_growth_rate ? `${growthAnalysis.revenue_growth_rate >= 0 ? '+' : ''}${growthAnalysis.revenue_growth_rate}%` : ''
    },
    {
      key: 'orders',
      title: '总订单数',
      value: formatNumber(businessData.value?.total_orders || 0),
      icon: 'Document',
      color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      trend: growthAnalysis?.order_growth_rate ? (growthAnalysis.order_growth_rate >= 0 ? 'up' as const : 'down' as const) : 'neutral' as const,
      change: growthAnalysis?.order_growth_rate ? `${growthAnalysis.order_growth_rate >= 0 ? '+' : ''}${growthAnalysis.order_growth_rate}%` : ''
    },
    {
      key: 'customers',
      title: '总客流量',
      value: formatNumber(businessData.value?.total_customers || 0),
      icon: 'User',
      color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      trend: growthAnalysis?.customer_growth_rate ? (growthAnalysis.customer_growth_rate >= 0 ? 'up' as const : 'down' as const) : 'neutral' as const,
      change: growthAnalysis?.customer_growth_rate ? `${growthAnalysis.customer_growth_rate >= 0 ? '+' : ''}${growthAnalysis.customer_growth_rate}%` : ''
    },
    {
      key: 'avgAmount',
      title: '平均客单价',
      value: `¥${formatNumber(businessData.value?.avg_order_value || 0)}`,
      icon: 'Wallet',
      color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      trend: 'neutral' as const,
      change: '' // 后端暂未提供客单价增长率
    }
  ]
})

// 方法
const loadData = async () => {
  try {
    loading.revenue = true
    loading.payment = true
    loading.timeAnalysis = true
    loading.table = true

    // 加载真实数据
    const result = await financeService.getBusinessOverview(queryParams)
    if (result) {
      businessData.value = result
      generateChartDataFromReal(result)
      generateTableDataFromReal(result)
    } else {
      ElMessage.warning('暂无数据，请检查查询条件')
    }

  } catch (error: any) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败：' + (error.message || '未知错误'))
  } finally {
    loading.revenue = false
    loading.payment = false
    loading.timeAnalysis = false
    loading.table = false
  }
}


const generateChartDataFromReal = (data: any) => {
      //console.log('接收到的后端数据:', data) // 调试日志

  // 营业额趋势图数据处理
  if (data.revenue_trend && data.revenue_trend.length > 0) {
    revenueChartData.value = {
      dates: data.revenue_trend.map((item: any) => item.date),
      series: [{
        name: '营业额',
        data: data.revenue_trend.map((item: any) => item.amount),
        type: 'line'
      }]
    }
          //console.log('营业额趋势数据已处理:', revenueChartData.value)
  } else {
    // 如果没有数据，显示空图表
    revenueChartData.value = {
      dates: [],
      series: [{
        name: '营业额',
        data: [],
        type: 'line'
      }]
    }
          //console.log('营业额趋势数据为空，使用默认数据')
  }

  // 支付方式分析数据处理
  if (data.payment_analysis && data.payment_analysis.length > 0) {
    paymentChartData.value = data.payment_analysis
          //console.log('支付方式数据已处理:', paymentChartData.value)
  } else {
    paymentChartData.value = []
          //console.log('支付方式数据为空')
  }

  // 时段分析数据处理
  generateTimeAnalysisChartData(data)
}

const generateTimeAnalysisChartData = (data: any) => {
  // 使用真实的时段分析数据
  if (data.hour_analysis && data.hour_analysis.length > 0) {
    timeAnalysisData.value = {
      dates: data.hour_analysis.map((item: any) => `${item.hour}:00`),
      series: [{
        name: timeAnalysisType.value === 'revenue' ? '营业额' :
              timeAnalysisType.value === 'orders' ? '订单数' : '客流量',
        data: data.hour_analysis.map((item: any) =>
          timeAnalysisType.value === 'revenue' ? item.revenue :
          timeAnalysisType.value === 'orders' ? item.orders : item.customers
        ),
        type: timeAnalysisChartType.value
      }]
    }
  } else {
    // 如果没有数据，显示空图表
    timeAnalysisData.value = {
      dates: [],
      series: [{
        name: timeAnalysisType.value === 'revenue' ? '营业额' :
              timeAnalysisType.value === 'orders' ? '订单数' : '客流量',
        data: [],
        type: timeAnalysisChartType.value
      }]
    }
  }
}

const generateTableDataFromReal = (data: any) => {
  // 使用真实数据生成表格数据
  if (data.daily_list && data.daily_list.length > 0) {
    tableData.value = data.daily_list.map((item: any) => ({
      date: item.date,
      revenue: item.revenue || 0,
      orders: item.orders || 0,
      customers: item.customers || 0,
      avgOrderAmount: item.avg_order_value || 0,
      completionRate: item.completion_rate !== undefined ? item.completion_rate : 0,
      refundRate: item.refund_rate !== undefined ? item.refund_rate : 0,
      peakHours: item.peak_hours || '暂无数据'
    }))
  } else {
    // 如果没有详细数据，显示空表格
    tableData.value = []
  }

  pagination.total = tableData.value.length
  
  // 确保当前页不超出范围
  const maxPage = Math.ceil(pagination.total / pagination.pageSize)
  if (pagination.currentPage > maxPage) {
    pagination.currentPage = Math.max(1, maxPage)
  }
}

const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('zh-CN').format(num)
}

const getTrendIcon = (trend: string) => {
  switch (trend) {
    case 'up': return 'ArrowUp'
    case 'down': return 'ArrowDown'
    default: return 'Minus'
  }
}

const getCompletionRateType = (rate: number) => {
  if (rate >= 95) return 'success'
  if (rate >= 85) return 'warning'
  return 'danger'
}

const getRefundRateType = (rate: number) => {
  if (rate <= 2) return 'success'
  if (rate <= 5) return 'warning'
  return 'danger'
}

// 事件处理
const handleDateRangeChange = (dateRange: [string, string]) => {
  queryParams.start_date = dateRange[0]
  queryParams.end_date = dateRange[1]
  loadData()
}

const handleExport = async (options: any) => {
  try {
    if (!tableData.value || tableData.value.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    const dateRange = `${queryParams.start_date}_${queryParams.end_date}`
    const format = options?.format || 'excel'
    
    // 准备详细数据，转换为中文列名
    const detailData = tableData.value.map(item => ({
      '日期': item.date,
      '营业额': `¥${formatNumber(item.revenue)}`,
      '订单数': formatNumber(item.orders),
      '客流量': formatNumber(item.customers),
      '客单价': `¥${formatNumber(item.avgOrderAmount)}`,
      '完成率': `${item.completionRate}%`,
      '退单率': `${item.refundRate}%`,
      '高峰时段': item.peakHours
    }))

    // 准备概览数据
    const summaryOverview = [{
      '指标': '总营业额',
      '数值': `¥${formatNumber(businessData.value?.total_revenue || 0)}`,
      '说明': '报告期内总营业额'
    }, {
      '指标': '总订单数',
      '数值': formatNumber(businessData.value?.total_orders || 0),
      '说明': '报告期内总订单数'
    }, {
      '指标': '总客流量',
      '数值': formatNumber(businessData.value?.total_customers || 0),
      '说明': '报告期内总客流量'
    }, {
      '指标': '平均客单价',
      '数值': `¥${formatNumber(businessData.value?.avg_order_value || 0)}`,
      '说明': '报告期内平均客单价'
    }, {
      '指标': '完成订单率',
      '数值': `${(businessData.value?.completion_rate || 0).toFixed(1)}%`,
      '说明': '订单完成率'
    }, {
      '指标': '退单率',
      '数值': `${(businessData.value?.refund_rate || 0).toFixed(1)}%`,
      '说明': '订单退单率'
    }, {
      '指标': '会员占比',
      '数值': `${(businessData.value?.member_rate || 0).toFixed(1)}%`,
      '说明': '会员客户占比'
    }]

    // 动态导入导出工具
    const { exportToExcel, exportMultiSheetExcel, exportToCSV } = await import('@/utils/excelUtils')
    
    if (format === 'excel') {
      // 导出多工作表Excel
      await exportMultiSheetExcel([
        { data: summaryOverview, sheetName: '概览数据' },
        { data: detailData, sheetName: '详细数据' }
      ], `营业概况报告_${dateRange}`)
      ElMessage.success('Excel报告导出成功')
    } else if (format === 'csv') {
      // CSV只导出详细数据
      await exportToCSV(detailData, `营业概况详情_${dateRange}`)
      ElMessage.success('CSV导出成功')
    } else if (format === 'detail-only') {
      // 仅导出详细数据
      await exportToExcel(detailData, `营业概况详情_${dateRange}`, '详细数据')
      ElMessage.success('详细数据导出成功')
    }
  } catch (error: any) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败：' + (error.message || '未知错误'))
  }
}

// 专门用于表格导出的方法
const handleTableExport = async () => {
  try {
    if (!tableData.value || tableData.value.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    const dateRange = `${queryParams.start_date}_${queryParams.end_date}`
    
    // 准备表格数据，转换为中文列名
    const exportData = tableData.value.map(item => ({
      '日期': item.date,
      '营业额': `¥${formatNumber(item.revenue)}`,
      '订单数': formatNumber(item.orders),
      '客流量': formatNumber(item.customers),
      '客单价': `¥${formatNumber(item.avgOrderAmount)}`,
      '完成率': `${item.completionRate}%`,
      '退单率': `${item.refundRate}%`,
      '高峰时段': item.peakHours
    }))

    // 动态导入导出工具
    const { exportToExcel } = await import('@/utils/excelUtils')
    
    // 导出表格数据
    await exportToExcel(exportData, `营业概况详细数据_${dateRange}`, '详细数据')
    ElMessage.success('表格导出成功')
    
  } catch (error: any) {
    console.error('表格导出失败:', error)
    ElMessage.error('表格导出失败：' + (error.message || '未知错误'))
  }
}

const handleRefresh = () => {
  loadData()
}

const focusChart = (key: string) => {
  // 点击概览卡片聚焦到对应图表
  const element = document.querySelector(`.chart-${key}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const handleChartClick = (params: any) => {
  ElMessage.info(`查看 ${params.name} 的详细数据`)
}

// viewDetails 函数已移除，因为操作列只保留导出功能

const exportSingle = async (row: any) => {
  try {
    // 准备单行导出数据
    const exportData = [{
      '日期': row.date,
      '营业额': `¥${formatNumber(row.revenue)}`,
      '订单数': formatNumber(row.orders),
      '客流量': formatNumber(row.customers),
      '客单价': `¥${formatNumber(row.avgOrderAmount)}`,
      '完成率': `${row.completionRate}%`,
      '退单率': `${row.refundRate}%`,
      '高峰时段': row.peakHours
    }]

    // 动态导入导出工具
    const { exportToExcel } = await import('@/utils/excelUtils')
    
    // 导出单日数据
    await exportToExcel(exportData, `营业概况_${row.date}`, '单日概况')
    ElMessage.success(`${row.date} 数据导出成功`)
    
  } catch (error: any) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败：' + (error.message || '未知错误'))
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  // 计算新的页面大小下，当前数据应该在第几页
  const maxPage = Math.ceil(pagination.total / size)
  if (pagination.currentPage > maxPage) {
    pagination.currentPage = Math.max(1, maxPage)
  }
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

const handleTimeAnalysisChartTypeChange = (type: string) => {
  timeAnalysisChartType.value = type as 'line' | 'bar' | 'area'
}

// 监听时段分析类型变化
watch(timeAnalysisType, () => {
  if (businessData.value) {
    generateTimeAnalysisChartData(businessData.value)
  }
})

// 监听时段分析图表类型变化
watch(timeAnalysisChartType, () => {
  if (businessData.value) {
    generateTimeAnalysisChartData(businessData.value)
  }
})

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
// 确保整个页面组件占满全宽
.business-overview-detail {width: 100%;max-width: none;background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);min-height: 100vh;
  // 统一所有卡片的圆角和阴影
  :deep(.el-card) {border-radius:4px;border: none;box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);height:104px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);backdrop-filter: blur(10px);
    &:hover {box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);transform: translateY(-2px);}
  }
  // 优化加载动画
  :deep(.el-loading-mask) {background-color: rgba(255, 255, 255, 0.8);backdrop-filter: blur(10px);border-radius: 4px;
    .el-loading-spinner {
      .el-loading-text {color: #64748b;font-weight: 500;}
    }
  }
  // 优化标签样式
  :deep(.el-tag) {border-radius: 8px;font-weight: 600;font-size: 12px;padding: 4px 10px;border: none;
    &.el-tag--success {
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.15) 100%);
      color: #059669;border: 1px solid rgba(16, 185, 129, 0.3);}
    &.el-tag--warning {
      background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.15) 100%);
      color: #d97706;border: 1px solid rgba(245, 158, 11, 0.3);}
    &.el-tag--danger {
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(220, 38, 38, 0.15) 100%);
      color: #dc2626;border: 1px solid rgba(239, 68, 68, 0.3);
    }
  }
}
@media (max-width: 1200px){
    .business-overview-detail {width: 100%;max-width: none;background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);min-height: 100vh;
        // 统一所有卡片的圆角和阴影
        :deep(.el-card) {border-radius:4px;border: none;box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);height:91px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);backdrop-filter: blur(10px);
            &:hover {box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);transform: translateY(-2px);}
        }
        // 优化加载动画
        :deep(.el-loading-mask) {background-color: rgba(255, 255, 255, 0.8);backdrop-filter: blur(10px);border-radius: 4px;
            .el-loading-spinner {
                .el-loading-text {color: #64748b;font-weight: 500;}
            }
        }
        // 优化标签样式
        :deep(.el-tag) {border-radius: 8px;font-weight: 600;font-size: 12px;padding: 4px 10px;border: none;
            &.el-tag--success {
                background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.15) 100%);
                color: #059669;border: 1px solid rgba(16, 185, 129, 0.3);}
            &.el-tag--warning {
                background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.15) 100%);
                color: #d97706;border: 1px solid rgba(245, 158, 11, 0.3);}
            &.el-tag--danger {
                background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(220, 38, 38, 0.15) 100%);
                color: #dc2626;border: 1px solid rgba(239, 68, 68, 0.3);
            }
        }
    }
}
// 确保ReportPageLayout组件占满全宽
:deep(.report-page-layout){width:100%;max-width:none;padding:10px 10px 20px 10px !important;}
:deep(.report-page-layout .page-header){border-radius:0;margin-bottom:0;padding:15px 25px;}
:deep(.report-page-layout .summary-section){margin-bottom:0;margin-top:10px;}
:deep(.report-page-layout .page-header .header-left .title-section .page-title){gap:10px;font-size:22px;}
:deep(.report-page-layout .page-header .header-left .title-section .page-title .el-icon){font-size:28px;}
:deep(.report-page-layout .page-header .header-left .title-section .page-subtitle){font-size:14px;color:#999;margin-top:6px;}
:deep(.report-page-layout .page-header .header-right .quick-date-buttons){border-radius:0;padding:8px 12px;margin-right:0;}
:deep(.report-page-layout .page-header .header-right .quick-date-buttons::before){border-radius:0;padding:8px 12px;}
:deep(.report-page-layout .page-header .header-right .quick-date-buttons .quick-date-btn){border-radius:4px;height:28px;width:60px;}
:deep(.report-page-layout .page-header .header-right .quick-date-buttons .quick-date-btn.el-button--primary::after){
    border-radius:4px;height:28px;}
:deep(.report-page-layout .page-header .header-right .el-button.el-button--primary .el-icon){margin-right:5px;}
:deep(.report-page-layout .page-header .header-right .el-button){border-radius:4px;height:32px;width:100px;}
:deep(.report-page-layout .el-row){margin-left:-5px !important;margin-right:-5px !important;width:auto !important;}
:deep(.report-page-layout .el-col){padding-left:5px !important;padding-right:5px !important;}
:deep(.report-page-layout .main-content){margin-bottom:0;margin-top:10px;}
:deep(.report-page-layout .main-content .el-card){border-radius:0;height:auto;}
@media (max-width: 1200px){
    :deep(.report-page-layout){padding:5px 5px 10px 5px !important;}
    :deep(.report-page-layout .page-header){padding:10px 20px;}
    :deep(.report-page-layout .page-header .header-left){display:inline-flex;width:240px;}
    :deep(.report-page-layout .summary-section){margin-top:5px;}
    :deep(.report-page-layout .page-header .header-left .title-section .page-title){font-size:20px;}
    :deep(.report-page-layout .page-header .header-left .title-section .page-title .el-icon){font-size:24px;}
    :deep(.report-page-layout .page-header .header-left .title-section .page-subtitle){font-size:12px;margin-top:6px;}
    :deep(.report-page-layout .page-header .header-right){display:inline-flex;flex:1;gap:6px 12px;}
    :deep(.report-page-layout .page-header .header-right .quick-date-buttons){padding:4px 12px;}
    :deep(.report-page-layout .page-header .header-right .quick-date-buttons::before){padding:4px 12px;}
    :deep(.report-page-layout .page-header .header-right .quick-date-buttons .quick-date-btn){border-radius:4px;height:28px;width:60px;}
    :deep(.report-page-layout .page-header .header-right .quick-date-buttons .quick-date-btn.el-button--primary::after){
        border-radius:4px;height:28px;}
    :deep(.report-page-layout .page-header .header-right .el-button){border-radius:4px;height:32px;width:100px;}
    :deep(.report-page-layout .el-row){margin-left:-3px !important;margin-right:-3px !important;width:auto !important;}
    :deep(.report-page-layout .el-col){padding-left:3px !important;padding-right:3px !important;}
    :deep(.report-page-layout .main-content){margin-bottom:0;margin-top:5px;}
    :deep(.report-page-layout .main-content .el-card){border-radius:0;height:auto;}
}
// 确保页面头部占满宽度
:deep(.page-header) {width:100%;max-width: none;margin-left: 0;margin-right: 0;}
// 确保概览数据区域占满宽度
:deep(.summary-section) {width: 100%;max-width: none;margin-left: 0;margin-right: 0;}
// 确保内容区域占满宽度
:deep(.main-content) {width: 100%;max-width: none;box-sizing: border-box;margin-left: 0;margin-right: 0;}
// 确保表格区域占满宽度
:deep(.table-section) {width: 100%;max-width: none;margin-left: 0;margin-right: 0;
  .el-card {width: 100%;
    .el-card__body {padding: 16px;}
  }
}
:deep(.report-page-layout .main-content .el-card .el-card__body){padding:20px;}
:deep(.report-page-layout .table-section){margin-top:10px;margin-bottom:0;}
// 统一内容区域间距 - 更紧凑的布局
.content-section {margin-bottom: 10px;
  &:last-child {margin-bottom: 0;}
  // 统一卡片内边距 - 更紧凑
  :deep(.el-card__body) {padding: 16px;}
}
@media (max-width: 1200px){
    :deep(.report-page-layout .main-content .el-card .el-card__body){padding:15px;}
    :deep(.report-page-layout .table-section){margin-top:5px;margin-bottom:0;}
    .content-section {margin-bottom: 5px;
        &:last-child {margin-bottom: 0;}
        // 统一卡片内边距 - 更紧凑
        :deep(.el-card__body) {padding: 16px;}
    }
}
// 确保卡片布局正确
.summary-card {cursor: pointer;transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);border: none;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);border-radius: 16px;overflow: hidden;position: relative;
  &::before {content: '';position: absolute;top: 0;left: 0;right: 0;height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    opacity: 0;transition: opacity 0.3s ease;}
  &:hover {transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
    &::before {opacity:1;}
    .summary-icon{transform: scale(1.1) rotate(5deg);}
  }
  .summary-content {display: flex;align-items: center;height: 100%;padding:0;position: relative;
    .summary-icon {width:64px;height:64px;border-radius:8px;display:flex;align-items: center;justify-content: center;margin-right:20px;color: white;
      font-size: 28px;box-shadow:0 8px 20px rgba(0, 0, 0, 0.15);transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);position: relative;
      &::after {content:'';position:absolute;inset:-2px;border-radius:8px;
          background:linear-gradient(45deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05));opacity:0;transition: opacity 0.3s ease;
      }
      &:hover::after{opacity:1;}
    }
    .summary-info {flex: 1;
      .summary-title{font-size:15px;font-weight:500;color:#333;margin-bottom:8px;letter-spacing:1px;}
      .summary-value {font-size:24px;font-weight:bold;color: #000;line-height: 1.4;
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        -webkit-background-clip:text;-webkit-text-fill-color: transparent;background-clip: text;}
      .summary-trend {display: flex;align-items: center;gap: 6px;
        font-size:12px;font-weight: 600;padding:4px 12px;border-radius:4px;width: fit-content;backdrop-filter: blur(10px);
        &.up {color: #059669;background: rgba(16, 185, 129, 0.1);border: 1px solid rgba(16, 185, 129, 0.2);}
        &.down {color: #dc2626;background: rgba(239, 68, 68, 0.1);border: 1px solid rgba(239, 68, 68, 0.2);}
        &.neutral {color: #6b7280;background: rgba(107, 114, 128, 0.1);border: 1px solid rgba(107, 114, 128, 0.2);}
        .el-icon {font-size: 14px;}
      }
    }
  }
}
@media (max-width: 1200px){
    :deep(.el-range-editor.el-input__wrapper){flex:unset;}
    .summary-card {cursor: pointer;transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);border: none;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);border-radius: 16px;overflow: hidden;position: relative;
        &::before {content: '';position: absolute;top: 0;left: 0;right: 0;height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            opacity: 0;transition: opacity 0.3s ease;}
        &:hover {transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            &::before {opacity:1;}
            .summary-icon{transform: scale(1.1) rotate(5deg);}
        }
        .summary-content {display: flex;align-items: center;height: 100%;padding:0;position: relative;
            .summary-icon {width:48px;height:48px;border-radius:8px;display:flex;align-items: center;justify-content: center;margin-right:20px;color: white;
                font-size: 28px;box-shadow:0 8px 20px rgba(0, 0, 0, 0.15);transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);position: relative;
                &::after {content:'';position:absolute;inset:-2px;border-radius:8px;
                    background:linear-gradient(45deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05));opacity:0;transition: opacity 0.3s ease;
                }
                &:hover::after{opacity:1;}
            }
            .summary-info {flex: 1;
                .summary-title{font-size:14px;font-weight:500;color:#333;margin-bottom:5px;letter-spacing:1px;}
                .summary-value {font-size:18px;font-weight:bold;color: #000;line-height:1.4;
                    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
                    -webkit-background-clip:text;-webkit-text-fill-color: transparent;background-clip: text;}
                .summary-trend {display: flex;align-items: center;gap:5px;
                    font-size:12px;font-weight: 600;padding:2px 10px;border-radius:4px;width: fit-content;backdrop-filter: blur(10px);
                    &.up {color: #059669;background: rgba(16, 185, 129, 0.1);border: 1px solid rgba(16, 185, 129, 0.2);}
                    &.down {color: #dc2626;background: rgba(239, 68, 68, 0.1);border: 1px solid rgba(239, 68, 68, 0.2);}
                    &.neutral {color: #6b7280;background: rgba(107, 114, 128, 0.1);border: 1px solid rgba(107, 114, 128, 0.2);}
                    .el-icon {font-size: 14px;}
                }
            }
        }
    }
}



.chart-card {width: 100%;height: 100%;border-radius:4px;border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);transition: all 0.3s ease;overflow: hidden;
  &:hover {box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);transform: translateY(-2px);}
  :deep(.el-card__header){background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);padding:10px 20px !important;}
  .chart-header{display: flex;justify-content: space-between;align-items: center;font-weight: 600;font-size: 18px;color: #333;height:30px;
    .chart-controls {display: flex;align-items: center;
      :deep(.el-radio-group){background: rgba(255, 255, 255, 0.8);border-radius:4px;padding:0;backdrop-filter: blur(10px);
        .el-radio-button {margin:0;
          &:first-child .el-radio-button__inner{border-top-left-radius:4px;border-bottom-left-radius:4px;}
          &:last-child .el-radio-button__inner{border-top-right-radius:4px;border-bottom-right-radius:4px;}
          &.is-active .el-radio-button__inner {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-color: transparent;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
            color: white;
            transform: scale(1.05);
          }
          .el-radio-button__inner {
            font-weight: 500;
            padding: 8px 16px;
            font-size: 13px;
            border: none;
            background-color: transparent;
            color: #64748b;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            &:hover {
              background-color: rgba(59, 130, 246, 0.1);
              color: #3b82f6;
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }
  .chart-type-controls {display:flex;justify-content:flex-end;padding:0;margin-bottom:20px;
    :deep(.el-radio-group) {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
      border-radius:4px;padding:0;backdrop-filter: blur(10px);
      border:1px solid rgba(226, 232, 240, 0.5);box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      .el-radio-button {
        &:first-child .el-radio-button__inner {
          border-top-left-radius:4px;
          border-bottom-left-radius:4px;
        }
        &:last-child .el-radio-button__inner {
          border-top-right-radius:4px;
          border-bottom-right-radius:4px;
        }
        &.is-active .el-radio-button__inner {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          border-color: transparent;
          box-shadow:0 3px 10px rgba(16, 185, 129, 0.4);
          color: white;
          transform: scale(1.02);
        }
        .el-radio-button__inner {
          font-size: 12px;
          font-weight: 500;
          padding: 8px 14px;
          background-color: transparent;
          border: none;
          color: #64748b;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          &:hover {
            background-color: rgba(16, 185, 129, 0.1);
            color: #10b981;
            transform: translateY(-1px);
          }
        }
      }
    }
  }

  .chart-container{min-height:280px;padding:0;width:100%;}
  &.chart-card-fixed-height {
    min-height: 480px; // 增加最小高度
    .chart-container {
      min-height: 400px; // 设置最小高度而不是固定高度
      width: 100%;
      padding: 0; // 移除内边距，让图表组件自己处理
      // 确保内部的图表组件占满容器
      :deep(.sales-chart) {
        width: 100%;
        height: 100%;
        border: none;
        box-shadow: none;
        .chart-header {display: none;} // 隐藏组件内部的header，使用卡片的header
        .chart-container{height:400px !important;padding:0;}
      }
      // 确保ECharts容器填满整个区域
      :deep(.echarts-container) {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }
}
:deep(.sales-chart){margin-bottom:0;box-shadow:unset;}
.data-analysis-card {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  border: none;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  :deep(.el-card__header){background:linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);border-bottom: 1px solid rgba(226, 232, 240, 0.8);padding:10px 20px !important;
    span {font-weight:600;font-size:18px;height:30px;color:#333;display:flex;align-items:center;}
  }
  .analysis-content{padding:0;
    .metric-row {display: flex;gap:10px;margin-bottom:10px;
      &:last-child {margin-bottom: 0;}
      .metric-item {
        flex: 1;
        text-align: center;
        padding:10px;
        border-radius:4px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
        border: 1px solid rgba(226, 232, 240, 0.6);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444, #8b5cf6);
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);

          &::before {
            opacity: 1;
          }
        }

        .metric-label {
          font-size: 14px;
          font-weight: 500;
          color: #64748b;
          margin-bottom: 12px;
          letter-spacing: 0.3px;
        }

        .metric-value {font-size: 24px;font-weight: 700;line-height: 1.2;
          &.primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
          &.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
          &.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
          &.danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
          &.info {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
        }

        .metric-change {
          font-size: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;

          &.up { color: #10b981; }
          &.down { color: #ef4444; }
          &.neutral { color: #6b7280; }
        }
      }
    }
  }
}
.amount-text {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  font-size: 15px;
}
.number-text {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  font-size: 15px;
}
.time-text {
  color: #f59e0b;
  font-size: 13px;
  font-weight: 600;
  padding: 4px 8px;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(245, 158, 11, 0.2);
}
// 表格样式优化
:deep(.el-table) {
  .el-table__header {
    background-color: #f8f9fa;

    th {
      background-color: #f8f9fa !important;
      color: #606266;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background-color: #f5f7fa !important;
    }
  }
}
.table-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  padding: 0 20px;

  :deep(.el-pagination) {
    .el-pagination__total {
      color: #64748b;
      font-weight: 500;
    }

    .el-select {
      .el-input__wrapper {
        border-radius: 10px;
        border: 1px solid rgba(226, 232, 240, 0.8);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }
      }
    }

    .btn-prev, .btn-next {
      border-radius: 10px;
      border: 1px solid rgba(226, 232, 240, 0.8);
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border-color: transparent;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }
    }

    .el-pager {
      li {
        border-radius: 10px;
        margin: 0 2px;
        border: 1px solid rgba(226, 232, 240, 0.8);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
          border-color: #3b82f6;
          transform: translateY(-1px);
        }

        &.is-active {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          color: white;
          border-color: transparent;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
      }
    }

    .el-pagination__jump {
      .el-input__wrapper {
        border-radius: 10px;
        border: 1px solid rgba(226, 232, 240, 0.8);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }
      }
    }
  }
}
@media (max-width: 768px) {
  .data-analysis-card .analysis-content .metric-row {
    flex-direction: column;
    gap: 16px;
  }

  .chart-card .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .chart-controls {
      width: 100%;
      justify-content: center;

      :deep(.el-radio-group) .el-radio-button .el-radio-button__inner {
        padding: 8px 12px;
        font-size: 13px;
      }
    }
  }
}
:deep(.payment-analysis-chart .chart-container){background:unset;}
:deep(.report-page-layout .table-section .table-card){height:auto;border-radius:0;}
:deep(.report-page-layout .table-section .table-card .el-card__header){padding:10px 20px;}
:deep(.report-page-layout .table-section .table-card .table-header){height:30px;}
:deep(.report-page-layout .table-section .table-card .table-header .table-actions .el-button){border-radius:4px;padding:8px 16px;height:30px;}
:deep(.report-page-layout .table-section .table-card .table-header .table-actions .el-button .el-icon){margin-right:5px;}
:deep(.report-page-layout .table-section .table-card .el-table .el-table__body-wrapper .el-table__body tr td){padding:8px 0;}
:deep(.report-page-layout .table-section .table-card .el-table .el-table__body-wrapper .el-table__body tr td .amount-text){
    font-weight:normal;color:#2c9a4d;}
:deep(.report-page-layout .table-section .table-card .el-table .el-table__body-wrapper .el-table__body tr td .number-text){
    font-weight:normal;color:#2c9a4d;}
:deep(.report-page-layout .table-section .table-card .el-table .el-table__header-wrapper .el-table__header th){border-bottom:1px;}
.business-overview-detail .el-tag{border-radius:4px;}
:deep(.report-page-layout .table-section .table-card .el-table .el-table__body-wrapper .el-table__body tr td .time-text){border-radius:4px;}
:deep(.table-pagination .el-pagination .btn-prev){border-radius:4px;}
:deep(.table-pagination .el-pagination .btn-next){border-radius:4px;}
:deep(.table-pagination .el-pagination .el-pager li){border-radius:4px;margin:0  4px;}
:deep(.table-pagination .el-pagination .el-pagination__jump .el-input__wrapper){border-radius:4px;box-shadow:unset;}
</style>
