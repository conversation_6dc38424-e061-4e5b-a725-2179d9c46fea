<template>
  <div class="register_wrap">
      <div class="fixed_index">
          <el-icon :size="20"><HomeFilled /></el-icon>
          九软官网
      </div>
      <div class="version">当前版本：V1.0.0</div>
      <div class="register_main">
          <div class="register_header">
              <div class="logo-container">
                  <img src="../assets/images/logo.png" alt="">
              </div>
              <div class="title-container">
                  <h1>九软门店收银管理系统</h1>
                  <p class="subtitle">创建您的账户</p>
              </div>
          </div>
          <form @submit.prevent="handleRegister" autocomplete="off" class="register_form">
                <div class="form-group">
                    <label class="form-label">手机号</label>
                    <div class="input-wrapper">
                        <el-input
                            v-model="mobile"
                            placeholder="请输入11位手机号"
                            size="large"
                            class="form-input"
                            maxlength="11"
                            clearable
                            :class="{ 'is-error': mobileError, 'is-success': mobile && !mobileError }"
                            @blur="validateMobile"
                            @input="clearMobileError"
                        >
                            <template #prefix>
                                <el-icon class="input-icon"><Phone /></el-icon>
                            </template>
                        </el-input>
                        <transition name="error-fade">
                            <div v-if="mobileError" class="error-message">
                                <el-icon><WarningFilled /></el-icon>
                                {{ mobileError }}
                            </div>
                        </transition>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">登录密码</label>
                    <div class="input-wrapper">
                        <el-input
                            v-model="password"
                            placeholder="请输入6-20位密码"
                            type="password"
                            size="large"
                            class="form-input"
                            show-password
                            :class="{ 'is-error': passwordError, 'is-success': password && !passwordError }"
                            @blur="validatePassword"
                            @input="clearPasswordError"
                        >
                            <template #prefix>
                                <el-icon class="input-icon"><Lock /></el-icon>
                            </template>
                        </el-input>
                        <transition name="error-fade">
                            <div v-if="passwordError" class="error-message">
                                <el-icon><WarningFilled /></el-icon>
                                {{ passwordError }}
                            </div>
                        </transition>
                        <transition name="strength-fade">
                            <div v-if="password && !passwordError" class="password-strength">
                                <div class="strength-info">
                                    <span class="strength-label">密码强度</span>
                                    <span class="strength-text" :class="'strength-' + passwordStrength">{{ passwordStrengthText }}</span>
                                </div>
                                <div class="strength-bar">
                                    <div 
                                        class="strength-segment" 
                                        v-for="i in 3" 
                                        :key="i"
                                        :class="{ 
                                            'active': passwordStrength >= i,
                                            'weak': passwordStrength === 1 && i === 1,
                                            'medium': passwordStrength === 2 && i <= 2,
                                            'strong': passwordStrength === 3 && i <= 3
                                        }"
                                    ></div>
                                </div>
                            </div>
                        </transition>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">手机验证码</label>
                    <div class="input-wrapper">
                        <div class="captcha-container">
                            <el-input
                                v-model="captcha"
                                placeholder="请输入6位验证码"
                                size="large"
                                class="form-input captcha-input"
                                maxlength="6"
                                :class="{ 'is-error': captchaError, 'is-success': captcha && !captchaError }"
                                @blur="validateCaptcha"
                                @input="clearCaptchaError"
                            >
                                <template #prefix>
                                    <el-icon class="input-icon"><Message /></el-icon>
                                </template>
                            </el-input>
                            <el-button 
                                class="captcha-btn" 
                                :type="captchaCountdown > 0 ? 'info' : 'primary'"
                                size="large"
                                :disabled="!mobile || mobileError || captchaCountdown > 0 || sendingCaptcha"
                                :loading="sendingCaptcha"
                                @click="sendCaptcha"
                            >
                                {{ captchaButtonText }}
                            </el-button>
                        </div>
                        <transition name="error-fade">
                            <div v-if="captchaError" class="error-message">
                                <el-icon><WarningFilled /></el-icon>
                                {{ captchaError }}
                            </div>
                        </transition>
                    </div>
                </div>
                
              <!-- 注册按钮 -->
                  <div class="submit-section">
                      <el-button 
                          class="register-btn"
                          type="primary"
                          size="large"
                          :disabled="!canRegister || loading"
                          :loading="loading"
                          native-type="submit"
                          round
                      >
                          {{ loading ? '注册中...' : '立即注册' }}
                      </el-button>
                  </div>
          </form>
          <div class="register_footer">
              <div class="register_footer_li" @click="goToLogin">已有账号，去登录？</div>
              <div class="register_footer_li" @click="ewm_flag = true">
                  <el-icon :size="18"><Iphone /></el-icon>
                  九软员工端
              </div>
          </div>
          <!---------------九软商家端的弹框----------------->
          <div class="alert_bg" v-show="ewm_flag" @click="ewm_flag = false"></div>
          <div class="alert_down" v-show="ewm_flag">
              <div class="alert_down_header">
                  <b>九软员工端</b>
                  <el-icon :size="20" @click="ewm_flag = false"><Close /></el-icon>
              </div>
              <div class="ewm_ul">
                  <div class="ewm_li">
                      <b><img src="../assets/images/ewm.png" alt=""></b>
                      <span>安卓手机端下载</span>
                  </div>
                  <div class="ewm_li">
                      <b><img src="../assets/images/ewm.png" alt=""></b>
                      <span>微信小程序端</span>
                  </div>
              </div>
          </div>
      </div>
      <div class="copyright">Copyright © 2024 九软信息技术
          <a href="https://beian.miit.gov.cn" target="_blank" class="beian">浙ICP备16015472号-1</a>
      </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElInput, ElButton, ElMessage, ElIcon } from 'element-plus'
import { HomeFilled, Iphone, Close, Phone, Lock, Message, WarningFilled } from '@element-plus/icons-vue'
import { throttle } from '@/utils'

const router = useRouter()
const mobile = ref('')
const password = ref('')
const captcha = ref('')
const loading = ref(false)
const sendingCaptcha = ref(false)
const isMobile = ref(false)
const ewm_flag = ref(false)
const captchaCountdown = ref(0)

// 错误信息
const mobileError = ref('')
const passwordError = ref('')
const captchaError = ref('')

// 倒计时定时器
let countdownTimer: NodeJS.Timeout | null = null

function checkMobile() {
  isMobile.value = window.innerWidth < 800
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})

// 手机号验证
function validateMobile() {
  if (!mobile.value.trim()) {
    mobileError.value = '请输入手机号'
    return false
  }
  if (!/^1[3-9]\d{9}$/.test(mobile.value)) {
    mobileError.value = '请输入正确的手机号格式'
    return false
  }
  mobileError.value = ''
  return true
}

function clearMobileError() {
  if (mobileError.value) {
    mobileError.value = ''
  }
}

// 密码验证和强度检测
function validatePassword() {
  if (!password.value.trim()) {
    passwordError.value = '请输入登录密码'
    return false
  }
  if (password.value.length < 6) {
    passwordError.value = '密码长度不能少于6位'
    return false
  }
  if (password.value.length > 20) {
    passwordError.value = '密码长度不能超过20位'
    return false
  }
  passwordError.value = ''
  return true
}

function clearPasswordError() {
  if (passwordError.value) {
    passwordError.value = ''
  }
}

// 密码强度计算
const passwordStrength = computed(() => {
  if (!password.value) return 0
  
  let strength = 0
  // 长度检查
  if (password.value.length >= 6) strength++
  // 包含数字和字母
  if (/[0-9]/.test(password.value) && /[a-zA-Z]/.test(password.value)) strength++
  // 包含特殊字符或长度超过8位
  if (/[^a-zA-Z0-9]/.test(password.value) || password.value.length >= 8) strength++
  
  return strength
})

const passwordStrengthText = computed(() => {
  switch (passwordStrength.value) {
    case 1: return '弱'
    case 2: return '中'
    case 3: return '强'
    default: return ''
  }
})

// 验证码验证
function validateCaptcha() {
  if (!captcha.value.trim()) {
    captchaError.value = '请输入验证码'
    return false
  }
  if (!/^\d{6}$/.test(captcha.value)) {
    captchaError.value = '请输入6位数字验证码'
    return false
  }
  captchaError.value = ''
  return true
}

function clearCaptchaError() {
  if (captchaError.value) {
    captchaError.value = ''
  }
}

// 验证码按钮文本
const captchaButtonText = computed(() => {
  if (captchaCountdown.value > 0) {
    return `${captchaCountdown.value}s后重发`
  }
  return '发送验证码'
})

// 发送验证码
const sendCaptcha = throttle(async () => {
  if (!validateMobile()) {
    return
  }

  sendingCaptcha.value = true
  try {
    // 这里暂时模拟发送验证码的过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('验证码已发送')
    
    // 开始倒计时
    captchaCountdown.value = 60
    countdownTimer = setInterval(() => {
      captchaCountdown.value--
      if (captchaCountdown.value <= 0) {
        clearInterval(countdownTimer!)
        countdownTimer = null
      }
    }, 1000)
  } catch (error) {
    ElMessage.error('验证码发送失败，请重试')
  } finally {
    sendingCaptcha.value = false
  }
}, 3000)

// 是否可以注册
const canRegister = computed(() => {
  return mobile.value && password.value && captcha.value && 
         !mobileError.value && !passwordError.value && !captchaError.value
})

// 注册处理
const handleRegister = throttle(async () => {
  // 表单验证
  const isMobileValid = validateMobile()
  const isPasswordValid = validatePassword()
  const isCaptchaValid = validateCaptcha()
  
  if (!isMobileValid || !isPasswordValid || !isCaptchaValid) {
    return
  }

  if (loading.value) {
    return
  }

  loading.value = true
  try {
    // 这里暂时模拟注册过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('注册成功！即将跳转到登录页面')
    
    // 延迟跳转到登录页面
    setTimeout(() => {
      router.push('/login')
    }, 1500)
  } catch (error) {
    ElMessage.error('注册失败，请重试')
  } finally {
    loading.value = false
  }
}, 3000)

// 跳转到登录页
function goToLogin() {
  router.push('/login')
}
</script>

<style scoped>
/* 主容器 */
.register_wrap {
  width: 100%;
  height: 100vh;
  background: url(../assets/images/login_bg.jpg) center center no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 注册卡片 */
.register_main {
  width: 560px;
  max-width: 90vw;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 头部区域 */
.register_header {
  padding: 40px 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, #2c9a4d 0%, #214334 100%);
  color: white;
  position: relative;
}

.register_header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: white;
  border-radius: 20px 20px 0 0;
}

.logo-container {
  margin-bottom: 16px;
}

.logo-container img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.title-container h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  letter-spacing: 0.5px;
}

.subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

/* 表单区域 */
.register_form {
  padding: 30px 40px 40px;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.input-wrapper {
  position: relative;
}

/* 输入框样式 */
:deep(.form-input .el-input__wrapper) {
  background: #f0f5f3;
  border: 2px solid #ddd;
  border-radius: 12px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  box-shadow: none;
}

:deep(.form-input .el-input__wrapper:hover) {
  border-color: #2c9a4d;
  background: #f0f5f3;
}

:deep(.form-input .el-input__wrapper.is-focus) {
  border-color: #2c9a4d;
  background: white;
  box-shadow: 0 0 0 3px rgba(44, 154, 77, 0.1);
}

:deep(.form-input.is-error .el-input__wrapper) {
  border-color: #ef4444;
  background: #fef2f2;
}

:deep(.form-input.is-success .el-input__wrapper) {
  border-color: #2c9a4d;
  background: #f0f5f3;
}

:deep(.form-input .el-input__inner) {
  color: #1f2937;
  font-size: 15px;
  height: auto;
}

:deep(.form-input .el-input__prefix) {
  padding-right: 8px;
}

.input-icon {
  color: #6b7280;
  font-size: 18px;
}

/* 错误提示 */
.error-message {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ef4444;
  font-size: 13px;
  margin-top: 6px;
  padding: 0 4px;
}

.error-fade-enter-active,
.error-fade-leave-active {
  transition: all 0.3s ease;
}

.error-fade-enter-from,
.error-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 密码强度 */
.password-strength {
  margin-top: 12px;
  padding: 12px;
  background: #f0f5f3;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.strength-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.strength-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

.strength-text {
  font-size: 13px;
  font-weight: 600;
}

.strength-text.strength-1 { color: #ef4444; }
.strength-text.strength-2 { color: #f59e0b; }
.strength-text.strength-3 { color: #2c9a4d; }

.strength-bar {
  display: flex;
  gap: 4px;
  height: 4px;
}

.strength-segment {
  flex: 1;
  background: #e5e7eb;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.strength-segment.active.weak { background: #ef4444; }
.strength-segment.active.medium { background: #f59e0b; }
.strength-segment.active.strong { background: #2c9a4d; }

.strength-fade-enter-active,
.strength-fade-leave-active {
  transition: all 0.3s ease;
}

.strength-fade-enter-from,
.strength-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 验证码容器 */
.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-btn {
  height: 48px;
  min-width: 120px;
  border-radius: 12px;
  font-weight: 500;
  white-space: nowrap;
}

/* 提交按钮区域 */
.submit-section {
  margin-top: 32px;
}

.register-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: #2c9a4d;
  border: none;
  box-shadow: 0 4px 12px rgba(44, 154, 77, 0.3);
  transition: all 0.3s ease;
}

.register-btn:hover:not(:disabled) {
  background: #c4272e;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(196, 39, 46, 0.4);
}

.register-btn:active:not(:disabled) {
  transform: translateY(0);
}

.register-btn:disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

/* 底部区域 */
.register_footer {
  background: #e1efe9;
  padding: 20px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #ddd;
}

.register_footer_li {
  font-size: 14px;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.register_footer_li:hover {
  color: #c4272e;
}

/* 版权信息 */
.copyright {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.copyright a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  margin-left: 8px;
}

.copyright a:hover {
  color: white;
}

/* 顶部导航 */
.version {
  position: fixed;
  top: 20px;
  right: 20px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  opacity: 0.8;
}

.fixed_index {
  position: fixed;
  top: 20px;
  left: 20px;
  background: #214334;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.fixed_index:hover {
  opacity: 1.0;
}

/* 弹窗样式 */
.alert_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 999;
}

.alert_down {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  max-width: 90vw;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.alert_down_header {
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert_down_header b {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.alert_down_header .el-icon {
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.alert_down_header .el-icon:hover {
  color: #c4272e;
}

.ewm_ul {
  padding: 20px;
  display: flex;
  gap: 20px;
}

.ewm_li {
  flex: 1;
  text-align: center;
}

.ewm_li b {
  display: block;
  width: 120px;
  height: 120px;
  margin: 0 auto 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ewm_li img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ewm_li span {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .register_main {
    width: 100%;
    max-width: calc(100vw - 32px);
    margin: 16px;
    border-radius: 12px;
  }

  .register_header {
    padding: 30px 24px 20px;
  }

  .title-container h1 {
    font-size: 20px;
  }

  .register_form {
    padding: 24px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .register_footer {
    padding: 16px 24px;
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .captcha-container {
    flex-direction: column;
    gap: 8px;
  }

  .captcha-btn {
    width: 100%;
  }

  .alert_down {
    width: calc(100vw - 32px);
    margin: 16px;
  }

  .ewm_ul {
    flex-direction: column;
    align-items: center;
  }

  .ewm_li {
    max-width: 200px;
  }

  .version,
  .fixed_index {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    margin: 8px;
    display: inline-block;
  }
}

@media (max-width: 560px) {
  .logo-container img {
    width: 48px;
    height: 48px;
  }

  .title-container h1 {
    font-size: 18px;
  }

  .subtitle {
    font-size: 13px;
  }

  :deep(.form-input .el-input__wrapper) {
    padding: 10px 14px;
  }

  .register-btn {
    height: 44px;
    font-size: 15px;
  }

  .captcha-btn {
    height: 44px;
    min-width: 100px;
    font-size: 13px;
  }
}
</style>