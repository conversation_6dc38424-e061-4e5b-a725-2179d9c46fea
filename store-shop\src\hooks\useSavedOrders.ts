import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { SavedOrder, SavedOrderListItem } from '@/types/order'
import type { CartItem } from '@/types/retail'

const STORAGE_KEY = 'saved_orders'

// 全局状态，确保单例
const savedOrders = ref<SavedOrder[]>([])
const isLoading = ref(false)
const error = ref<string | null>(null)

// 初始化标志
let isInitialized = false

export function useSavedOrders() {
  // 计算属性
  const orderCount = computed(() => savedOrders.value.length)
  const totalSavedAmount = computed(() => 
    savedOrders.value.reduce((sum, order) => sum + order.totalAmount, 0)
  )

  // 加载存单列表
  const loadSavedOrders = () => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const parsedData = JSON.parse(stored)
        savedOrders.value = Array.isArray(parsedData) ? parsedData : []
        //console.log('加载存单数据:', savedOrders.value)
      } else {
        savedOrders.value = []
        //console.log('没有找到存单数据')
      }
    } catch (err) {
      console.error('加载存单失败:', err)
      error.value = '加载存单失败'
      ElMessage.error('加载存单失败')
      savedOrders.value = []
    }
  }

  // 保存存单列表到本地存储
  const saveSavedOrders = () => {
    try {
      const dataToSave = JSON.stringify(savedOrders.value)
      localStorage.setItem(STORAGE_KEY, dataToSave)
      //console.log('保存存单数据:', savedOrders.value)
    } catch (err) {
      console.error('保存存单失败:', err)
      error.value = '保存存单失败'
      ElMessage.error('保存存单失败')
    }
  }

  // 生成存单ID
  const generateOrderId = () => {
    return `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 保存存单
  const saveOrder = async (
    cartItems: CartItem[],
    totalAmount: number,
    totalItems: number,
    staffName: string,
    remark: string
  ): Promise<boolean> => {
    if (!remark.trim()) {
      ElMessage.warning('请填写存单备注')
      return false
    }

    if (!staffName.trim()) {
      ElMessage.warning('请选择点单员工')
      return false
    }

    if (cartItems.length === 0) {
      ElMessage.warning('购物车为空，无法存单')
      return false
    }

    try {
      isLoading.value = true
      
      const newOrder: SavedOrder = {
        id: generateOrderId(),
        orderTime: Date.now(),
        staffName: staffName.trim(),
        remark: remark.trim(),
        totalAmount,
        totalItems,
        cartItems: JSON.parse(JSON.stringify(cartItems)), // 深拷贝
        createdAt: Date.now()
      }

      //console.log('准备保存存单:', newOrder)
      savedOrders.value.unshift(newOrder) // 添加到开头
      saveSavedOrders()
      
      ElMessage.success('存单成功')
      return true
    } catch (err) {
      console.error('存单失败:', err)
      error.value = '存单失败'
      ElMessage.error('存单失败')
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 删除存单
  const deleteOrder = async (orderId: string): Promise<boolean> => {
    try {
      const result = await ElMessageBox.confirm(
        '确定要删除这个存单吗？删除后无法恢复。',
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      if (result === 'confirm') {
        const index = savedOrders.value.findIndex(order => order.id === orderId)
        if (index > -1) {
          savedOrders.value.splice(index, 1)
          saveSavedOrders()
          ElMessage.success('删除成功')
          return true
        } else {
          ElMessage.error('存单不存在')
          return false
        }
      }
      return false
    } catch (err) {
      // 用户取消删除
      return false
    }
  }

  // 恢复存单
  const restoreOrder = async (orderId: string): Promise<CartItem[] | null> => {
    try {
      const result = await ElMessageBox.confirm(
        '确定要恢复这个存单吗？当前购物车内容将被清空。',
        '恢复确认',
        {
          confirmButtonText: '确定恢复',
          cancelButtonText: '取消',
          type: 'info',
        }
      )

      if (result === 'confirm') {
        const orderIndex = savedOrders.value.findIndex(order => order.id === orderId)
        if (orderIndex > -1) {
          const order = savedOrders.value[orderIndex]
                //console.log('找到要恢复的存单:', order)
      //console.log('存单中的购物车数据:', order.cartItems)
          
          const cartItems = JSON.parse(JSON.stringify(order.cartItems)) // 深拷贝
          //console.log('深拷贝后的购物车数据:', cartItems)
          
          // 从存单列表中删除
          savedOrders.value.splice(orderIndex, 1)
          saveSavedOrders()
          
          //console.log('存单恢复成功，返回购物车数据')
          return cartItems
        } else {
          console.error('存单不存在，orderId:', orderId)
          ElMessage.error('存单不存在')
          return null
        }
      }
      return null
    } catch (err) {
      console.error('恢复存单过程中出错:', err)
      // 用户取消恢复
      return null
    }
  }

  // 获取存单详情
  const getOrderDetail = (orderId: string): SavedOrder | null => {
    return savedOrders.value.find(order => order.id === orderId) || null
  }

  // 获取存单列表项（用于列表显示）
  const getOrderListItems = (): SavedOrderListItem[] => {
    return savedOrders.value.map(order => ({
      id: order.id,
      orderTime: order.orderTime,
      staffName: order.staffName,
      remark: order.remark,
      totalAmount: order.totalAmount,
      totalItems: order.totalItems,
      createdAt: order.createdAt
    }))
  }

  // 格式化时间
  const formatOrderTime = (timestamp: number): string => {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // 格式化相对时间
  const formatRelativeTime = (timestamp: number): string => {
    const now = Date.now()
    const diff = now - timestamp
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`
    } else if (diff < 604800000) { // 1周内
      return `${Math.floor(diff / 86400000)}天前`
    } else {
      return formatOrderTime(timestamp)
    }
  }

  // 清空所有存单
  const clearAllOrders = async (): Promise<boolean> => {
    try {
      const result = await ElMessageBox.confirm(
        '确定要清空所有存单吗？此操作无法恢复。',
        '清空确认',
        {
          confirmButtonText: '确定清空',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      if (result === 'confirm') {
        savedOrders.value = []
        saveSavedOrders()
        ElMessage.success('已清空所有存单')
        return true
      }
      return false
    } catch (err) {
      // 用户取消清空
      return false
    }
  }

  // 清理所有数据（用于退出登录）
  const clearAllData = () => {
    savedOrders.value = []
    isLoading.value = false
    error.value = null
    localStorage.removeItem(STORAGE_KEY)
    // 重置初始化标志，以便下次登录时重新加载
    isInitialized = false
  }

  // 初始化
  if (!isInitialized) {
    loadSavedOrders()
    isInitialized = true
  }

  return {
    // 状态
    savedOrders,
    isLoading,
    error,
    orderCount,
    totalSavedAmount,
    
    // 方法
    saveOrder,
    deleteOrder,
    restoreOrder,
    getOrderDetail,
    getOrderListItems,
    formatOrderTime,
    formatRelativeTime,
    clearAllOrders,
    clearAllData,
    loadSavedOrders
  }
} 