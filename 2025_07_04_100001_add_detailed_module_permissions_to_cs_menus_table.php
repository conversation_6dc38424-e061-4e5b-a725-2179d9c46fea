<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * 为各个模块添加详细的操作权限
     */
    public function up(): void
    {
        $now = Carbon::now();
        
        // 批量插入所有权限菜单
        DB::table('cs_menus')->insert([

            // 预订管理模块 - 假设ID为2103
            [
                'pid' => 2103,
                'menu_name' => '查看',
                'menu_sign' => 'reservation_detail',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2103,
                'menu_name' => '预定开单',
                'menu_sign' => 'reservation_open',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2103,
                'menu_name' => '转台',
                'menu_sign' => 'reservation_change_table',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2103,
                'menu_name' => '修改',
                'menu_sign' => 'reservation_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2103,
                'menu_name' => '取消',
                'menu_sign' => 'reservation_cancel',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 会员管理模块 - 假设ID为2201
            [
                'pid' => 2201,
                'menu_name' => '新增',
                'menu_sign' => 'member_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2201,
                'menu_name' => '编辑',
                'menu_sign' => 'member_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2201,
                'menu_name' => '删除',
                'menu_sign' => 'member_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2201,
                'menu_name' => '状态切换',
                'menu_sign' => 'member_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2201,
                'menu_name' => '会员充值',
                'menu_sign' => 'member_recharge',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 会员等级管理 - 假设ID为2202
            [
                'pid' => 2202,
                'menu_name' => '新增',
                'menu_sign' => 'member_level_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2202,
                'menu_name' => '编辑',
                'menu_sign' => 'member_level_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2202,
                'menu_name' => '删除',
                'menu_sign' => 'member_level_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2202,
                'menu_name' => '排序',
                'menu_sign' => 'member_level_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 会员充值套餐管理 - 假设ID为2203
            [
                'pid' => 2203,
                'menu_name' => '新增',
                'menu_sign' => 'member_recharge_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2203,
                'menu_name' => '编辑',
                'menu_sign' => 'member_recharge_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2203,
                'menu_name' => '删除',
                'menu_sign' => 'member_recharge_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 会员余额明细 - 假设ID为2206
            [
                'pid' => 2206,
                'menu_name' => '详情',
                'menu_sign' => 'member_balance_detail',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 会员积分明细 - 假设ID为2207
            [
                'pid' => 2207,
                'menu_name' => '详情',
                'menu_sign' => 'member_points_detail',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 订单管理模块 - 假设ID为2301
            [
                'pid' => 2301,
                'menu_name' => '订单详情',
                'menu_sign' => 'order_table_detail',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2302,
                'menu_name' => '订单详情',
                'menu_sign' => 'order_retail_detail',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2302,
                'menu_name' => '取消订单',
                'menu_sign' => 'order_retail_cancel',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 优惠券管理 - 假设ID为2501
            [
                'pid' => 2501,
                'menu_name' => '新增',
                'menu_sign' => 'marketing_coupon_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2501,
                'menu_name' => '编辑',
                'menu_sign' => 'marketing_coupon_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2501,
                'menu_name' => '删除',
                'menu_sign' => 'marketing_coupon_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2501,
                'menu_name' => '状态切换',
                'menu_sign' => 'marketing_coupon_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 营销活动管理 - 假设ID为2502
            [
                'pid' => 2502,
                'menu_name' => '新增',
                'menu_sign' => 'marketing_activity_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2502,
                'menu_name' => '编辑',
                'menu_sign' => 'marketing_activity_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2502,
                'menu_name' => '删除',
                'menu_sign' => 'marketing_activity_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2502,
                'menu_name' => '状态切换',
                'menu_sign' => 'marketing_activity_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 库存管理模块 - 假设ID为2601
            [
                'pid' => 2601,
                'menu_name' => '库存调整',
                'menu_sign' => 'inventory_adjust',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2601,
                'menu_name' => '变动记录',
                'menu_sign' => 'inventory_record',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2601,
                'menu_name' => '导出库存',
                'menu_sign' => 'inventory_export',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 库存盘点 - 假设ID为2602
            [
                'pid' => 2602,
                'menu_name' => '盘点创建',
                'menu_sign' => 'inventory_check_create',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2602,
                'menu_name' => '盘点开始',
                'menu_sign' => 'inventory_check_start',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2602,
                'menu_name' => '盘点完成',
                'menu_sign' => 'inventory_check_complete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2602,
                'menu_name' => '盘点取消',
                'menu_sign' => 'inventory_check_cancel',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2602,
                'menu_name' => '盘点删除',
                'menu_sign' => 'inventory_check_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2602,
                'menu_name' => '盘点详情',
                'menu_sign' => 'inventory_check_detail',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2602,
                'menu_name' => '盘点执行',
                'menu_sign' => 'inventory_check_execute',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 8,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2602,
                'menu_name' => '盘点导出',
                'menu_sign' => 'inventory_check_export',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 9,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 库存变动记录 - 假设ID为2603
            [
                'pid' => 2603,
                'menu_name' => '查看详情',
                'menu_sign' => 'inventory_log_detail',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2603,
                'menu_name' => '导出记录',
                'menu_sign' => 'inventory_log_export',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2603,
                'menu_name' => '操作统计',
                'menu_sign' => 'inventory_log_statistics',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 消息管理模块 - 假设ID为2701
            [
                'pid' => 2701,
                'menu_name' => '查看',
                'menu_sign' => 'message_center_detail',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2701,
                'menu_name' => '标记已读',
                'menu_sign' => 'message_center_read',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 桌台分类管理 - 假设ID为2811
            [
                'pid' => 2811,
                'menu_name' => '新增',
                'menu_sign' => 'table_category_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2811,
                'menu_name' => '编辑',
                'menu_sign' => 'table_category_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2811,
                'menu_name' => '删除',
                'menu_sign' => 'table_category_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2811,
                'menu_name' => '状态切换',
                'menu_sign' => 'table_category_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2811,
                'menu_name' => '排序',
                'menu_sign' => 'table_category_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 桌台管理模块 - 假设ID为2812
            [
                'pid' => 2812,
                'menu_name' => '新增',
                'menu_sign' => 'table_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2812,
                'menu_name' => '编辑',
                'menu_sign' => 'table_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2812,
                'menu_name' => '删除',
                'menu_sign' => 'table_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2812,
                'menu_name' => '状态切换',
                'menu_sign' => 'table_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2812,
                'menu_name' => '排序',
                'menu_sign' => 'table_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 商品管理模块 - 需要先查找商品管理的菜单ID

            // 商品分类管理模块 - 假设ID为2821
            [
                'pid' => 2821,
                'menu_name' => '新增',
                'menu_sign' => 'goods_category_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2821,
                'menu_name' => '编辑',
                'menu_sign' => 'goods_category_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2821,
                'menu_name' => '删除',
                'menu_sign' => 'goods_category_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2821,
                'menu_name' => '状态切换',
                'menu_sign' => 'goods_category_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2821,
                'menu_name' => '排序',
                'menu_sign' => 'goods_category_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 假设商品管理的ID为2822（需要根据实际菜单ID调整）
            [
                'pid' => 2822,
                'menu_name' => '新增',
                'menu_sign' => 'goods_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2822,
                'menu_name' => '编辑',
                'menu_sign' => 'goods_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2822,
                'menu_name' => '删除',
                'menu_sign' => 'goods_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2822,
                'menu_name' => '状态切换',
                'menu_sign' => 'goods_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2822,
                'menu_name' => '排序',
                'menu_sign' => 'goods_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 假设套餐管理的ID为2823（需要根据实际菜单ID调整）
            [
                'pid' => 2823,
                'menu_name' => '新增',
                'menu_sign' => 'goods_package_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2823,
                'menu_name' => '编辑',
                'menu_sign' => 'goods_package_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2823,
                'menu_name' => '删除',
                'menu_sign' => 'goods_package_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2823,
                'menu_name' => '状态切换',
                'menu_sign' => 'goods_package_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2823,
                'menu_name' => '排序',
                'menu_sign' => 'goods_package_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 假设配品分类管理的ID为2824（需要根据实际菜单ID调整）
            [
                'pid' => 2824,
                'menu_name' => '新增',
                'menu_sign' => 'goods_material_category_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2824,
                'menu_name' => '编辑',
                'menu_sign' => 'goods_material_category_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2824,
                'menu_name' => '删除',
                'menu_sign' => 'goods_material_category_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2824,
                'menu_name' => '状态切换',
                'menu_sign' => 'goods_material_category_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2824,
                'menu_name' => '排序',
                'menu_sign' => 'goods_material_category_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 假设配品管理的ID为2825（需要根据实际菜单ID调整）
            [
                'pid' => 2825,
                'menu_name' => '新增',
                'menu_sign' => 'goods_material_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2825,
                'menu_name' => '编辑',
                'menu_sign' => 'goods_material_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2825,
                'menu_name' => '删除',
                'menu_sign' => 'goods_material_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2825,
                'menu_name' => '状态切换',
                'menu_sign' => 'goods_material_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2825,
                'menu_name' => '排序',
                'menu_sign' => 'goods_material_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 假设标签管理的ID为2826（需要根据实际菜单ID调整）
            [
                'pid' => 2826,
                'menu_name' => '新增',
                'menu_sign' => 'goods_tag_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2826,
                'menu_name' => '编辑',
                'menu_sign' => 'goods_tag_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2826,
                'menu_name' => '删除',
                'menu_sign' => 'goods_tag_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2826,
                'menu_name' => '状态切换',
                'menu_sign' => 'goods_tag_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2826,
                'menu_name' => '排序',
                'menu_sign' => 'goods_tag_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 假设规格分类管理的ID为2827（需要根据实际菜单ID调整）
            [
                'pid' => 2827,
                'menu_name' => '新增',
                'menu_sign' => 'goods_spec_category_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2827,
                'menu_name' => '编辑',
                'menu_sign' => 'goods_spec_category_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2827,
                'menu_name' => '删除',
                'menu_sign' => 'goods_spec_category_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2827,
                'menu_name' => '状态切换',
                'menu_sign' => 'goods_spec_category_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2827,
                'menu_name' => '排序',
                'menu_sign' => 'goods_spec_category_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 假设规格管理的ID为2828（需要根据实际菜单ID调整）
            [
                'pid' => 2828,
                'menu_name' => '新增',
                'menu_sign' => 'goods_spec_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2828,
                'menu_name' => '编辑',
                'menu_sign' => 'goods_spec_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2828,
                'menu_name' => '删除',
                'menu_sign' => 'goods_spec_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2828,
                'menu_name' => '状态切换',
                'menu_sign' => 'goods_spec_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2828,
                'menu_name' => '排序',
                'menu_sign' => 'goods_spec_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 假设收款方式管理的ID为2831（需要根据实际菜单ID调整）
            [
                'pid' => 2831,
                'menu_name' => '新增',
                'menu_sign' => 'payment_method_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2831,
                'menu_name' => '编辑',
                'menu_sign' => 'payment_method_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2831,
                'menu_name' => '删除',
                'menu_sign' => 'payment_method_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2831,
                'menu_name' => '状态切换',
                'menu_sign' => 'payment_method_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2831,
                'menu_name' => '排序',
                'menu_sign' => 'payment_method_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 假设桌台费规则管理的ID为2841（需要根据实际菜单ID调整）
            [
                'pid' => 2841,
                'menu_name' => '新增',
                'menu_sign' => 'table_billing_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2841,
                'menu_name' => '编辑',
                'menu_sign' => 'table_billing_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2841,
                'menu_name' => '删除',
                'menu_sign' => 'table_billing_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2841,
                'menu_name' => '状态切换',
                'menu_sign' => 'table_billing_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2841,
                'menu_name' => '排序',
                'menu_sign' => 'table_billing_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 假设服务费规则管理的ID为2842（需要根据实际菜单ID调整）
            [
                'pid' => 2842,
                'menu_name' => '新增',
                'menu_sign' => 'service_fee_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2842,
                'menu_name' => '编辑',
                'menu_sign' => 'service_fee_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2842,
                'menu_name' => '删除',
                'menu_sign' => 'service_fee_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2842,
                'menu_name' => '状态切换',
                'menu_sign' => 'service_fee_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2842,
                'menu_name' => '排序',
                'menu_sign' => 'service_fee_sort',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],


            // 打印机模板设置 - 假设ID为2851
            [
                'pid' => 2851,
                'menu_name' => '编辑',
                'menu_sign' => 'print_template_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2851,
                'menu_name' => '预览',
                'menu_sign' => 'print_template_preview',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2851,
                'menu_name' => '状态切换',
                'menu_sign' => 'print_template_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 打印机设置 - 假设ID为2852
            [
                'pid' => 2852,
                'menu_name' => '新增',
                'menu_sign' => 'printer_config_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2852,
                'menu_name' => '编辑',
                'menu_sign' => 'printer_config_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2852,
                'menu_name' => '删除',
                'menu_sign' => 'printer_config_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2852,
                'menu_name' => '测试连接',
                'menu_sign' => 'printer_config_test_connect',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2852,
                'menu_name' => '测试打印',
                'menu_sign' => 'printer_config_test_print',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2852,
                'menu_name' => '复制配置',
                'menu_sign' => 'printer_config_copy_config',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2852,
                'menu_name' => '状态切换',
                'menu_sign' => 'printer_config_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 管理员管理模块 (2901)
            [
                'pid' => 2901,
                'menu_name' => '新增',
                'menu_sign' => 'admin_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2901,
                'menu_name' => '编辑',
                'menu_sign' => 'admin_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2901,
                'menu_name' => '删除',
                'menu_sign' => 'admin_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2901,
                'menu_name' => '状态切换',
                'menu_sign' => 'admin_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 角色管理模块 (2902)
            [
                'pid' => 2902,
                'menu_name' => '新增',
                'menu_sign' => 'role_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2902,
                'menu_name' => '编辑',
                'menu_sign' => 'role_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2902,
                'menu_name' => '删除',
                'menu_sign' => 'role_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2902,
                'menu_name' => '权限分配',
                'menu_sign' => 'role_permission_assign',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],


        ]);
    }

    public function down(): void
    {
        // 回滚时删除所有添加的权限菜单
        DB::table('cs_menus')->whereIn('menu_sign', [
            
            // 预订管理模块
            'reservation_detail', 'reservation_open', 'reservation_change_table', 'reservation_edit', 'reservation_cancel',
            
            // 会员管理
            'member_add', 'member_edit', 'member_delete', 'member_status', 'member_recharge',
            
            // 会员等级管理
            'member_level_add', 'member_level_edit', 'member_level_delete', 'member_level_sort',
            
            // 会员充值套餐管理
            'member_recharge_add', 'member_recharge_edit', 'member_recharge_delete',
            
            // 会员余额明细
            'member_balance_detail',
            
            // 会员积分明细
            'member_points_detail',
            
            // 订单管理
            'order_table_detail', 'order_retail_detail', 'order_retail_cancel',
            
            // 优惠券管理
            'marketing_coupon_add', 'marketing_coupon_edit', 'marketing_coupon_delete', 'marketing_coupon_status',
            
            // 营销活动管理
            'marketing_activity_add', 'marketing_activity_edit', 'marketing_activity_delete', 'marketing_activity_status',
            
            // 库存管理
            'inventory_adjust', 'inventory_record', 'inventory_export',
            
            // 库存盘点
            'inventory_check_create', 'inventory_check_start', 'inventory_check_complete', 
            'inventory_check_cancel', 'inventory_check_delete', 'inventory_check_detail', 
            'inventory_check_execute', 'inventory_check_export',
            
            // 库存变动记录
            'inventory_log_detail', 'inventory_log_export', 'inventory_log_statistics',
            
            // 消息管理
            'message_center_detail', 'message_center_read',
            
            // 桌台分类管理
            'table_category_add', 'table_category_edit', 'table_category_delete', 'table_category_status', 'table_category_sort',
            
            // 桌台管理
            'table_add', 'table_edit', 'table_delete', 'table_status', 'table_sort',
            
            // 商品分类管理
            'goods_category_add', 'goods_category_edit', 'goods_category_delete', 'goods_category_status', 'goods_category_sort',
            
            // 商品管理
            'goods_add', 'goods_edit', 'goods_delete', 'goods_status', 'goods_sort',
            
            // 套餐管理
            'goods_package_add', 'goods_package_edit', 'goods_package_delete', 'goods_package_status', 'goods_package_sort',
            
            // 配品分类管理
            'goods_material_category_add', 'goods_material_category_edit', 'goods_material_category_delete', 'goods_material_category_status', 'goods_material_category_sort',
            
            // 配品管理
            'goods_material_add', 'goods_material_edit', 'goods_material_delete', 'goods_material_status', 'goods_material_sort',
            
            // 标签管理
            'goods_tag_add', 'goods_tag_edit', 'goods_tag_delete', 'goods_tag_status', 'goods_tag_sort',
            
            // 规格分类管理
            'goods_spec_category_add', 'goods_spec_category_edit', 'goods_spec_category_delete', 'goods_spec_category_status', 'goods_spec_category_sort',
            
            // 规格管理
            'goods_spec_add', 'goods_spec_edit', 'goods_spec_delete', 'goods_spec_status', 'goods_spec_sort',
            
            // 收款方式管理
            'payment_method_add', 'payment_method_edit', 'payment_method_delete', 'payment_method_status', 'payment_method_sort',
            
            // 桌台费规则管理
            'table_billing_add', 'table_billing_edit', 'table_billing_delete', 'table_billing_status', 'table_billing_sort',
            
            // 服务费规则管理
            'service_fee_add', 'service_fee_edit', 'service_fee_delete', 'service_fee_status', 'service_fee_sort',
            
            // 打印机模板设置
            'print_template_edit', 'print_template_preview', 'print_template_status',
            
            // 打印机设置
            'printer_config_add', 'printer_config_edit', 'printer_config_delete', 'printer_config_test_connect', 
            'printer_config_test_print', 'printer_config_copy_config', 'printer_config_status',

            // 管理员管理
            'admin_add', 'admin_edit', 'admin_delete', 'admin_status',
            
            // 角色管理
            'role_add', 'role_edit', 'role_delete', 'role_permission_assign',
        ])->delete();
    }
}; 